
# Django
DJANGO_SECRET=your_secret_key
DEBUG=True

# Database
DB_ENGINE=django.db.backends.postgresql
DB_NAME=amportal_db
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=localhost
DB_PORT=5432

# AWS
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=aj-amportal
AWS_STORAGE_BUCKET_NAME=my-first-bucket
AWS_S3_REGION_NAME=ap-south-1
AWS_S3_ENDPOINT_URL=http://localhost:4566

# Email
DEFAULT_FROM_EMAIL=<EMAIL>
EMAIL_HOST_USER=your_email_user
EMAIL_HOST_PASSWORD=your_email_password


EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=djibpeizldfyxqre
DJANGO_SECRET="fu@^7%=mrj!bb!-_k6z6kkh=t!zt(xdbue398$fi!_=j@+vwlu+ampersand"
"""
Management command to check OAuth token status and health.
"""

import os
import json
from datetime import datetime, timezone
from django.core.management.base import BaseCommand
from google.oauth2.credentials import Credentials
from accounts.mail import get_gmail_service, TOKEN_FILE


class Command(BaseCommand):
    help = 'Check OAuth token status and health'

    def add_arguments(self, parser):
        parser.add_argument(
            '--refresh',
            action='store_true',
            help='Attempt to refresh the token if it\'s expired',
        )

    def handle(self, *args, **options):
        if not os.path.exists(TOKEN_FILE):
            self.stdout.write(
                self.style.ERROR('No OAuth token file found. Run: python manage.py setup_oauth')
            )
            return

        try:
            # Load and check token
            with open(TOKEN_FILE, 'r') as f:
                token_data = json.load(f)
            
            creds = Credentials.from_authorized_user_file(TOKEN_FILE)
            
            self.stdout.write("OAuth Token Status:")
            self.stdout.write("=" * 50)
            
            # Check expiry
            if creds.expiry:
                expiry_time = creds.expiry.replace(tzinfo=timezone.utc)
                now = datetime.now(timezone.utc)
                time_until_expiry = expiry_time - now
                
                if time_until_expiry.total_seconds() > 0:
                    self.stdout.write(
                        self.style.SUCCESS(f"✓ Token is valid")
                    )
                    self.stdout.write(f"  Expires: {expiry_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
                    self.stdout.write(f"  Time remaining: {time_until_expiry}")
                else:
                    self.stdout.write(
                        self.style.WARNING(f"⚠ Token is expired")
                    )
                    self.stdout.write(f"  Expired: {expiry_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
            else:
                self.stdout.write(
                    self.style.WARNING("⚠ No expiry information available")
                )
            
            # Check refresh token
            if creds.refresh_token:
                self.stdout.write(
                    self.style.SUCCESS("✓ Refresh token available")
                )
            else:
                self.stdout.write(
                    self.style.ERROR("✗ No refresh token available")
                )
                self.stdout.write("  You may need to re-authorize the application")
            
            # Check scopes
            if hasattr(creds, 'scopes') and creds.scopes:
                self.stdout.write(f"Scopes: {', '.join(creds.scopes)}")
            
            # Test refresh if requested
            if options['refresh']:
                self.stdout.write("\nAttempting to refresh token...")
                try:
                    service = get_gmail_service()
                    self.stdout.write(
                        self.style.SUCCESS("✓ Token refresh successful")
                    )
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"✗ Token refresh failed: {str(e)}")
                    )
                    self.stdout.write("  Run: python manage.py setup_oauth")
            
            # Test email sending capability
            self.stdout.write("\nTesting email service...")
            try:
                service = get_gmail_service()
                self.stdout.write(
                    self.style.SUCCESS("✓ Gmail service is accessible")
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"✗ Gmail service error: {str(e)}")
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error checking OAuth status: {str(e)}")
            )

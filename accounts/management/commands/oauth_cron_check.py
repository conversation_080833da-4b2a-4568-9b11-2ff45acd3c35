"""
Management command for automated OAuth token checking and refreshing.
Designed to be run via cron job for production monitoring.
"""

import os
import json
import logging
from datetime import datetime, timezone, timedelta
from django.core.management.base import BaseCommand
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from accounts.mail import get_gmail_service, TOKEN_FILE
from django.core.mail import send_mail as django_send_mail
from django.conf import settings

logger = logging.getLogger('accounts.mail')


class Command(BaseCommand):
    help = 'Automated OAuth refresh token health check for cron jobs'

    def add_arguments(self, parser):
        parser.add_argument(
            '--refresh-threshold-hours',
            type=int,
            default=24,
            help='Legacy parameter - now tests refresh token health directly (default: 24)',
        )
        parser.add_argument(
            '--alert-email',
            type=str,
            help='Email address to send alerts to if token refresh fails',
        )
        parser.add_argument(
            '--quiet',
            action='store_true',
            help='Only output errors and warnings',
        )

    def handle(self, *args, **options):
        threshold_hours = options['refresh_threshold_hours']
        alert_email = options['alert_email']
        quiet = options['quiet']
        
        if not quiet:
            self.stdout.write(f"OAuth Token Cron Check - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.stdout.write("=" * 60)

        # Check if token file exists
        if not os.path.exists(TOKEN_FILE):
            error_msg = "OAuth token file not found. OAuth email system is not set up."
            self.stdout.write(self.style.ERROR(f"ERROR: {error_msg}"))
            logger.error(error_msg)
            self._send_alert(alert_email, "OAuth Setup Required", error_msg)
            return

        try:
            # Load credentials
            creds = Credentials.from_authorized_user_file(TOKEN_FILE)
            
            if not creds:
                error_msg = "Failed to load OAuth credentials from token file."
                self.stdout.write(self.style.ERROR(f"ERROR: {error_msg}"))
                logger.error(error_msg)
                self._send_alert(alert_email, "OAuth Credentials Error", error_msg)
                return

            # Test refresh token health by attempting a refresh
            if not quiet:
                self.stdout.write("Testing refresh token health...")

            refresh_success = self._test_refresh_token(creds, quiet)

            if not refresh_success:
                error_msg = "Refresh token is invalid or expired. Manual intervention required."
                self.stdout.write(self.style.ERROR(f"ERROR: {error_msg}"))
                logger.error(error_msg)
                self._send_alert(alert_email, "OAuth Refresh Token Expired",
                               f"{error_msg} Please run: python manage.py setup_oauth")
                return
            else:
                if not quiet:
                    self.stdout.write(self.style.SUCCESS("Refresh token is healthy"))
                logger.info("Refresh token successfully tested")

            # Also check access token expiry for informational purposes
            if creds.expiry:
                expiry_time = creds.expiry.replace(tzinfo=timezone.utc)
                now = datetime.now(timezone.utc)
                time_until_expiry = expiry_time - now
                hours_until_expiry = time_until_expiry.total_seconds() / 3600

                if not quiet:
                    self.stdout.write(f"Access token expires: {expiry_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
                    self.stdout.write(f"Hours until access token expiry: {hours_until_expiry:.1f}")

                if hours_until_expiry <= 1:  # Only refresh if very close to expiry
                    if not quiet:
                        self.stdout.write("Access token expires soon, refreshing...")
                    self._refresh_token(creds, quiet)
            else:
                if not quiet:
                    self.stdout.write("No access token expiry information available")

            # Test email service
            try:
                service = get_gmail_service()
                if not quiet:
                    self.stdout.write(self.style.SUCCESS("Gmail service test: PASSED"))
                logger.info("Gmail service accessibility confirmed")
            except Exception as e:
                error_msg = f"Gmail service test failed: {str(e)}"
                self.stdout.write(self.style.ERROR(f"ERROR: {error_msg}"))
                logger.error(error_msg)
                self._send_alert(alert_email, "Gmail Service Error", error_msg)

        except Exception as e:
            error_msg = f"Unexpected error during OAuth check: {str(e)}"
            self.stdout.write(self.style.ERROR(f"ERROR: {error_msg}"))
            logger.error(error_msg)
            self._send_alert(alert_email, "OAuth Cron Check Error", error_msg)

    def _test_refresh_token(self, creds, quiet=False):
        """
        Test if the refresh token is still valid by attempting to refresh.
        This is the primary way to check refresh token health since Google
        doesn't always provide expiry info for refresh tokens.
        Returns True if refresh token is healthy, False otherwise.
        """
        try:
            if not creds.refresh_token:
                if not quiet:
                    self.stdout.write(self.style.ERROR("No refresh token available"))
                return False

            # Create a copy of credentials to test refresh without affecting the original
            test_creds = Credentials.from_authorized_user_info({
                'client_id': creds.client_id,
                'client_secret': creds.client_secret,
                'refresh_token': creds.refresh_token,
                'token_uri': creds.token_uri,
            })

            # Attempt to refresh
            test_creds.refresh(Request())

            # If we get here, refresh token is valid
            # Save the refreshed credentials
            with open(TOKEN_FILE, 'w') as token:
                token.write(test_creds.to_json())

            if not quiet:
                self.stdout.write("Refresh token test successful, credentials updated")
            return True

        except Exception as e:
            if not quiet:
                self.stdout.write(self.style.ERROR(f"Refresh token test failed: {str(e)}"))
            logger.error(f"Refresh token test failed: {str(e)}")
            return False

    def _refresh_token(self, creds, quiet=False):
        """
        Attempt to refresh the OAuth token.
        Returns True if successful, False otherwise.
        """
        try:
            if creds.refresh_token:
                # Refresh the token
                creds.refresh(Request())

                # Save the refreshed credentials
                with open(TOKEN_FILE, 'w') as token:
                    token.write(creds.to_json())

                if not quiet:
                    self.stdout.write("Token refreshed and saved successfully")
                return True
            else:
                if not quiet:
                    self.stdout.write(self.style.ERROR("No refresh token available"))
                return False

        except Exception as e:
            if not quiet:
                self.stdout.write(self.style.ERROR(f"Token refresh failed: {str(e)}"))
            logger.error(f"Token refresh failed: {str(e)}")
            return False

    def _send_alert(self, alert_email, subject, message):
        """
        Send alert email if alert_email is provided.
        Uses Django's SMTP fallback since OAuth might be broken.
        """
        if not alert_email:
            return
            
        try:
            full_message = f"""
OAuth Email System Alert

{message}

Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Server: {os.uname().nodename if hasattr(os, 'uname') else 'Unknown'}

Please check the OAuth email system and run diagnostics:
- python manage.py check_oauth_status
- python manage.py setup_oauth (if refresh failed)

This is an automated alert from the OAuth monitoring system.
"""
            
            django_send_mail(
                subject=f"[Ampersand Capital] {subject}",
                message=full_message,
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
                recipient_list=[alert_email],
                fail_silently=False,
            )
            
            logger.info(f"Alert email sent to {alert_email}")
            
        except Exception as e:
            logger.error(f"Failed to send alert email: {str(e)}")
            self.stdout.write(self.style.ERROR(f"Failed to send alert email: {str(e)}"))

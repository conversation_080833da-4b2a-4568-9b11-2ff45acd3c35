"""
Management command to set up OAuth authentication for Gmail API.
"""

import os
from django.core.management.base import BaseCommand
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow

# If modifying these scopes, delete the file token.json.
SCOPES = ["https://www.googleapis.com/auth/gmail.send"]


class Command(BaseCommand):
    help = 'Set up OAuth authentication for Gmail API'

    def add_arguments(self, parser):
        parser.add_argument(
            '--credentials-file',
            type=str,
            default='accounts/credentials.json',
            help='Path to the OAuth credentials JSON file',
        )

    def handle(self, *args, **options):
        credentials_file = options['credentials_file']
        token_file = 'accounts/token.json'
        
        # Check if credentials file exists
        if not os.path.exists(credentials_file):
            self.stdout.write(
                self.style.ERROR(f'Credentials file not found: {credentials_file}')
            )
            self.stdout.write(
                'Please download your OAuth credentials JSON file from Google Cloud Console and place it at:'
            )
            self.stdout.write(f'  {credentials_file}')
            self.stdout.write('')
            self.stdout.write('Steps to get OAuth credentials:')
            self.stdout.write('1. Go to Google Cloud Console (https://console.cloud.google.com/)')
            self.stdout.write('2. Create a new project or select existing project')
            self.stdout.write('3. Enable Gmail API')
            self.stdout.write('4. Go to Credentials > Create Credentials > OAuth client ID')
            self.stdout.write('5. Choose "Desktop application"')
            self.stdout.write('6. Download the JSON file and save it as credentials.json')
            return

        creds = None
        
        # The file token.json stores the user's access and refresh tokens.
        if os.path.exists(token_file):
            creds = Credentials.from_authorized_user_file(token_file, SCOPES)
            
        # If there are no (valid) credentials available, let the user log in.
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                    self.stdout.write(
                        self.style.SUCCESS('Successfully refreshed existing OAuth token!')
                    )
                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f'Failed to refresh token: {str(e)}')
                    )
                    self.stdout.write('Creating new OAuth flow...')
                    creds = None
            
            if not creds or not creds.valid:
                flow = InstalledAppFlow.from_client_secrets_file(credentials_file, SCOPES)
                creds = flow.run_local_server(port=0)
                self.stdout.write(
                    self.style.SUCCESS('Successfully completed OAuth flow!')
                )
            
            # Save the credentials for the next run
            with open(token_file, 'w') as token:
                token.write(creds.to_json())
                
        self.stdout.write(
            self.style.SUCCESS(f'OAuth setup complete! Token saved to {token_file}')
        )
        
        # Test the credentials
        try:
            from googleapiclient.discovery import build
            service = build('gmail', 'v1', credentials=creds)
            profile = service.users().getProfile(userId='me').execute()
            email_address = profile.get('emailAddress', 'Unknown')
            
            self.stdout.write(f'Authenticated as: {email_address}')
            self.stdout.write('OAuth authentication is working correctly!')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to test OAuth credentials: {str(e)}')
            )

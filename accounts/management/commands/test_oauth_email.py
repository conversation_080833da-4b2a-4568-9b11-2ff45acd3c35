"""
Management command to test OAuth email functionality.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from accounts.mail import send_mail, get_gmail_service
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Test OAuth email functionality'

    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            help='Email address to send test email to',
        )
        parser.add_argument(
            '--type',
            type=str,
            choices=['welcome', 'reset'],
            default='welcome',
            help='Type of email to send (welcome or reset)',
        )
        parser.add_argument(
            '--check-auth',
            action='store_true',
            help='Only check OAuth authentication without sending email',
        )

    def handle(self, *args, **options):
        if options['check_auth']:
            self.check_oauth_auth()
            return

        email = options.get('email')
        if not email:
            self.stdout.write(
                self.style.ERROR('Please provide an email address with --email')
            )
            return

        email_type = options['type']
        
        # Try to find existing user or create a test user
        try:
            user = User.objects.get(email=email)
            self.stdout.write(f"Found existing user: {user.username}")
        except User.DoesNotExist:
            # Create a temporary test user
            user = User(
                username=f"test_{email.split('@')[0]}",
                email=email,
                first_name="Test",
                last_name="User",
                role="client"
            )
            # Don't save to database, just use for email testing
            self.stdout.write(f"Created temporary test user for email: {email}")

        self.stdout.write(f"Sending {email_type} email to {email}...")
        
        try:
            success = send_mail(email_type, user)
            if success:
                self.stdout.write(
                    self.style.SUCCESS(f'Successfully sent {email_type} email to {email}')
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f'Failed to send {email_type} email to {email}')
                )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error sending email: {str(e)}')
            )

    def check_oauth_auth(self):
        """Check if OAuth authentication is working."""
        self.stdout.write("Checking OAuth authentication...")

        try:
            service = get_gmail_service()

            # Just check if we can build the service - we don't need profile access for sending emails
            self.stdout.write(
                self.style.SUCCESS(f'OAuth authentication successful!')
            )
            self.stdout.write('Gmail service initialized successfully.')
            self.stdout.write('Ready to send emails via OAuth.')

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'OAuth authentication failed: {str(e)}')
            )
            self.stdout.write(
                'Please check your token.json file and ensure it contains valid OAuth credentials.'
            )

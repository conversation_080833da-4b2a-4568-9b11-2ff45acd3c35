from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenRefreshView
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from .serializers import LoginSerializer
from django.conf import settings
from .models import User
from .serializers import PasswordResetRequestSerializer, SetNewPasswordSerializer
from rest_framework import generics, status
from .mail import send_mail, get_gmail_service
from .permissions import IsAdmin
from rest_framework.permissions import IsAuthenticated


from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator

# from django.shortcuts import render


@method_decorator(ratelimit(key='user_or_ip', rate='10/m', block=True), name='post')
class LoginView(APIView):

    # def get(self, request):
    #     return render(request, 'login.html')
    
    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            username = serializer.validated_data['username']
            password = serializer.validated_data['password']
            
            user = authenticate(username=username, password=password)
            
            if user is not None:
                refresh = RefreshToken.for_user(user)
                response_data = {
                    'access_token': str(refresh.access_token),
                    'refresh_token': str(refresh),
                    'user_id': user.id,
                    'username': user.username,
                    'role': user.role,
                    'dashboard_url': self.get_dashboard_url(user.role)
                }
                
                response = Response(response_data, status=status.HTTP_200_OK)
                
                # Set JWT tokens in cookies with specific paths
                response.set_cookie(
                    'access_token',
                    str(refresh.access_token),
                    httponly=True,
                    samesite='Strict',
                    path='/api/',  # Only send to API endpoints
                    max_age=60 * 60 * 24  # 1 day
                )
                response.set_cookie(
                    'refresh_token',
                    str(refresh),
                    httponly=True,
                    samesite='Strict',
                    path='/api/token/refresh/',  # Only send to refresh endpoint
                    max_age=60 * 60 * 24 * 7  # 7 days
                )

                response.set_cookie(
                    'user_id',
                    user.id,
                    httponly=True,
                    samesite='Strict',
                    path='/api/',  # Only send to API endpoints
                    max_age=60 * 60 * 24  # 1 day
                )
                
                return response
            else:
                return Response({
                    'error': 'Invalid credentials'
                }, status=status.HTTP_401_UNAUTHORIZED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def get_dashboard_url(self, role):
        dashboard_urls = {
            'admin': '/admin/dashboard/',
            'staff': '/staff/dashboard/',
            'client': '/client/dashboard/'
        }
        return dashboard_urls.get(role, '/')

@method_decorator(ratelimit(key='user_or_ip', rate='10/m', block=True), name='post')
class PasswordResetRequestView(generics.GenericAPIView):
    """
    View to request a password reset link.
    """
    serializer_class = PasswordResetRequestSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        email = serializer.validated_data['email']
        
        try:
            user = User.objects.get(email__iexact=email)
        except User.DoesNotExist:
            # Don't reveal that the user doesn't exist.
            # Still return a success response for security.
            return Response({'detail': 'If an account with this email exists, a password reset link has been sent.'}, status=status.HTTP_200_OK)
        send_mail('reset', user)
        
        return Response({'detail': 'If an account with this email exists, a password reset link has been sent.'}, status=status.HTTP_200_OK)

@method_decorator(ratelimit(key='user_or_ip', rate='10/m', block=True), name='patch')
class SetNewPasswordView(generics.GenericAPIView):
    """
    View to set a new password using the token from the reset link.
    """
    serializer_class = SetNewPasswordSerializer

    def patch(self, request, *args, **kwargs):
        serializer_data = request.data.copy()
        serializer_data['uidb64'] = self.kwargs.get('uidb64')
        serializer_data['token'] = self.kwargs.get('token')
        serializer = self.get_serializer(data=serializer_data)
        serializer.is_valid(raise_exception=True)
        return Response({'detail': 'Password has been reset successfully.'}, status=status.HTTP_200_OK)


class EmailHealthCheckView(APIView):
    """
    Health check endpoint for monitoring OAuth email system status.
    """
    permission_classes = [IsAuthenticated, IsAdmin]

    def get(self, request):
        """
        Check OAuth email system health.
        Returns status of OAuth tokens and email service availability.
        """
        try:
            # Test Gmail service
            service = get_gmail_service()

            # If we get here, OAuth is working
            return Response({
                'status': 'healthy',
                'oauth_status': 'active',
                'service': 'gmail_api',
                'message': 'OAuth email system is operational'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            # OAuth failed, check if SMTP fallback is available
            smtp_available = bool(
                getattr(settings, 'EMAIL_HOST_USER', None) and
                getattr(settings, 'EMAIL_HOST_PASSWORD', None)
            )

            return Response({
                'status': 'degraded' if smtp_available else 'unhealthy',
                'oauth_status': 'failed',
                'oauth_error': str(e),
                'smtp_fallback': 'available' if smtp_available else 'unavailable',
                'service': 'smtp_fallback' if smtp_available else 'none',
                'message': 'OAuth failed, using SMTP fallback' if smtp_available else 'Email service unavailable'
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE if not smtp_available else status.HTTP_200_OK)

class LogoutView(APIView):
    """
    Logout view that clears JWT tokens from cookies.
    """
    
    def post(self, request):
        response = Response({'message': 'Successfully logged out'}, status=status.HTTP_200_OK)
        
        # Clear all authentication cookies
        response.delete_cookie('access_token', path='/api/')
        response.delete_cookie('refresh_token', path='/api/token/refresh/')
        response.delete_cookie('user_id', path='/api/')
        
        return response

class CustomTokenRefreshView(TokenRefreshView):
    """
    Custom token refresh view that works with HTTP-only cookies.
    """
    
    def post(self, request, *args, **kwargs):
        # Get refresh token from cookie
        refresh_token = request.COOKIES.get('refresh_token')
        
        if not refresh_token:
            return Response(
                {'error': 'Refresh token not found'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Add the refresh token to request data
        request.data['refresh'] = refresh_token
        
        try:
            response = super().post(request, *args, **kwargs)
            
            if response.status_code == 200:
                # Get the new tokens from the response
                access_token = response.data.get('access')
                new_refresh_token = response.data.get('refresh')
                
                # Update cookies with new tokens and proper paths
                response.set_cookie(
                    'access_token',
                    access_token,
                    httponly=True,
                    samesite='Strict',
                    path='/api/',  # Only send to API endpoints
                    max_age=60 * 60  # 1 hour
                )
                
                if new_refresh_token:  # Only if rotation is enabled
                    response.set_cookie(
                        'refresh_token',
                        new_refresh_token,
                        httponly=True,
                        samesite='Strict',
                        path='/api/token/refresh/',  # Only send to refresh endpoint
                        max_age=60 * 60 * 4  # 4 hours
                    )
                
                # Return just the access token in response body
                response.data = {'access': access_token}
            
            return response
            
        except (InvalidToken, TokenError) as e:
            return Response(
                {'error': 'Invalid refresh token'}, 
                status=status.HTTP_401_UNAUTHORIZED
            )

from rest_framework.permissions import IsAuthenticated

class SessionValidationView(APIView):
    """
    View to validate current session and return user information.
    This allows the frontend to check authentication status without storing tokens.
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        if user.is_authenticated:
            return Response({
                'id': user.id,
                'username': user.username,
                'role': user.role,
            })
        return Response({'error': 'Invalid session'}, status=401)
from django.urls import path
from .views import (
    DashboardStatsView, 
    StaffListView, 
    ClientListView,
    RegisterStaff,
    AdminDashboardView,
    SearchStaffData,
    SearchClientData
)

urlpatterns = [
    path('dashboard/', AdminDashboardView.as_view(), name='admin_dashboard'),
    path('dashboard-stats/', DashboardStatsView.as_view(), name='dashboard_stats'),
    path('staff-list/', StaffListView.as_view(), name='staff_list'),
    path('client-list/', ClientListView.as_view(), name='client_list'),
    path('register-staff/', RegisterStaff.as_view(), name='register_staff'),
    path('search-staff/', SearchStaffData.as_view(), name='search_staff'),
    path('search-clients/', SearchClientData.as_view(), name='search_clients'), 
]
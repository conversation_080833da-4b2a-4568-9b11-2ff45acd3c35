from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from accounts.permissions import IsAdmin
from accounts.models import Folio, User, Staff, Client
from accounts.serializers import StaffSerializer, StaffListSerializer, ClientSerializer, ClientListSerializer, StaffSearchSerializer, ClientSearchSerializer
from django.db.models import Q
from accounts.mail import send_mail
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator

@method_decorator(ratelimit(key='user_or_ip', rate='10/m', block=True), name='dispatch')
class RegisterStaff(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]
    
    def post(self, request):
        serializer = StaffSerializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            client_instance = serializer.save()
            user = client_instance.user
            send_mail('welcome', user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
class AdminDashboardView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    def get(self, request):
        return render(request, 'admin/admin_dashboard.html')

class DashboardStatsView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    def get(self, request):
        staff_count = User.objects.filter(role='staff').count()
        client_count = User.objects.filter(role='client').count()
        folio_count = Folio.objects.count()
        
        return Response({
            'staffCount': staff_count,
            'clientCount': client_count,
            'folioCount': folio_count,
            'adminName': request.user.get_full_name() or request.user.username
        })

class StaffListView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    def get(self, request):
        staff = Staff.objects.select_related('user').all()
        serializer = StaffListSerializer(staff, many=True, context={'request': request})
        return Response({'staff': serializer.data})

class ClientListView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    def get(self, request):
        clients = Client.objects.select_related('user').all()
        serializer = ClientListSerializer(clients, many=True, context={'request': request})
        return Response({'clients': serializer.data})

@method_decorator(ratelimit(key='ip', rate='10/m', block=True), name='dispatch')
class SearchStaffData(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    def get(self, request):
        query = request.query_params.get('query', '').strip()

        # If no query is provided, return all staff
        if not query:
            staffs = Staff.objects.all()
        else:
            # Filter staff based on the search query
            staffs = Staff.objects.filter(
                Q(user__username__icontains=query) |
                Q(user__first_name__icontains=query) |
                Q(user__last_name__icontains=query)
            ).distinct()

        if not staffs.exists():
            return Response({"detail": "No staff found matching the criteria."}, status=status.HTTP_404_NOT_FOUND)

        results = []
        for staff in staffs:
            user = staff.user

            staff_data = {
                'username': user.username, #username is the staffid
                'first_name': user.first_name,
                'last_name': user.last_name,
                'email': user.email,
            }
            results.append(staff_data)

        serializer = StaffSearchSerializer(results, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

@method_decorator(ratelimit(key='user_or_ip', rate='30/m', block=True), name='dispatch')
class SearchClientData(APIView):
    permission_classes = [IsAuthenticated, IsAdmin] # Example permission

    def get(self, request):
        username_query = request.query_params.get('username', None)
        name_query = request.query_params.get('name_query', None)
        folio_number_query = request.query_params.get('folio_number_query', None)

        # if not name_query and not folio_number_query and not username_query:
        #     return Response(
        #         {"detail": "At least 'name' or 'folio_number' or 'username' must be provided for search."},
        #         status=status.HTTP_400_BAD_REQUEST
        #     )

        clients = Client.objects.all()

        if name_query:
            clients = clients.filter(
                Q(user__first_name__icontains=name_query) | Q(user__last_name__icontains=name_query)
            )
             

        if folio_number_query:
            try:
                folio_number_query = int(folio_number_query)
                clients = clients.filter(folios__folio_number=folio_number_query).distinct()
            except ValueError:
                return Response(
                    {"detail": "Folio number must be an integer."},
                    status=status.HTTP_400_BAD_REQUEST
                )

        if username_query:
            clients = clients.filter(user__username__icontains=username_query)


        if not clients.exists():
            return Response({"detail": "No clients found matching the criteria."}, status=status.HTTP_404_NOT_FOUND)

        results = []
        for client in clients:
            user = client.user
            folios = client.folios.all().values_list('folio_number', 'terminated')
            folio_data = {folio_number: terminated for folio_number, terminated in folios}
            
            client_data = {
                'username': user.username, #username is the clientid
                'first_name': user.first_name,
                'last_name': user.last_name,
                'email': user.email,
                'folio_numbers': folio_data
            }
            results.append(client_data)
        
        serializer = ClientSearchSerializer(results, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


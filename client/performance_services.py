from datetime import date
from dateutil.relativedelta import relativedelta
from decimal import Decimal

# Assuming these models are in your 'accounts' app
from accounts.models import FundNAVHistory, BenchmarkHistory, Folio, Client 

# You would keep this utility function, perhaps in performance_utils.py
def calculate_cagr(end_value, start_value, years):
    """Calculates Compound Annual Growth Rate."""
    if start_value is None or end_value is None or start_value == 0 or years <= 0:
        return 0.0
    
    # Convert Decimal to float for calculation
    start_value, end_value = float(start_value), float(end_value)
    
    return ((end_value / start_value) ** (1 / years)) - 1

def _get_value_for_date(model, target_date):
    """
    Finds the latest record on or before the target_date from a given model.
    This handles cases where the exact date doesn't exist (e.g., weekends).
    """
    record = model.objects.filter(date__lte=target_date).order_by('-date').first()
    if model == FundNAVHistory:
        return record.nav if record else None
    return record.value if record else None

### Requirement 1: Fund & Benchmark Column Chart ###

def get_overall_fund_performance_chart_data():
    """
    Generates performance data by querying the FundNAVHistory and BenchmarkHistory models.
    """
    # today = date.today()
    today = FundNAVHistory.objects.order_by('-date').first().date
    periods = {
        "1M": (today - relativedelta(months=1), today),
        "3M": (today - relativedelta(months=3), today),
        "6M": (today - relativedelta(months=6), today),
        "1 Year": (today - relativedelta(years=1), today),
        "3 Years": (today - relativedelta(years=3), today),
        "5 Years": (today - relativedelta(years=5), today),
    }

    chart_data = []

    for label, (start_date, end_date) in periods.items():
        num_years = (end_date - start_date).days / 365.25

        # --- Get Values from Database ---
        fund_start_val = _get_value_for_date(FundNAVHistory, start_date)
        fund_end_val = _get_value_for_date(FundNAVHistory, end_date)
        
        bench_start_val = _get_value_for_date(BenchmarkHistory, start_date)
        bench_end_val = _get_value_for_date(BenchmarkHistory, end_date)

        # Skip period if data is missing
        if any(v is None for v in [fund_start_val, fund_end_val, bench_start_val, bench_end_val]):
            continue

        # --- Performance Calculation ---
        if num_years > 1: # Use CAGR for periods over 1 year
            fund_performance = calculate_cagr(fund_end_val, fund_start_val, num_years)
            benchmark_performance = calculate_cagr(bench_end_val, bench_start_val, num_years)
        else: # Use Absolute Return for periods 1 year or less
            fund_performance = (fund_end_val / fund_start_val) - 1
            benchmark_performance = (bench_end_val / bench_start_val) - 1

        chart_data.append({
            "period": label,
            "fund_performance": round(float(fund_performance) * 100, 2),
            "benchmark_performance": round(float(benchmark_performance) * 100, 2),
        })
        
    # --- "Since Inception" Calculation ---
    inception_fund_record = FundNAVHistory.objects.order_by('date').first()
    if inception_fund_record:
        inception_date = inception_fund_record.date
        inception_years = (today - inception_date).days / 365.25

        fund_start_val = inception_fund_record.nav
        fund_end_val = _get_value_for_date(FundNAVHistory, today)
        
        bench_start_val = _get_value_for_date(BenchmarkHistory, inception_date)
        bench_end_val = _get_value_for_date(BenchmarkHistory, today)

        if all(v is not None for v in [fund_start_val, fund_end_val, bench_start_val, bench_end_val]):
            fund_inception_perf = calculate_cagr(fund_end_val, fund_start_val, inception_years)
            bench_inception_perf = calculate_cagr(bench_end_val, bench_start_val, inception_years)

            chart_data.append({
                "period": "Since Inception",
                "fund_performance": round(fund_inception_perf * 100, 2),
                "benchmark_performance": round(bench_inception_perf * 100, 2),
            })

    return chart_data


### Requirement 2: Client's Monthly NAV Line Chart ###

def get_client_nav_performance_charts(client_user):
    """
    Generates NAV performance line chart data for a client's active folios.
    The `nav_data` in the Folio model is assumed to be a JSON object like:
    {
        "2024-05-31": {"pre_tax_nav": 120.50, "post_tax_nav": 118.90},
        "2024-06-30": {"pre_tax_nav": 125.75, "post_tax_nav": 124.10},
        ...
    }
    """
    try:
        client = Client.objects.get(user=client_user)
    except Client.DoesNotExist:
        return [] # Return empty list if user is not a client

    active_folios = Folio.objects.filter(client=client, terminated=False)
    charts_data = []

    for folio in active_folios:
        nav_data = folio.nav_data
        if not nav_data or not isinstance(nav_data, dict):
            continue

        # Sort data by date (key)
        sorted_dates = sorted(nav_data.keys())

        labels = []
        pre_tax_values = []
        post_tax_values = []

        for date_str in sorted_dates:
            labels.append(date_str)
            month_data = nav_data[date_str]
            pre_tax_values.append(month_data.get('pre_tax_nav')) # Use .get() to handle missing keys gracefully
            post_tax_values.append(month_data.get('post_tax_nav'))

        charts_data.append({
            "folio_number": folio.folio_number,
            "labels": labels,
            "datasets": [
                {
                    "label": "Pre-Tax NAV",
                    "data": pre_tax_values,
                },
                {
                    "label": "Post-Tax NAV",
                    "data": post_tax_values,
                }
            ]
        })

    return charts_data
from datetime import date
from dateutil.relativedelta import relativedelta
import math

# --- Placeholder Data ---
# In a real application, you would fetch this from a database or a financial data API.
# The keys represent the date (end of month), and values are the NAV or Index value.
FUND_PERFORMANCE_HISTORY = {
    (date.today() - relativedelta(months=i)).strftime('%Y-%m-01'): 100 + i * 2 + (i % 5) for i in range(60)
}
BENCHMARK_HISTORY = {
    (date.today() - relativedelta(months=i)).strftime('%Y-%m-01'): 100 + i * 1.5 for i in range(60)
}
# --- End Placeholder Data ---


def calculate_cagr(end_value, start_value, years):
    """Calculates Compound Annual Growth Rate."""
    if start_value == 0 or years <= 0:
        return 0.0
    return ((end_value / start_value) ** (1 / years)) - 1


def get_performance_for_period(data_history, start_date, end_date):
    """Gets the value from the history data for a specific date."""
    # This is a simple lookup. In reality, you'd find the closest date match.
    start_key = start_date.strftime('%Y-%m-01')
    end_key = end_date.strftime('%Y-%m-01')

    start_value = data_history.get(start_key, 0)
    end_value = data_history.get(end_key, 0)

    if start_value == 0 or end_value == 0:
        return 0.0 # Return 0 if data is missing

    return (end_value / start_value) - 1


def get_chart_data():
    """
    Generates the performance data for the specified time periods.
    """
    today = date.today()
    periods = {
        "1M": (today - relativedelta(months=1), today),
        "3M": (today - relativedelta(months=3), today),
        "6M": (today - relativedelta(months=6), today),
        "1 Year": (today - relativedelta(years=1), today),
        "3 Years": (today - relativedelta(years=3), today),
        "5 Years": (today - relativedelta(years=5), today),
    }

    chart_data = []

    for label, (start_date, end_date) in periods.items():
        num_years = (end_date - start_date).days / 365.25

        # --- Fund Performance Calculation ---
        fund_return = get_performance_for_period(FUND_PERFORMANCE_HISTORY, start_date, end_date)
        if num_years > 1:
            start_val = FUND_PERFORMANCE_HISTORY.get(start_date.strftime('%Y-%m-01'), 0)
            end_val = FUND_PERFORMANCE_HISTORY.get(end_date.strftime('%Y-%m-01'), 0)
            fund_performance = calculate_cagr(end_val, start_val, num_years)
        else:
            fund_performance = fund_return

        # --- Benchmark Performance Calculation ---
        benchmark_return = get_performance_for_period(BENCHMARK_HISTORY, start_date, end_date)
        if num_years > 1:
            start_val = BENCHMARK_HISTORY.get(start_date.strftime('%Y-%m-01'), 0)
            end_val = BENCHMARK_HISTORY.get(end_date.strftime('%Y-%m-01'), 0)
            benchmark_performance = calculate_cagr(end_val, start_val, num_years)
        else:
            benchmark_performance = benchmark_return

        chart_data.append({
            "period": label,
            "fund_performance": round(fund_performance * 100, 2),
            "benchmark_performance": round(benchmark_performance * 100, 2),
        })
        
    # --- "Since Inception" Calculation ---
    # (Assuming inception was 5 years ago for this example)
    inception_date = today - relativedelta(years=5)
    inception_years = (today - inception_date).days / 365.25
    
    fund_start_val = FUND_PERFORMANCE_HISTORY.get(inception_date.strftime('%Y-%m-01'), 0)
    fund_end_val = FUND_PERFORMANCE_HISTORY.get(today.strftime('%Y-%m-01'), 0)
    fund_inception_perf = calculate_cagr(fund_end_val, fund_start_val, inception_years)

    bench_start_val = BENCHMARK_HISTORY.get(inception_date.strftime('%Y-%m-01'), 0)
    bench_end_val = BENCHMARK_HISTORY.get(today.strftime('%Y-%m-01'), 0)
    bench_inception_perf = calculate_cagr(bench_end_val, bench_start_val, inception_years)

    chart_data.append({
        "period": "Since Inception",
        "fund_performance": round(fund_inception_perf * 100, 2),
        "benchmark_performance": round(bench_inception_perf * 100, 2),
    })


    return chart_data
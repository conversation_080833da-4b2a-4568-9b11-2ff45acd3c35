from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from accounts.permissions import IsClient
from accounts.models import Folio, Document, Client
from accounts.serializers import DashboardFolioSerializer, DashboardDocumentSerializer
from staff.views import DownloadDocumentView
from .performance_services import get_overall_fund_performance_chart_data, get_client_nav_performance_charts
from django.db.models import Q
import calendar
from datetime import date
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator


class ClientDashboardView(APIView):
    """
    Provides filtered and real-time data for the client dashboard, including:
    - Active folios for the client.
    - Client and Company documents for the CURRENT MONTH.
    - Fund vs. Benchmark performance data (Column Chart).
    - Client's monthly NAV performance data (Line Charts).
    """
    permission_classes = [IsAuthenticated, IsClient]

    def get(self, request):
        try:
            # Ensure the user has a client profile
            client = request.user.client
        except Client.DoesNotExist:
            return Response({"error": "Client profile not found for this user."}, status=404)

        # --- Data Fetching and Filtering ---

        current_date = date.today()
        current_year = current_date.year
        current_month = current_date.month

        # 1. Get ONLY active folios for the client
        active_folios = Folio.objects.filter(client=client, terminated=False)
        folio_serializer = DashboardFolioSerializer(active_folios, many=True, context={'request': request})

        # 2. Get client documents for the current month, linked to active folios
        client_documents = Document.objects.filter(
            folio__in=active_folios,
            year=current_year,
            month=current_month
        )
        client_doc_serializer = DashboardDocumentSerializer(client_documents, many=True, context={'request': request})
        
        # 3. Get company-level documents for the CURRENT MONTH
        company_documents = Document.objects.filter(
            document_type='Company',
            year=current_year,
            month=current_month
        )
        company_doc_serializer = DashboardDocumentSerializer(company_documents, many=True, context={'request': request})

        # 4. Get performance data for the column chart from the new service
        fund_performance_data = get_overall_fund_performance_chart_data()

        # 5. Get client-specific NAV performance for the line charts
        client_nav_charts = get_client_nav_performance_charts(request.user)


        # --- Construct the Final Response Payload ---

        response_data = {
            "message": f"Welcome, {request.user.first_name or request.user.username}",
            "active_folios": folio_serializer.data,
            "client_documents_current_month": client_doc_serializer.data,
            "company_documents_current_month": company_doc_serializer.data,
            "performance_chart_data": fund_performance_data, # This data is already a list of dicts
            "client_line_charts_data": client_nav_charts # This data is also ready for JSON
        }

        return Response(response_data)

class ClientDocumentView(APIView):
    """
    Provides a structured view of all documents accessible to a client,
    organized by year, quarter, and month for easy navigation.
    """
    permission_classes = [IsAuthenticated, IsClient]

    def get(self, request, *args, **kwargs):
        try:
            client = request.user.client
        except Client.DoesNotExist:
            return Response({"error": "Client profile not found for this user."}, status=status.HTTP_404_NOT_FOUND)

        # 1. Fetch all relevant documents in a single, optimized query
        documents = Document.objects.filter(
            Q(folio__client=client) | Q(document_type='Company')
        ).select_related('folio').order_by('-year', '-month').distinct()

        # 2. Structure the data into the format expected by the frontend
        company_documents = {}
        client_documents = {}
        
        serializer = DashboardDocumentSerializer(documents, many=True, context={'request': request})

        for doc_data in serializer.data:
            year = doc_data.get('year')
            month = doc_data.get('month')
            doc_type = doc_data.get('document_type')

            if not year or not month:
                continue # Skip documents without a year/month
            
            year_key = str(year)
            quarter_key = str((month - 1) // 3 + 1)  # Remove 'Q' prefix for consistency
            month_key = calendar.month_name[month]

            if doc_type == 'Company':
                # Structure company documents by year/quarter/month
                year_node = company_documents.setdefault(year_key, {})
                quarter_node = year_node.setdefault(quarter_key, {})
                month_node = quarter_node.setdefault(month_key, [])
                month_node.append(doc_data)
            else:
                # Structure client documents by folio/year
                folio_number = doc_data.get('folio_number')
                if folio_number:
                    folio_key = str(folio_number)
                    folio_node = client_documents.setdefault(folio_key, {})
                    year_node = folio_node.setdefault(year_key, [])
                    year_node.append(doc_data)

        structured_data = {
            "company_documents": company_documents,
            "client_documents": client_documents
        }

        return Response(structured_data)

@method_decorator(ratelimit(key='user_or_ip', rate='30/m', block=True), name='dispatch')
class ClientDocumentDownloadView(DownloadDocumentView):
    """
    A view for clients to download documents with specific permissions.
    - Inherits file handling from DownloadDocumentView.
    - Allows download of any 'Company' document.
    - Restricts 'Client' document downloads to the client's own folios.
    """
    permission_classes = [IsAuthenticated, IsClient] # Ensures the user is logged in.

    def get(self, request, doc_id):
        try:
            document = Document.objects.get(id=doc_id)
        except Document.DoesNotExist:
            return Response({"detail": "Document not found."}, status=status.HTTP_404_NOT_FOUND)

        # --- Authorization Check for 'Client' Documents ---
        if document.document_type == 'Client':
            try:
                is_owner = Folio.objects.filter(
                    folio_number=document.folio.folio_number,
                    client__user=request.user
                ).exists()

                if not is_owner:
                    return Response(
                        {"detail": "You do not have permission to access this document."},
                        status=status.HTTP_403_FORBIDDEN
                    )
            except ValueError:
                return Response({"detail": "Invalid folio number format."}, status=status.HTTP_400_BAD_REQUEST)

        return super(ClientDocumentDownloadView, self).get(request, doc_id)
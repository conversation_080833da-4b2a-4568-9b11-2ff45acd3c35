
import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import Login from './pages/auth/Login';
import Unauthorized from './pages/Unauthorized';
import PrivateRoute from './components/common/PrivateRoute';
import { Box } from '@mui/material';

import Admin from './pages/admin/Admin';
import AdminDashboard from './pages/admin/AdminDashboard';
import StaffList from './pages/admin/StaffList';
import ClientList from './pages/admin/ClientList';
import RegisterStaff from './pages/admin/RegisterStaff';

import Staff from './pages/staff/Staff';
import StaffDashboard from './pages/staff/StaffDashboard';
import ClientDirectory from './pages/staff/ClientDirectory';
import RegisterClient from './pages/staff/RegisterClient';
import DocumentManager from './pages/staff/DocumentManager';
import UploadDocument from './pages/staff/UploadDocument';
import TerminateFolio from './pages/staff/TerminateFolio';

import Client from './pages/client/Client';
import ClientDashboard from './pages/client/ClientDashboard';
import Statements from './pages/client/Statements';

import PasswordResetRequest from './pages/auth/PasswordResetRequest';
import PasswordResetConfirm from './pages/auth/PasswordResetConfirm';

const App: React.FC = () => {

  return (
    <Box sx={{ display: 'flex' }}>
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/password-reset" element={<PasswordResetRequest />} />
        <Route path="/password-reset/confirm/:uidb64/:token" element={<PasswordResetConfirm />} />
        <Route path="/unauthorized" element={<Unauthorized />} />

        {/* Admin Routes */}
        <Route path="/admin" element={<PrivateRoute allowedRoles={['admin']} />}>
            <Route path="" element={<Admin />}>
                <Route path="" element={<Navigate to="dashboard" replace />} />
                <Route path="dashboard" element={<AdminDashboard />} />
                <Route path="staff" element={<StaffList />} />
                <Route path="clients" element={<ClientList />} />
                <Route path="register-staff" element={<RegisterStaff />} />
            </Route>
        </Route>

        {/* Staff Routes */}
        <Route path="/staff" element={<PrivateRoute allowedRoles={['staff']} />}>
            <Route path="" element={<Staff />}>
                <Route path="" element={<Navigate to="dashboard" replace />} />
                <Route path="dashboard" element={<StaffDashboard />} />
                <Route path="clients" element={<ClientDirectory />} />
                <Route path="register-client" element={<RegisterClient />} />
                <Route path="documents" element={<DocumentManager />} />
                <Route path="upload-document" element={<UploadDocument />} />
                <Route path="terminate-folio" element={<TerminateFolio />} />
            </Route>
        </Route>

        {/* Client Routes */}
        <Route path="/client" element={<PrivateRoute allowedRoles={['client']} />}>
            <Route path="" element={<Client />}>
                <Route path="" element={<Navigate to="dashboard" replace />} />
                <Route path="dashboard" element={<ClientDashboard />} />
                <Route path="statements" element={<Statements />} />
            </Route>
        </Route>

        <Route path="/" element={<Navigate to="/login" replace />} />
      </Routes>
    </Box>
  );
};

export default App;

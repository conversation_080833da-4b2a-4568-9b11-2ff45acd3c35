
import React, { createContext, useContext, useState, useEffect } from 'react';
import { validateSession, login as apiLogin, logout as apiLogout } from '../services/authService';

interface User {
  id: number;
  username: string;
  role: string;
}

interface LoginData {
  username: string;
  password: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (credentials: LoginData) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkSession = async () => {
      try {
        const userData = await validateSession();
        setUser(userData);
      } catch (error) {
        setUser(null);
      }
      setLoading(false);
    };

    checkSession();
  }, []);

  const login = async (credentials: LoginData) => {
    const userData = await apiLogin(credentials);
    setUser(userData);
  };

  const logout = async () => {
    await apiLogout();
    setUser(null);
    // After logout, we can redirect the user to the login page.
    // This can be handled in the component that calls logout.
    window.location.href = '/login';
  };

  return (
    <AuthContext.Provider value={{ user, loading, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

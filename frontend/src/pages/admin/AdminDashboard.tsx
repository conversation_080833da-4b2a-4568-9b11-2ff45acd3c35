import React, { useEffect, useState } from 'react';
import { getDashboardStats } from '../../services/adminService';
import { Card, Typography, Box, Grid } from '@mui/material';
import PeopleIcon from '@mui/icons-material/People';
import FolderIcon from '@mui/icons-material/Folder';

interface StatCardProps {
  title: string;
  value: number | string;
  icon: React.ReactElement;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon }) => (
  <Card sx={{ display: 'flex', alignItems: 'center', p: 2 }}>
    <Box sx={{ flexGrow: 1 }}>
      <Typography color="text.secondary" gutterBottom>
        {title}
      </Typography>
      <Typography variant="h4" component="div">
        {value}
      </Typography>
    </Box>
    {icon}
  </Card>
);

const AdminDashboard: React.FC = () => {
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const data = await getDashboardStats();
        setStats(data);
      } catch (error) {
        console.error('Failed to fetch dashboard stats', error);
      }
      setLoading(false);
    };

    fetchStats();
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <Box>
        <Typography variant="h4" gutterBottom>
            Welcome, {stats?.adminName || 'Admin'}
        </Typography>
        <Grid container spacing={3}>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <StatCard title="Total Staff" value={stats?.staffCount} icon={<PeopleIcon sx={{ fontSize: 40, color: 'primary.main' }} />} />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <StatCard title="Total Clients" value={stats?.clientCount} icon={<PeopleIcon sx={{ fontSize: 40, color: 'secondary.main' }} />} />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <StatCard title="Total Folios" value={stats?.folioCount} icon={<FolderIcon sx={{ fontSize: 40, color: 'error.main' }} />} />
            </Grid>
        </Grid>
    </Box>
  );
};

export default AdminDashboard;
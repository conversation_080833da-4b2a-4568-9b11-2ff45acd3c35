import React, { useEffect, useState } from 'react';
import { getClientList, searchClients } from '../../services/adminService';
import { getErrorMessage } from '../../utils/errorUtils';
import {
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography, Box, TextField, Button, CircularProgress, Alert
} from '@mui/material';

interface ClientMember {
    id: number;
    user: number;
    email: string;
    first_name: string;
    last_name: string;
    pan: string;
}

const ClientList: React.FC = () => {
  const [clients, setClients] = useState<ClientMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [query, setQuery] = useState('');
  const [error, setError] = useState<string | null>(null);

  const fetchClients = async () => {
    setLoading(true);
    try {
      const data = await getClientList();
      setClients(data);
    } catch (error) {
      console.error('Failed to fetch client list', error);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchClients();
  }, []);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const data = await searchClients(query);
      setClients(data);
    } catch (err: any) {
      setError(getErrorMessage(err));
      setClients([]);
    }

    setLoading(false);
  };

  const handleShowAll = async () => {
    setQuery('');
    setLoading(true);
    setError(null);

    try {
      const data = await searchClients(''); // Empty query to get all clients
      setClients(data);
    } catch (err: any) {
      setError(getErrorMessage(err));
      setClients([]);
    }

    setLoading(false);
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Client List
      </Typography>

      <Box component="form" onSubmit={handleSearch} sx={{ mb: 4, display: 'flex', gap: 2, alignItems: 'center' }}>
        <TextField
          label="Search by Name or Folio Number"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          variant="outlined"
          sx={{ flexGrow: 1 }}
          placeholder="Leave empty and search to show all clients"
        />
        <Button type="submit" variant="contained" disabled={loading}>
          {loading ? <CircularProgress size={24} /> : 'Search'}
        </Button>
        <Button
          variant="outlined"
          onClick={handleShowAll}
          disabled={loading}
          sx={{ whiteSpace: 'nowrap' }}
        >
          Show All
        </Button>
      </Box>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      <Box sx={{ overflowX: 'auto' }}>
        <TableContainer component={Paper}>
          <Table sx={{ minWidth: 650 }} aria-label="simple table">
            <TableHead>
              <TableRow>
                <TableCell>First Name</TableCell>
                <TableCell>Last Name</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>PAN</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {clients.map((client) => (
                <TableRow key={client.id}>
                  <TableCell>{client.first_name}</TableCell>
                  <TableCell>{client.last_name}</TableCell>
                  <TableCell>{client.email}</TableCell>
                  <TableCell>{client.pan}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    </Box>
  );
};

export default ClientList;

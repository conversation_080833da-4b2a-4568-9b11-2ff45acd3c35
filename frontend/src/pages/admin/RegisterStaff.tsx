import React, { useState } from 'react';
import { registerStaff } from '../../services/adminService';
import { getErrorMessage } from '../../utils/errorUtils';
import {
  TextField, Button, Box, Typography, CircularProgress, Alert
} from '@mui/material';

const RegisterStaff: React.FC = () => {
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await registerStaff({ email, first_name: firstName, last_name: lastName });
      setSuccess('Staff member registered successfully!');
      setEmail('');
      setFirstName('');
      setLastName('');
    } catch (err: any) {
      setError(getErrorMessage(err));
    }

    setLoading(false);
  };

  return (
    <Box sx={{ maxWidth: 500 }}>
      <Typography variant="h4" gutterBottom>
        Register New Staff Member
      </Typography>
      <Box component="form" onSubmit={handleSubmit} noValidate sx={{ mt: 1 }}>
        <TextField
          margin="normal"
          required
          fullWidth
          id="email"
          label="Email Address"
          name="email"
          autoComplete="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
        />
        <TextField
          margin="normal"
          required
          fullWidth
          id="firstName"
          label="First Name"
          name="firstName"
          autoComplete="given-name"
          value={firstName}
          onChange={(e) => setFirstName(e.target.value)}
        />
        <TextField
          margin="normal"
          required
          fullWidth
          id="lastName"
          label="Last Name"
          name="lastName"
          autoComplete="family-name"
          value={lastName}
          onChange={(e) => setLastName(e.target.value)}
        />
        {error && <Alert severity="error" sx={{ width: '100%', mt: 2 }}>{error}</Alert>}
        {success && <Alert severity="success" sx={{ width: '100%', mt: 2 }}>{success}</Alert>}
        <Button
          type="submit"
          fullWidth
          variant="contained"
          sx={{ mt: 3, mb: 2 }}
          disabled={loading}
        >
          {loading ? <CircularProgress size={24} /> : 'Register Staff'}
        </Button>
      </Box>
    </Box>
  );
};

export default RegisterStaff;

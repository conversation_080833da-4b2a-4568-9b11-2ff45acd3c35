import React, { useEffect, useState } from 'react';
import { getStaffList, searchStaff } from '../../services/adminService';
import { getErrorMessage } from '../../utils/errorUtils';
import {
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography, Box, TextField, Button, CircularProgress, Alert
} from '@mui/material';

interface StaffMember {
    id: number;
    user: number;
    email: string;
    first_name: string;
    last_name: string;
}

const StaffList: React.FC = () => {
  const [staff, setStaff] = useState<StaffMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [query, setQuery] = useState('');
  const [error, setError] = useState<string | null>(null);

  const fetchStaff = async () => {
    setLoading(true);
    try {
      const data = await getStaffList();
      setStaff(data);
    } catch (error) {
      console.error('Failed to fetch staff list', error);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchStaff();
  }, []);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const data = await searchStaff(query);
      setStaff(data);
    } catch (err: any) {
      setError(getErrorMessage(err));
      setStaff([]);
    }

    setLoading(false);
  };

  const handleShowAll = async () => {
    setQuery('');
    setLoading(true);
    setError(null);

    try {
      const data = await searchStaff(''); // Empty query to get all staff
      setStaff(data);
    } catch (err: any) {
      setError(getErrorMessage(err));
      setStaff([]);
    }

    setLoading(false);
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Staff List
      </Typography>

      <Box component="form" onSubmit={handleSearch} sx={{ mb: 4, display: 'flex', gap: 2, alignItems: 'center' }}>
        <TextField
          label="Search by Name or Staff ID"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          variant="outlined"
          sx={{ flexGrow: 1 }}
          placeholder="Leave empty and search to show all staff"
        />
        <Button type="submit" variant="contained" disabled={loading}>
          {loading ? <CircularProgress size={24} /> : 'Search'}
        </Button>
        <Button
          variant="outlined"
          onClick={handleShowAll}
          disabled={loading}
          sx={{ whiteSpace: 'nowrap' }}
        >
          Show All
        </Button>
      </Box>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      <Box sx={{ overflowX: 'auto' }}>
        <TableContainer component={Paper}>
          <Table sx={{ minWidth: 650 }} aria-label="simple table">
            <TableHead>
              <TableRow>
                <TableCell>Staff ID</TableCell>
                <TableCell>First Name</TableCell>
                <TableCell>Last Name</TableCell>
                <TableCell>Email</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {staff.map((staffMember) => (
                <TableRow key={staffMember.id}>
                  <TableCell>{staffMember.user}</TableCell>
                  <TableCell>{staffMember.first_name}</TableCell>
                  <TableCell>{staffMember.last_name}</TableCell>
                  <TableCell>{staffMember.email}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    </Box>
  );
};

export default StaffList;

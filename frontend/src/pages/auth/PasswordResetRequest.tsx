import React, { useState } from 'react';
import {
  Con<PERSON>er, Box, TextField, Button, Typography, CircularProgress, Alert, Link, Grid
} from '@mui/material';
import { getErrorMessage } from '../../utils/errorUtils';
import { requestPasswordReset } from '../../services/authService';
import { Link as RouterLink } from 'react-router-dom';

const PasswordResetRequest: React.FC = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setMessage(null);
    try {
      const response = await requestPasswordReset(email);
      setMessage(response.detail);
    } catch (err: any) {
      setError(getErrorMessage(err));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container component="main" maxWidth="xs">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Typography component="h1" variant="h5">
          Forgot Password
        </Typography>
        <Box component="form" onSubmit={handleSubmit} noValidate sx={{ mt: 1 }}>
          <TextField
            margin="normal"
            required
            fullWidth
            id="email"
            label="Email Address"
            name="email"
            autoComplete="email"
            autoFocus
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          {message && <Alert severity="success" sx={{ width: '100%', mt: 2 }}>{message}</Alert>}
          {error && <Alert severity="error" sx={{ width: '100%', mt: 2 }}>{error}</Alert>}
          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 3, mb: 2 }}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Send Password Reset Email'}
          </Button>
            <Grid container>
                <Grid>
                    <Link component={RouterLink} to="/login" variant="body2">
                        {"Go to Login"}
                    </Link>
                </Grid>
            </Grid>
        </Box>
      </Box>
    </Container>
  );
};

export default PasswordResetRequest;

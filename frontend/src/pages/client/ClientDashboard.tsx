import React, { useEffect, useState } from 'react';
import { getDashboardData } from '../../services/clientService';
import { Bar, Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Box, Typography, Paper, List, ListItem, ListItemText } from '@mui/material';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend
);

const ClientDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const data = await getDashboardData();
        setDashboardData(data);
      } catch (error) {
        console.error('Failed to fetch dashboard data', error);
      }
      setLoading(false);
    };

    fetchData();
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  const performanceChartData = {
    labels: dashboardData?.performance_chart_data.map((d: any) => d.period),
    datasets: [
      {
        label: 'Fund Performance',
        data: dashboardData?.performance_chart_data.map((d: any) => d.fund_performance),
        backgroundColor: 'rgba(13, 27, 75, 0.8)',
      },
      {
        label: 'Benchmark Performance',
        data: dashboardData?.performance_chart_data.map((d: any) => d.benchmark_performance),
        backgroundColor: 'rgba(212, 175, 55, 0.8)',
      },
    ],
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        {dashboardData?.message}
      </Typography>

      {/* Main Dashboard Grid */}
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: {
          xs: '1fr',
          md: 'repeat(2, 1fr)',
          lg: 'repeat(3, 1fr)'
        },
        gap: 3
      }}>
        {/* Fund Performance Chart */}
        <Paper sx={{
          p: 3,
          height: { xs: 380, sm: 430, md: 480 },
          display: 'flex',
          flexDirection: 'column',
          gridColumn: { xs: '1', md: '1 / -1', lg: '1 / 3' }
        }}>
          <Typography variant="h6" sx={{ mb: 2, fontSize: '1.25rem', fontWeight: 600 }}>
            Fund vs Benchmark Performance (%)
          </Typography>
          <Box sx={{
            height: { xs: 300, sm: 350, md: 400 },
            width: '100%',
            position: 'relative',
            flexGrow: 1
          }}>
            <Bar
              data={performanceChartData}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    display: true,
                    position: 'top',
                    labels: {
                      font: {
                        size: 14
                      },
                      padding: 20
                    }
                  },
                  tooltip: {
                    titleFont: {
                      size: 14
                    },
                    bodyFont: {
                      size: 13
                    }
                  }
                },
                scales: {
                  x: {
                    ticks: {
                      font: {
                        size: 12
                      }
                    }
                  },
                  y: {
                    ticks: {
                      font: {
                        size: 12
                      }
                    }
                  }
                }
              }}
            />
          </Box>
        </Paper>

        {/* Documents Section - positioned strategically */}
        <Paper sx={{
          p: 3,
          height: { xs: 360, sm: 430, md: 480 },
          display: 'flex',
          flexDirection: 'column',
          gridColumn: { xs: '1', lg: '3' }
        }}>
          <Typography variant="h6" sx={{ mb: 2, fontSize: '1.25rem', fontWeight: 600 }}>
            Documents for Current Month
          </Typography>
          <Box sx={{
            flexGrow: 1,
            overflow: 'auto'
          }}>
            {(dashboardData?.company_documents_current_month?.length > 0 ||
              dashboardData?.client_documents_current_month?.length > 0) ? (
              <List>
                {dashboardData?.company_documents_current_month.map((doc: any) => (
                  <ListItem key={doc.id} component="a" href={doc.uploaded_file_url} target="_blank">
                    <ListItemText primary={`${doc.subtype_display || doc.subtype} (${doc.year}-${doc.month})`} />
                  </ListItem>
                ))}
                {dashboardData?.client_documents_current_month.map((doc: any) => (
                  <ListItem key={doc.id} component="a" href={doc.uploaded_file_url} target="_blank">
                    <ListItemText primary={`Folio ${doc.folio_number} Statement (${doc.year}-${doc.month})`} />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                color: 'text.secondary'
              }}>
                <Typography variant="body1">
                  No documents available
                </Typography>
              </Box>
            )}
          </Box>
        </Paper>

        {/* Folio Charts */}
        {dashboardData?.client_line_charts_data.map((folioChart: any) => (
          <Paper key={folioChart.folio_number} sx={{
            p: 3,
            height: { xs: 360, sm: 430, md: 480 },
            display: 'flex',
            flexDirection: 'column'
          }}>
            <Typography variant="h6" sx={{ mb: 2, fontSize: '1.25rem', fontWeight: 600 }}>
              Folio {folioChart.folio_number} NAV
            </Typography>
            <Box sx={{
              height: { xs: 280, sm: 350, md: 400 },
              width: '100%',
              position: 'relative',
              flexGrow: 1
            }}>
              <Line
                data={{
                  labels: folioChart.labels,
                  datasets: folioChart.datasets.map((ds: any, dsIndex: number) => {
                    // Define color palette for different datasets
                    const colors = [
                      { border: '#1976d2', background: 'rgba(25, 118, 210, 0.1)' }, // Blue
                      { border: '#9c27b0', background: 'rgba(156, 39, 176, 0.1)' },  // Purple
                      { border: '#388e3c', background: 'rgba(56, 142, 60, 0.1)' },   // Green
                      { border: '#f57c00', background: 'rgba(245, 124, 0, 0.1)' },   // Orange
                      { border: '#00796b', background: 'rgba(0, 121, 107, 0.1)' },   // Teal
                      { border: '#5d4037', background: 'rgba(93, 64, 55, 0.1)' }     // Brown
                    ];

                    const colorIndex = dsIndex % colors.length;

                    return {
                      ...ds,
                      tension: 0.1,
                      borderWidth: 3,
                      pointRadius: 4,
                      pointHoverRadius: 6,
                      borderColor: colors[colorIndex].border,
                      backgroundColor: colors[colorIndex].background,
                      fill: false
                    };
                  })
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: true,
                      position: 'top',
                      labels: {
                        font: {
                          size: 13
                        },
                        padding: 15
                      }
                    },
                    tooltip: {
                      titleFont: {
                        size: 13
                      },
                      bodyFont: {
                        size: 12
                      }
                    }
                  },
                  scales: {
                    x: {
                      ticks: {
                        font: {
                          size: 11
                        },
                        maxTicksLimit: 8
                      }
                    },
                    y: {
                      ticks: {
                        font: {
                          size: 11
                        }
                      }
                    }
                  }
                }}
              />
            </Box>
          </Paper>
        ))}
      </Box>
    </Box>
  );
};

export default ClientDashboard;
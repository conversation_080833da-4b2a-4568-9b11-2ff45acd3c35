import React, { useState } from 'react';
import { searchClients, createFolio } from '../../services/staffService';
import { getErrorMessage } from '../../utils/errorUtils';
import {
  Text<PERSON>ield, Button, Box, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, CircularProgress, Alert,
  Dialog, DialogTitle, DialogContent, DialogActions
} from '@mui/material';

interface ClientSearchResult {
    username: string;
    first_name: string;
    last_name: string;
    email: string;
    folio_numbers: Record<string, boolean>;
}

const ClientDirectory: React.FC = () => {
  const [query, setQuery] = useState('');
  const [searchType, setSearchType] = useState('name'); // 'name' or 'folio'
  const [results, setResults] = useState<ClientSearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Dialog state for creating folio
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedUsername, setSelectedUsername] = useState('');
  const [newFolioNumber, setNewFolioNumber] = useState('');
  const [folioLoading, setFolioLoading] = useState(false);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const searchData = searchType === 'name' ? { name_query: query } : { folio_number_query: query };
      const data = await searchClients(searchData);
      setResults(data);
    } catch (err: any) {
      setError(getErrorMessage(err));
      setResults([]);
    }

    setLoading(false);
  };

  const handleCreateFolio = (username: string) => {
    setSelectedUsername(username);
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedUsername('');
    setNewFolioNumber('');
    setError(null);
  };

  const handleConfirmCreateFolio = async () => {
    if (!newFolioNumber) {
      setError('Folio number is required.');
      return;
    }

    const folioNum = parseInt(newFolioNumber);
    if (isNaN(folioNum) || folioNum <= 0) {
      setError('Folio number must be a positive integer.');
      return;
    }

    setFolioLoading(true);
    setError(null);

    try {
      await createFolio(selectedUsername, newFolioNumber);
      // Refresh the search results to show the new folio
      const searchData = searchType === 'name' ? { name_query: query } : { folio_number_query: query };
      const data = await searchClients(searchData);
      setResults(data);
      handleDialogClose();
    } catch (err: any) {
      setError(getErrorMessage(err));
    }

    setFolioLoading(false);
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Client Directory
      </Typography>
      <Box component="form" onSubmit={handleSearch} sx={{ mb: 4, display: 'flex', gap: 2, alignItems: 'center' }}>
        <TextField
          label={`Search by ${searchType === 'name' ? 'Name' : 'Folio Number'}`}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          variant="outlined"
          sx={{ flexGrow: 1 }}
        />
        <Button onClick={() => setSearchType(searchType === 'name' ? 'folio' : 'name')}>
          Search by {searchType === 'name' ? 'Folio' : 'Name'}
        </Button>
        <Button type="submit" variant="contained" disabled={loading}>
          {loading ? <CircularProgress size={24} /> : 'Search'}
        </Button>
      </Box>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      <Box sx={{ overflowX: 'auto' }}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Username</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Folios (Active/Terminated)</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {results.map((client) => (
                <TableRow key={client.username}>
                  <TableCell>{client.username}</TableCell>
                  <TableCell>{`${client.first_name} ${client.last_name}`}</TableCell>
                  <TableCell>{client.email}</TableCell>
                  <TableCell>
                    {Object.entries(client.folio_numbers).map(([folio, terminated]) => (
                      <span key={folio} style={{ color: terminated ? 'red' : 'green' }}>
                        {folio}{!terminated ? ' (Active)' : ' (Terminated)'}{ ' ' }
                      </span>
                    ))}
                  </TableCell>
                  <TableCell>
                    <Button variant="contained" onClick={() => handleCreateFolio(client.username)}>Create Folio</Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      {/* Create Folio Dialog */}
      <Dialog open={dialogOpen} onClose={handleDialogClose} maxWidth="sm" fullWidth>
        <DialogTitle>Create New Folio</DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Creating a new folio for user: <strong>{selectedUsername}</strong>
          </Typography>

          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

          <TextField
            label="Folio Number"
            value={newFolioNumber}
            onChange={(e) => setNewFolioNumber(e.target.value)}
            fullWidth
            required
            type="number"
            slotProps={{
              htmlInput: { min: 1 }
            }}
            helperText="Enter a unique positive integer for the folio number"
            sx={{ mt: 1 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose} disabled={folioLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmCreateFolio}
            variant="contained"
            disabled={folioLoading || !newFolioNumber}
          >
            {folioLoading ? <CircularProgress size={20} /> : 'Create Folio'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ClientDirectory;

import React, { useState } from 'react';
import { registerClient } from '../../services/staffService';
import { getErrorMessage } from '../../utils/errorUtils';
import {
  <PERSON>Field, Button, Box, Typography, CircularProgress, Alert
} from '@mui/material';

const RegisterClient: React.FC = () => {
  const [pan, setPan] = useState('');
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [folioNumber, setFolioNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleRegister = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    // Validate required fields
    if (!pan || !email || !firstName || !lastName || !folioNumber) {
      setError('All fields are required.');
      setLoading(false);
      return;
    }

    // Validate folio number is a positive integer
    const folioNum = parseInt(folioNumber);
    if (isNaN(folioNum) || folioNum <= 0) {
      setError('Folio number must be a positive integer.');
      setLoading(false);
      return;
    }

    try {
      await registerClient({ pan, email, first_name: firstName, last_name: lastName, folio_number: folioNumber });
      setSuccess('Client registered successfully!');
      // Reset form
      setPan('');
      setEmail('');
      setFirstName('');
      setLastName('');
      setFolioNumber('');
    } catch (err: any) {
      setError(getErrorMessage(err));
    }
    setLoading(false);
  };

  return (
    <Box sx={{ maxWidth: 600, margin: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Register New Client
      </Typography>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

      <Box>
        <TextField
          label="PAN Number"
          value={pan}
          onChange={(e) => setPan(e.target.value.toUpperCase())}
          fullWidth
          margin="normal"
        />
        <TextField
          label="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          fullWidth
          margin="normal"
        />
        <TextField
          label="First Name"
          value={firstName}
          onChange={(e) => setFirstName(e.target.value)}
          fullWidth
          margin="normal"
        />
        <TextField
          label="Last Name"
          value={lastName}
          onChange={(e) => setLastName(e.target.value)}
          fullWidth
          margin="normal"
        />
        <TextField
          label="Folio Number"
          value={folioNumber}
          onChange={(e) => setFolioNumber(e.target.value)}
          fullWidth
          margin="normal"
          required
          type="number"
          slotProps={{
            htmlInput: { min: 1 }
          }}
          helperText="Enter a unique positive integer for the folio number"
        />
        <Button
          onClick={handleRegister}
          variant="contained"
          color="primary"
          disabled={loading || !pan || !email || !firstName || !lastName || !folioNumber}
          fullWidth
          sx={{ mt: 2 }}
        >
          {loading ? <CircularProgress size={24} /> : 'Register Client'}
        </Button>
      </Box>
    </Box>
  );
};

export default RegisterClient;

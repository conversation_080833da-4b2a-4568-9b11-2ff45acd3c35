
import React, { useEffect, useState } from 'react';
import { getDashboardStats } from '../../services/staffService';
import { Box, Typography, Paper } from '@mui/material';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const StaffDashboard: React.FC = () => {
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const data = await getDashboardStats();
        setStats(data);
      } catch (error) {
        console.error('Failed to fetch dashboard stats', error);
      }
      setLoading(false);
    };

    fetchStats();
  }, []);

  const chartData = {
    labels: stats?.fund_vs_benchmark?.labels || [],
    datasets: [
      {
        label: 'Fund Performance',
        data: stats?.fund_vs_benchmark?.fund_performance || [],
        backgroundColor: 'rgba(75, 192, 192, 0.6)',
      },
      {
        label: 'Benchmark Performance',
        data: stats?.fund_vs_benchmark?.benchmark_performance || [],
        backgroundColor: 'rgba(153, 102, 255, 0.6)',
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          font: {
            size: 14
          },
          padding: 20
        }
      },
      title: {
        display: true,
        text: 'Fund vs. Benchmark Performance',
        font: {
          size: 16
        }
      },
      tooltip: {
        titleFont: {
          size: 14
        },
        bodyFont: {
          size: 13
        }
      }
    },
    scales: {
      x: {
        ticks: {
          font: {
            size: 12
          }
        }
      },
      y: {
        ticks: {
          font: {
            size: 12
          }
        }
      }
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Staff Dashboard
      </Typography>
      {loading ? (
        <div>Loading...</div>
      ) : stats && (
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: '1fr',
            md: 'repeat(4, 1fr)'
          },
          gap: 3
        }}>
          {/* Stats Cards Row - wider cards */}
          <Paper sx={{
            p: 4,
            height: { xs: 140, md: 180 },
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            gridColumn: { xs: '1', md: '1 / 3' }
          }}>
            <Typography variant="h6" sx={{ fontSize: '1.4rem', fontWeight: 600, color: 'primary.main', mb: 1 }}>
              Total Clients
            </Typography>
            <Typography variant="h2" sx={{ fontWeight: 'bold', color: 'text.primary' }}>
              {stats.num_clients}
            </Typography>
          </Paper>

          <Paper sx={{
            p: 4,
            height: { xs: 140, md: 180 },
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            gridColumn: { xs: '1', md: '3 / 5' }
          }}>
            <Typography variant="h6" sx={{ fontSize: '1.4rem', fontWeight: 600, color: 'success.main', mb: 1 }}>
              Active Folios
            </Typography>
            <Typography variant="h2" sx={{ fontWeight: 'bold', color: 'text.primary' }}>
              {stats.num_active_folios}
            </Typography>
          </Paper>

          {/* Performance Chart - spans full width and taller */}
          <Paper sx={{
            p: 4,
            height: { xs: 450, md: 550 },
            display: 'flex',
            flexDirection: 'column',
            gridColumn: { xs: '1', md: '1 / -1' }
          }}>
            <Typography variant="h5" sx={{ mb: 3, fontWeight: 600 }}>
              Fund vs. Benchmark Performance
            </Typography>
            <Box sx={{
              height: { xs: 370, md: 470 },
              width: '100%',
              position: 'relative',
              flexGrow: 1
            }}>
              <Bar options={chartOptions} data={chartData} />
            </Box>
          </Paper>
        </Box>
      )}
    </Box>
  );
};

export default StaffDashboard;

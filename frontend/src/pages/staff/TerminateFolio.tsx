import React, { useState } from 'react';
import { terminateFolio } from '../../services/staffService';
import { getErrorMessage } from '../../utils/errorUtils';
import {
  TextField, Button, Box, Typography, CircularProgress, Alert, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle
} from '@mui/material';

const TerminateFolio: React.FC = () => {
  const [folioNumber, setFolioNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [open, setOpen] = useState(false);
  const [confirmationText, setConfirmationText] = useState('');

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  const handleTerminate = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await terminateFolio(folioNumber);
      setSuccess(response.message);
      handleClose();
    } catch (err: any) {
      setError(getErrorMessage(err));
    }

    setLoading(false);
  };

  const isConfirmationTextValid = () => {
    return confirmationText === `${folioNumber} delete`;
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Terminate Folio
      </Typography>
      <Box component="form" onSubmit={(e) => { e.preventDefault(); handleOpen(); }} sx={{ mb: 4, display: 'flex', gap: 2, alignItems: 'center' }}>
        <TextField
          label="Folio Number"
          value={folioNumber}
          onChange={(e) => setFolioNumber(e.target.value)}
          variant="outlined"
          sx={{ flexGrow: 1 }}
        />
        <Button type="submit" variant="contained" color="error" disabled={!folioNumber}>
          Terminate
        </Button>
      </Box>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Confirm Termination</DialogTitle>
        <DialogContent>
          <DialogContentText>
            To confirm termination, please type the folio number followed by "delete".
            For example: "{folioNumber} delete"
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            label="Confirmation Text"
            type="text"
            fullWidth
            variant="standard"
            value={confirmationText}
            onChange={(e) => setConfirmationText(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleTerminate} color="error" disabled={!isConfirmationTextValid() || loading}>
            {loading ? <CircularProgress size={24} /> : 'Terminate'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TerminateFolio;

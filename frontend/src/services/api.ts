
import axios from 'axios';

const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL, 
  withCredentials: true,
});

api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.data) {
      // Backend-specific error structure
      if (error.response.data.error) {
        return Promise.reject(new Error(error.response.data.error));
      }
      if (error.response.data.detail) {
        return Promise.reject(new Error(error.response.data.detail));
      }
      // Handle validation errors (e.g., from Django Rest Framework)
      if (typeof error.response.data === 'object') {
        const messages = Object.values(error.response.data).flat();
        if (messages.length > 0) {
          return Promise.reject(new Error(messages.join(' ')));
        }
      }
    }
    return Promise.reject(new Error(error.message || 'An unexpected error occurred.'));
  }
);

export default api;

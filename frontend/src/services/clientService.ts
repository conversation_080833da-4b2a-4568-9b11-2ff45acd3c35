import api from './api';

export const getDashboardData = async (): Promise<any> => {
    const response = await api.get('/api/client/dashboard/');
    return response.data;
}

export const getDocuments = async (): Promise<any> => {
    const response = await api.get('api/client/documents/');
    return response.data;
}

export const downloadDocument = async (documentId: number): Promise<Blob> => {
    const response = await api.get(`/api/client/download-document/${documentId}/`, {
        responseType: 'blob',
    });
    return response.data;
};
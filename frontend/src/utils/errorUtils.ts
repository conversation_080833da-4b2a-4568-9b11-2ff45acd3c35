export const getErrorMessage = (err: any): string => {
  // Check 1: Detailed response from API body (your current logic)
  if (err.response?.data) {
    const data = err.response.data;
    if (data.detail) {
      return data.detail;
    }
    if (typeof data === 'object') {
      let message = '';
      for (const key in data) {
        if (Array.isArray(data[key])) {
          // If you want only the first message, change join(', ')
          message += `${key}: ${data[key].join(', ')}\n`;
        }
      }
      if (message) {
        return message;
      }
    }
  }
  
  //Check 2: Top-level error message (where 'This field may not be blank.' is found)
  if (err.message && typeof err.message === 'string') {
    return err.message; 
  }

  return 'An unexpected error occurred.';
};
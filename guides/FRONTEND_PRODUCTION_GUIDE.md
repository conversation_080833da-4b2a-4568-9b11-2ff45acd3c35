# Frontend Production Deployment Guide

This guide explains how to prepare and deploy the React frontend for production with relative paths.

## Changes Made for Production

### 1. Environment Configuration

**Development (.env.local):**
```
VITE_API_BASE_URL=http://localhost:8000
```

**Production (.env.production):**
```
VITE_API_BASE_URL=/api
```

**Staging (.env.staging):**
```
VITE_API_BASE_URL=/api
```

### 2. Vite Configuration Updates

The `vite.config.ts` has been updated with:
- **Relative base path**: `base: './'` for production builds
- **Code splitting**: Optimized chunks for better loading
- **Proxy configuration**: For development API calls
- **Build optimization**: Disabled sourcemaps for security

### 3. Build Scripts

New build commands in `package.json`:
```bash
npm run build:staging     # Build for staging environment
npm run build:production  # Build for production environment
npm run preview:production # Preview production build locally
```

## Deployment Steps

### Step 1: Build for Production

```bash
cd frontend
npm run build:production
```

This creates a `dist/` directory with optimized static files.

### Step 2: Django Configuration

Update your Django `settings.py` for production:

```python
# Production settings
ALLOWED_HOSTS = [
    'your-domain.com',
    'www.your-domain.com',
    'your-lightsail-ip',
]

CORS_ALLOWED_ORIGINS = [
    "https://your-domain.com",
    "https://www.your-domain.com",
]

# Static files configuration
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# Serve React build files
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'frontend/dist'),
]
```

### Step 3: URL Configuration

Update your main `urls.py` to serve the React app:

```python
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView

urlpatterns = [
    # Your existing API patterns
    path('api/', include('your_api_urls')),
    
    # Serve React app for all other routes
    path('', TemplateView.as_view(template_name='index.html')),
]

# Serve static files in development
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
```

### Step 4: Nginx Configuration (Recommended)

For production, use Nginx to serve static files:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # Serve React static files
    location / {
        root /path/to/your/project/frontend/dist;
        try_files $uri $uri/ /index.html;
    }

    # Proxy API requests to Django
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Serve Django admin
    location /admin/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Serve Django static files
    location /static/ {
        alias /path/to/your/project/staticfiles/;
    }
}
```

## Deployment Options

### Option 1: Nginx + Django (Recommended)

1. Build React app: `npm run build:production`
2. Configure Nginx to serve `frontend/dist/`
3. Proxy `/api/` requests to Django
4. Run Django with Gunicorn

### Option 2: Django Serves Everything

1. Build React app: `npm run build:production`
2. Configure Django to serve static files
3. Add React build to `STATICFILES_DIRS`
4. Use Django's static file serving

### Option 3: CDN + API Server

1. Build React app: `npm run build:production`
2. Upload `dist/` to CDN (S3, CloudFront, etc.)
3. Configure CORS for your CDN domain
4. Deploy Django as API-only server

## Environment Variables for Production

Create a `.env` file in your Django project root:

```bash
# Django settings
DEBUG=False
SECRET_KEY=your-production-secret-key
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# Database
DATABASE_URL=postgresql://user:pass@localhost/dbname

# CORS
CORS_ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# Email
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# AWS (if using S3)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_STORAGE_BUCKET_NAME=your-bucket-name
```

## Testing Production Build

### Test Locally

```bash
# Build for production
cd frontend
npm run build:production

# Preview the build
npm run preview:production

# Test with Django
cd ..
python manage.py collectstatic
python manage.py runserver
```

### Test API Connectivity

1. Open browser to `http://localhost:4173`
2. Check browser console for errors
3. Test login functionality
4. Verify API calls work with relative paths

## Troubleshooting

### Common Issues

1. **404 on page refresh**
   - Configure server to serve `index.html` for all routes
   - Check `try_files` in Nginx config

2. **API calls fail**
   - Verify CORS settings in Django
   - Check proxy configuration
   - Ensure `/api/` prefix is consistent

3. **Static files not loading**
   - Run `python manage.py collectstatic`
   - Check `STATIC_ROOT` and `STATIC_URL` settings
   - Verify file permissions

4. **CORS errors**
   - Update `CORS_ALLOWED_ORIGINS` in Django
   - Check protocol (http vs https)
   - Verify domain names match exactly

### Debug Commands

```bash
# Check Django static files
python manage.py findstatic index.html

# Test Django URLs
python manage.py show_urls

# Check Nginx configuration
nginx -t

# View Nginx logs
tail -f /var/log/nginx/error.log
```

## Security Considerations

1. **Disable sourcemaps** in production (already configured)
2. **Use HTTPS** in production
3. **Set secure headers** in Django
4. **Validate CORS origins** carefully
5. **Use environment variables** for sensitive data

## Performance Optimization

1. **Enable gzip compression** in Nginx
2. **Set cache headers** for static files
3. **Use CDN** for static assets
4. **Implement code splitting** (already configured)
5. **Optimize images** and assets

The frontend is now configured to use relative paths and is ready for production deployment!

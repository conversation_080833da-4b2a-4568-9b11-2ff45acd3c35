# OAuth Token Monitoring - Production Deployment Guide

This guide explains how to set up automated OAuth token monitoring on your production server (AWS Lightsail or any Linux server).

## Prerequisites

- Django project deployed on your server
- OAuth email system already configured (`accounts/credentials.json` and `accounts/token.json` exist)
- Virtual environment set up
- Cron service running (usually enabled by default)

## Deployment Steps

### 1. Upload Files to Server

Ensure these files are on your production server:
```
your-project/
├── accounts/management/commands/oauth_cron_check.py
├── scripts/setup_oauth_cron.sh
└── (rest of your Django project)
```

### 2. Make Setup Script Executable

```bash
chmod +x scripts/setup_oauth_cron.sh
```

### 3. Run Setup Script

From your Django project root directory:

```bash
# Replace with your actual admin email
./scripts/setup_oauth_cron.sh <EMAIL>
```

The script will:
- Auto-detect your project directory
- Find your Python/virtual environment
- Create the cron job script
- Test the OAuth system
- Show you the cron command to install

### 4. Install Cron Job

The setup script will show you a command like this:
```bash
(crontab -l 2>/dev/null; echo "0 9 * * * /path/to/your/project/scripts/oauth_cron_job.sh") | crontab -
```

Copy and run that exact command to install the cron job.

### 5. Verify Installation

Check that the cron job is installed:
```bash
crontab -l
```

You should see a line like:
```
0 9 * * * /path/to/your/project/scripts/oauth_cron_job.sh
```

## Configuration Options

### Change Schedule

To run at a different time, edit the cron schedule:
- `0 9 * * *` = Daily at 9:00 AM
- `0 */6 * * *` = Every 6 hours
- `0 2 * * *` = Daily at 2:00 AM

### Change Alert Email

Re-run the setup script with a different email:
```bash
./scripts/setup_oauth_cron.sh <EMAIL>
```

### Manual Testing

Test the OAuth system manually:
```bash
# Check OAuth status
python manage.py check_oauth_status

# Run cron check manually
python manage.py oauth_cron_check --alert-email <EMAIL>

# Test OAuth setup
python manage.py setup_oauth
```

## Monitoring

### View Logs

```bash
# View recent cron activity
tail -f logs/oauth_cron.log

# View all cron logs
cat logs/oauth_cron.log
```

### Check Cron Status

```bash
# List installed cron jobs
crontab -l

# Check if cron service is running
sudo systemctl status cron
# or on some systems:
sudo systemctl status crond
```

### Email Alerts

You'll receive email alerts when:
- Refresh token becomes invalid/expired
- OAuth system fails
- Gmail service becomes inaccessible

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   chmod +x scripts/setup_oauth_cron.sh
   chmod +x scripts/oauth_cron_job.sh
   ```

2. **Python Not Found**
   - Ensure virtual environment is activated
   - Check Python path in generated cron script

3. **Django Settings Error**
   - Ensure `DJANGO_SETTINGS_MODULE` is set correctly
   - Check that all environment variables are available

4. **Cron Not Running**
   ```bash
   sudo systemctl start cron
   sudo systemctl enable cron
   ```

### Log Analysis

Check logs for common patterns:
```bash
# Look for errors
grep -i error logs/oauth_cron.log

# Look for successful runs
grep -i "successfully" logs/oauth_cron.log

# Check recent activity
tail -20 logs/oauth_cron.log
```

## What the Cron Job Does

1. **Daily Health Check**: Tests refresh token by attempting to refresh credentials
2. **Proactive Detection**: Identifies refresh token expiration before it breaks email
3. **Automatic Refresh**: Updates access tokens when needed
4. **Alert System**: Sends emails when manual intervention is required
5. **Comprehensive Logging**: Records all activity for monitoring

## Security Notes

- The cron job runs with the same permissions as your web application
- OAuth credentials are stored securely in `accounts/token.json`
- Alert emails use SMTP fallback (not OAuth) to ensure delivery even if OAuth fails
- Logs may contain sensitive information - ensure proper file permissions

## Support

If you encounter issues:

1. Check the logs: `tail -f logs/oauth_cron.log`
2. Test manually: `python manage.py oauth_cron_check`
3. Verify OAuth setup: `python manage.py check_oauth_status`
4. Re-run setup if needed: `python manage.py setup_oauth`

The system is designed to be self-healing and will automatically recover from temporary issues.

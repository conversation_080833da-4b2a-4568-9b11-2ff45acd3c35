# Production Deployment Guide - Nginx + Uvicorn

## Architecture Overview

```
Internet → Nginx (Port 80/443) → Uvicorn/Django (Port 8000)
                ↓
         Static Files (React + Django)
```

- **Nginx**: Serves static files, handles SSL, proxies API requests
- **Uvicorn**: Runs Django application (API only)
- **No Django static file serving**: All handled by Nginx

## Deployment Steps

### 1. Build Frontend for Production

```bash
# Build React app with relative paths
cd frontend
npm run build:production

# Collect Django static files
cd ..
python manage.py collectstatic --noinput
```

### 2. Install and Configure Nginx

```bash
# Install Nginx (Ubuntu/Debian)
sudo apt update
sudo apt install nginx

# Copy configuration
sudo cp nginx.conf /etc/nginx/sites-available/amportal
sudo ln -s /etc/nginx/sites-available/amportal /etc/nginx/sites-enabled/
sudo rm /etc/nginx/sites-enabled/default  # Remove default site
```

### 3. Update Nginx Configuration

Edit `/etc/nginx/sites-available/amportal`:

```nginx
# Replace these paths with your actual paths:
# /path/to/your/project/staticfiles/     → /home/<USER>/amportal/staticfiles/
# /path/to/your/project/frontend/dist/  → /home/<USER>/amportal/frontend/dist/
# your-domain.com                       → your actual domain
```

### 4. Install Production Dependencies

```bash
# Install Uvicorn for production ASGI server
pip install uvicorn[standard] gunicorn

# Optional: Install process manager
pip install supervisor
```

### 5. Configure Django for Production

Create `.env.production`:

```bash
DJANGO_ENV=production
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
SECRET_KEY=your-super-secret-production-key
DATABASE_URL=your-production-database-url
CORS_ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com
```

### 6. Start Uvicorn

```bash
# Start Django with Uvicorn
uvicorn amportal_backend.asgi:application --host 127.0.0.1 --port 8000 --workers 4

# Or with Gunicorn (alternative)
gunicorn amportal_backend.asgi:application -k uvicorn.workers.UvicornWorker --bind 127.0.0.1:8000 --workers 4
```

### 7. Start Nginx

```bash
# Test configuration
sudo nginx -t

# Start/restart Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## File Structure After Deployment

```
/home/<USER>/amportal/
├── frontend/dist/           # React build files (served by Nginx)
│   ├── index.html
│   ├── assets/
│   │   ├── index-xxx.js
│   │   ├── vendor-xxx.js
│   │   └── mui-xxx.js
│   └── vite.svg
├── staticfiles/            # Django static files (served by Nginx)
│   ├── admin/
│   ├── rest_framework/
│   └── assets/             # React assets (copied here)
└── amportal_backend/       # Django app (served by Uvicorn)
```

## How It Works

### Request Flow:

1. **Static Files** (`/static/`, `/assets/`, `.js`, `.css`, etc.)
   - Nginx serves directly from filesystem
   - No Django involvement
   - Fast and efficient

2. **API Requests** (`/api/*`)
   - Nginx proxies to Uvicorn (Django)
   - Django handles business logic
   - Returns JSON responses

3. **React App** (all other routes)
   - Nginx serves `index.html`
   - React Router handles client-side routing
   - API calls go back to Django

### Benefits:

✅ **Fast static file serving** (Nginx is optimized for this)  
✅ **Proper MIME types** (Nginx handles automatically)  
✅ **SSL termination** (Nginx handles certificates)  
✅ **Load balancing** (Nginx can proxy to multiple Django instances)  
✅ **Caching** (Nginx can cache static files and API responses)  

## Testing

1. **Check Nginx status:**
   ```bash
   sudo systemctl status nginx
   ```

2. **Check Django/Uvicorn:**
   ```bash
   curl http://127.0.0.1:8000/api/auth/validate/
   ```

3. **Check full stack:**
   ```bash
   curl http://your-domain.com/api/auth/validate/
   curl http://your-domain.com/  # Should return React HTML
   ```

## Process Management (Optional)

Create `/etc/supervisor/conf.d/amportal.conf`:

```ini
[program:amportal]
command=/home/<USER>/amportal/env/bin/uvicorn amportal_backend.asgi:application --host 127.0.0.1 --port 8000 --workers 4
directory=/home/<USER>/amportal
user=ubuntu
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/amportal.log
```

This ensures Django automatically restarts if it crashes.

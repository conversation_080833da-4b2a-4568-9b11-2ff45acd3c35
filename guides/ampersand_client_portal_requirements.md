# 🧾 Project: Client Portal for Ampersand Capital Investments LLP

## 1. 🔍 Overview
A secure, role-based web portal for a SEBI-registered financial advisory firm. Staff and clients can log in via dedicated site paths. The system facilitates document uploads, access to monthly investment statements, and performance tracking using charts. Hosting must be scalable and support encrypted storage.

## 2. 👥 User Roles & Access

| Role             | Access Level                                                                 |
|------------------|------------------------------------------------------------------------------|
| **Admin**        | Create staff accounts, assign roles, view full staff/client DB               |
| **Staff**| Onboard clients, create/terminate folios, upload company/client documents    |
| **Client**       | View own fund performance, NAV charts, company documents, folio-based reports|

## 3. 🔐 Authentication & Credential Logic

### 🔑 Login System
- Login via: `username`, `password`
- Password rules: ≥8 chars, 1 uppercase, 1 number, 1 special
- ~~Session: JWT-based, 1-hour timeout with auto-logout~~

### 👤 Client Credential Generation (by Staff)
- Input: PAN + email → verify via PAN gateway → fetch name
- ~~Username = `name_clientcounter`~~
- Password = generated (strong); sent via email with site link
- Prompt password reset on first login
- ~~Assigned one folio on creation (via folio counter)~~

### 🧑‍💼 Staff Credential Generation (by Admin)
- Input: company email + staff ID
- ~~Username = `name_staffid`~~
- Password sent via company mail
- First login = password reset prompt

## 4. 🗂️ Portal URLs

- **Admin** → `www.ampersand.com/admin010302`
- **Staff** → `www.ampersand.com/staff`
- **Client** → `www.ampersand.com/client`

## 5. 📄 Functional Features
### Admin Portal
#### Staff Onboarding
-~~Enter Staff ID and Staff Email to generate credentials~~

#### Staff Directory
- Search bar to:
  - 🔤 Input **name** or
  - 🆔 Input **Staff ID**
  ### BACKEND DONE

- Results display (tabular):
  - **Full Name**
  - **Staff ID**
  - **Company Email**

### 🧭 Client Portal

#### Dashboard (Landing Page after Login)
1. **Column Chart**: Fund vs BSE 500 TRI
   - Timeframes: 1M, 3M, 6M, 1Y, 3Y, 5Y, Since Inception
   - Backend-generated
2. **Line Graph**: Pre-tax & Post-tax NAV since folio start
3. **Documents Section (Current Month Only)**
   - Monthly Fund Performance (PDF)
   - Monthly Investor Presentation (PDF)
   - SEBI Quarterly Statement (PDF)

#### Statements Page
- Select:
  - Folio (dropdown, client-specific)
  - Year (only active years)
- Grouped by:
  - Quarters (visual separation)
  - Documents (view/preview/download)

### 🧾 Staff Portal

#### Client Handling Page
- **Create Client**: PAN + Email → generate credentials → auto-create folio
- **Terminate Folio**: Input folio → confirm twice
- **Create New Folio**: Input Client ID → auto-assign folio

#### Upload Page
- Document Type: `Company` or `Client`
  - **If Company** → Subtype:
    - Monthly Fund Performance
    - Monthly Investor Presentation
    - SEBI Quarterly Report
  - **If Client**:
    - Folio Number
    - Year (default: current)
    - Month (default: current)
    - Pre-tax NAV, Post-tax NAV
    - Upload (auto-named)

#### Document Page
- **Company Documents**:
  - Organize by Year → Quarters → Months
- **Client Documents**:
  - Search by Folio or Client ID

#### Client Directory
- Search bar to:
  - 🔤 Input **Client Name** or
  - 🆔 Input **Folio Number**
- Results display:
  - **Client ID**
  - **Full Name**
  - **Folio Numbers** (all linked folios)
  - **Join Date** (i.e. `created_at` from first folio)


## 6. 📊 Data Structures

```sql
Client (
    id PRIMARY KEY,
    client_id UNIQUE,
    name TEXT NOT NULL,  -- ✅ newly added field
    email TEXT NOT NULL,
    password TEXT NOT NULL,
    password_changed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)


Staff (
    id PRIMARY KEY,
    staff_id UNIQUE,
    username TEXT,
    password TEXT,
    email TEXT,
    role TEXT
)

Admins (
  id PRIMARY KEY
  admin_id UNIQUE,
  password TEXT
)

Folios (
    folio_number PRIMARY KEY,
    client_id FOREIGN KEY REFERENCES Client(client_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)

SystemMeta (
  latest_client_number INT,
  latest_folio_number INT
)
```

## 7. 🧠 Folio Logic

- Client may own multiple folios
- Folio counter auto-increments globally
- Terminated folios are never reused
- Example:
  - Client A → Folio 033 (Jul 20)
  - Client B → Folio 034 (Jul 22)
  - Client A terminates → (Jul 25)
  - Client A opens new folio → Folio 035 (Aug 1)

## 8. 🧱 Technical Requirements

### ✅ Tech Stack (Suggested)

| Component   | Tech                            |
|------------|----------------------------------|
| Backend    | Django                           |   
| Frontend   | React                            |
| Database   | PostgreSQL                       |
| Storage    | AWS S3 (prod), local/dev fallback|
| Auth       | JWT                              |
| Hosting    | AWS LightSail                    |
| File Sec.  | AES Encrypted PDF Storage        |
| External   | NSDL / Protean eGov APIs (API), SendGrid (for emailing credentials)  |

## 9. 🚧 Non-Functional Requirements

- JWT expiration with countdown + auto logout
- Encrypted file storage for sensitive documents
- Secure password storage (default django secure password storage)
- Scalable infrastructure
- Modular routes for different user roles
- Role-based access control (RBAC)
- Mobile responsive frontend
- Compatible with both local servers and cloud deployment

## 🔄 Onboarding Existing Clients (Suggestion)
🟩 Prepare client dataset offline  
🟩 Pre-populate clients & folios in DB  
🟩 Then host — avoids double work


## 10. Design Requirements

- modern react framework feel for a finance company portal. stick to classics
- colour scheme white background, dark grey text, rgb(17,42,121) for navbar and buttons, rgb(57,150,50) for contrast
- uniformity across all three client, staff and admin
- navbar should collapse into hamburger menu on mobile device.
- website navbar should also provide logout button right aligned.



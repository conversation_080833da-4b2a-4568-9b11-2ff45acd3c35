server {
    listen 80;
    server_name your-domain.com;  # Replace with your actual domain
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Serve React static files
    location /static/ {
        alias /path/to/your/project/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # Handle JavaScript modules with correct MIME type
        location ~* \.js$ {
            add_header Content-Type application/javascript;
        }
    }
    
    # Serve React assets (JS, CSS, etc.)
    location /assets/ {
        alias /path/to/your/project/frontend/dist/assets/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # Handle JavaScript modules with correct MIME type
        location ~* \.js$ {
            add_header Content-Type application/javascript;
        }
    }
    
    # Serve other React files (vite.svg, etc.)
    location ~* \.(svg|ico|png|jpg|jpeg|gif|css)$ {
        root /path/to/your/project/frontend/dist/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API endpoints - proxy to Django/Uvicorn
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Handle CORS preflight requests
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }
    
    # Django admin - proxy to Django/Uvicorn
    location /superadmin/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Serve React app for all other routes (SPA routing)
    location / {
        try_files $uri $uri/ /index.html;
        root /path/to/your/project/frontend/dist/;
        
        # Cache control for HTML files
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # Error pages
    error_page 404 /index.html;
}

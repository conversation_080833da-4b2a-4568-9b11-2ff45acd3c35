#!/bin/bash

# Frontend Production Deployment Script
# Usage: ./scripts/deploy_frontend.sh [environment]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT="${1:-production}"
FRONTEND_DIR="frontend"
BUILD_DIR="$FRONTEND_DIR/dist"

echo -e "${GREEN}Frontend Deployment Script${NC}"
echo "=========================="
echo "Environment: $ENVIRONMENT"
echo "Frontend Directory: $FRONTEND_DIR"
echo "Build Directory: $BUILD_DIR"
echo ""

# Check if we're in the right directory
if [ ! -f "manage.py" ]; then
    echo -e "${RED}Error: manage.py not found. Please run this script from your Django project root.${NC}"
    exit 1
fi

if [ ! -d "$FRONTEND_DIR" ]; then
    echo -e "${RED}Error: Frontend directory not found at $FRONTEND_DIR${NC}"
    exit 1
fi

# Check if Node.js and npm are installed
if ! command -v node >/dev/null 2>&1; then
    echo -e "${RED}Error: Node.js is not installed. Please install Node.js first.${NC}"
    exit 1
fi

if ! command -v npm >/dev/null 2>&1; then
    echo -e "${RED}Error: npm is not installed. Please install npm first.${NC}"
    exit 1
fi

echo -e "${BLUE}Step 1: Installing frontend dependencies...${NC}"
cd "$FRONTEND_DIR"

if [ ! -d "node_modules" ]; then
    echo "Installing npm packages..."
    npm install
else
    echo "Dependencies already installed. Updating..."
    npm ci
fi

echo -e "${GREEN}✓ Dependencies installed${NC}"

echo -e "${BLUE}Step 2: Building frontend for $ENVIRONMENT...${NC}"

# Build based on environment
case $ENVIRONMENT in
    "production")
        npm run build:production
        ;;
    "staging")
        npm run build:staging
        ;;
    "development")
        npm run build
        ;;
    *)
        echo -e "${YELLOW}Warning: Unknown environment '$ENVIRONMENT'. Using default build.${NC}"
        npm run build
        ;;
esac

if [ ! -d "dist" ]; then
    echo -e "${RED}Error: Build failed. dist directory not created.${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Frontend built successfully${NC}"

# Go back to project root
cd ..

echo -e "${BLUE}Step 3: Configuring Django static files...${NC}"

# Check if Django is available
if ! python manage.py check >/dev/null 2>&1; then
    echo -e "${YELLOW}Warning: Django check failed. Please ensure your Django environment is properly configured.${NC}"
else
    echo "Django configuration is valid."
fi

# Collect static files (skip if STATIC_ROOT not configured)
echo "Collecting Django static files..."
if python manage.py collectstatic --noinput --dry-run >/dev/null 2>&1; then
    python manage.py collectstatic --noinput
    echo "Static files collected successfully."
else
    echo -e "${YELLOW}Warning: STATIC_ROOT not configured. Skipping collectstatic.${NC}"
    echo "Configure STATIC_ROOT in your Django settings for production."
fi

echo -e "${GREEN}✓ Static files collected${NC}"

echo -e "${BLUE}Step 4: Deployment summary...${NC}"

# Show build information
BUILD_SIZE=$(du -sh "$BUILD_DIR" | cut -f1)
FILE_COUNT=$(find "$BUILD_DIR" -type f | wc -l)

echo ""
echo "Build Summary:"
echo "=============="
echo "• Environment: $ENVIRONMENT"
echo "• Build size: $BUILD_SIZE"
echo "• Files created: $FILE_COUNT"
echo "• Build location: $BUILD_DIR"
echo ""

echo "Key files created:"
if [ -f "$BUILD_DIR/index.html" ]; then
    echo -e "${GREEN}✓ index.html${NC}"
else
    echo -e "${RED}✗ index.html (missing)${NC}"
fi

if [ -d "$BUILD_DIR/assets" ]; then
    ASSET_COUNT=$(find "$BUILD_DIR/assets" -name "*.js" -o -name "*.css" | wc -l)
    echo -e "${GREEN}✓ assets/ ($ASSET_COUNT files)${NC}"
else
    echo -e "${RED}✗ assets/ (missing)${NC}"
fi

echo ""
echo -e "${BLUE}Next Steps:${NC}"
echo "==========="

case $ENVIRONMENT in
    "production")
        echo "1. Configure your web server (Nginx) to serve files from:"
        echo "   $(pwd)/$BUILD_DIR"
        echo ""
        echo "2. Update your Django CORS settings for production domain"
        echo ""
        echo "3. Test the deployment:"
        echo "   • Check that static files load correctly"
        echo "   • Verify API calls work with relative paths"
        echo "   • Test all application functionality"
        ;;
    "staging")
        echo "1. Deploy to your staging server"
        echo "2. Test all functionality before production"
        ;;
    *)
        echo "1. Test the build locally:"
        echo "   cd $FRONTEND_DIR && npm run preview"
        echo ""
        echo "2. Verify API connectivity"
        ;;
esac

echo ""
echo -e "${GREEN}🎉 Frontend deployment preparation complete!${NC}"

# Optional: Start preview server for testing
if [ "$ENVIRONMENT" != "production" ]; then
    echo ""
    read -p "Would you like to start a preview server to test the build? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}Starting preview server...${NC}"
        cd "$FRONTEND_DIR"
        npm run preview
    fi
fi

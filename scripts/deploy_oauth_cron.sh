#!/bin/bash

# Quick OAuth Cron Deployment Script for Production Servers
# Usage: ./scripts/deploy_oauth_cron.sh [admin-email]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}OAuth Token Monitoring - Quick Deployment${NC}"
echo "=============================================="

# Get admin email
ADMIN_EMAIL="${1}"
if [ -z "$ADMIN_EMAIL" ]; then
    echo -e "${YELLOW}Please provide an admin email address:${NC}"
    read -p "Admin email: " ADMIN_EMAIL
    if [ -z "$ADMIN_EMAIL" ]; then
        echo -e "${RED}Error: Admin email is required${NC}"
        exit 1
    fi
fi

echo "Admin email: $ADMIN_EMAIL"
echo ""

# Check if we're in the right directory
if [ ! -f "manage.py" ]; then
    echo -e "${RED}Error: manage.py not found. Please run this script from your Django project root.${NC}"
    exit 1
fi

# Check if OAuth files exist
if [ ! -f "accounts/credentials.json" ]; then
    echo -e "${RED}Error: accounts/credentials.json not found. Please set up OAuth first.${NC}"
    exit 1
fi

if [ ! -f "accounts/token.json" ]; then
    echo -e "${RED}Error: accounts/token.json not found. Please run 'python manage.py setup_oauth' first.${NC}"
    exit 1
fi

echo -e "${GREEN}✓ OAuth files found${NC}"

# Make setup script executable
chmod +x scripts/setup_oauth_cron.sh

echo -e "${GREEN}✓ Setup script made executable${NC}"

# Run the setup
echo -e "${YELLOW}Running OAuth cron setup...${NC}"
./scripts/setup_oauth_cron.sh "$ADMIN_EMAIL"

# Extract the cron command from the setup output
CRON_SCRIPT="$(pwd)/scripts/oauth_cron_job.sh"
CRON_COMMAND="0 9 * * * $CRON_SCRIPT"

echo ""
echo -e "${YELLOW}Installing cron job...${NC}"

# Install the cron job
if (crontab -l 2>/dev/null | grep -v "$CRON_SCRIPT"; echo "$CRON_COMMAND") | crontab -; then
    echo -e "${GREEN}✓ Cron job installed successfully${NC}"
else
    echo -e "${RED}✗ Failed to install cron job${NC}"
    echo "Please run this command manually:"
    echo "  (crontab -l 2>/dev/null; echo \"$CRON_COMMAND\") | crontab -"
    exit 1
fi

# Verify installation
echo ""
echo -e "${YELLOW}Verifying installation...${NC}"

if crontab -l | grep -q "$CRON_SCRIPT"; then
    echo -e "${GREEN}✓ Cron job verified in crontab${NC}"
else
    echo -e "${RED}✗ Cron job not found in crontab${NC}"
    exit 1
fi

# Test the system
echo ""
echo -e "${YELLOW}Testing OAuth system...${NC}"

if python manage.py oauth_cron_check --alert-email "$ADMIN_EMAIL" --quiet; then
    echo -e "${GREEN}✓ OAuth system test passed${NC}"
else
    echo -e "${RED}✗ OAuth system test failed${NC}"
    echo "Please check your OAuth configuration and try again."
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 OAuth Token Monitoring Successfully Deployed!${NC}"
echo ""
echo "Summary:"
echo "========"
echo "• Cron job: Daily at 9:00 AM"
echo "• Alert email: $ADMIN_EMAIL"
echo "• Log file: $(pwd)/logs/oauth_cron.log"
echo "• Cron script: $CRON_SCRIPT"
echo ""
echo "Commands:"
echo "• View logs: tail -f logs/oauth_cron.log"
echo "• Check cron: crontab -l"
echo "• Test manually: python manage.py oauth_cron_check"
echo "• Check status: python manage.py check_oauth_status"
echo ""
echo -e "${GREEN}The system will now automatically monitor your OAuth tokens!${NC}"

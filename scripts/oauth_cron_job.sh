#!/bin/bash

# OAuth Token Monitoring Cron Job
# Generated on: Sat Nov  8 15:00:02 IST 2025

# Set environment variables
export PATH="/usr/local/bin:/usr/bin:/bin"
export DJANGO_SETTINGS_MODULE="amportal_backend.settings"

# Change to project directory
cd "/Users/<USER>/dev/projects/amportal"

# Activate virtual environment and run the OAuth check
source "/Users/<USER>/dev/projects/amportal/env/bin/activate"

# Run the OAuth check with logging
echo "=== OAuth Cron Check - $(date) ===" >> "/Users/<USER>/dev/projects/amportal/logs/oauth_cron.log"
"/Users/<USER>/dev/projects/amportal/env/bin/python" "/Users/<USER>/dev/projects/amportal/manage.py" oauth_cron_check \
    --refresh-threshold-hours 24 \
    --alert-email "<EMAIL>" \
    --quiet >> "/Users/<USER>/dev/projects/amportal/logs/oauth_cron.log" 2>&1

# Log completion
echo "OAuth cron check completed at $(date)" >> "/Users/<USER>/dev/projects/amportal/logs/oauth_cron.log"
echo "" >> "/Users/<USER>/dev/projects/amportal/logs/oauth_cron.log"

#!/bin/bash

# OAuth Token Monitoring Cron Job Setup Script
# This script sets up automated OAuth token checking and refreshing
# Works on any server - automatically detects project directory

set -e

# Auto-detect project directory (script should be run from project root)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Configuration - auto-detect paths
PYTHON_PATH="$PROJECT_DIR/env/bin/python"
MANAGE_PY="$PROJECT_DIR/manage.py"
LOG_DIR="$PROJECT_DIR/logs"
CRON_LOG="$LOG_DIR/oauth_cron.log"

# Default alert email (can be overridden)
ALERT_EMAIL="${1:-<EMAIL>}"

# Detect if we're on a different system and adjust paths
if [ ! -f "$PYTHON_PATH" ]; then
    # Try common virtual environment locations
    if [ -f "$PROJECT_DIR/venv/bin/python" ]; then
        PYTHON_PATH="$PROJECT_DIR/venv/bin/python"
    elif [ -f "$PROJECT_DIR/.venv/bin/python" ]; then
        PYTHON_PATH="$PROJECT_DIR/.venv/bin/python"
    elif command -v python3 >/dev/null 2>&1; then
        PYTHON_PATH="python3"
        echo "Warning: Using system python3. Consider using a virtual environment."
    else
        echo "Error: Python not found. Please ensure Python is installed and accessible."
        exit 1
    fi
fi

echo "Setting up OAuth Token Monitoring Cron Job"
echo "=========================================="
echo "Project Directory: $PROJECT_DIR"
echo "Python Path: $PYTHON_PATH"
echo "Alert Email: $ALERT_EMAIL"
echo ""

# Create logs directory if it doesn't exist
if [ ! -d "$LOG_DIR" ]; then
    echo "Creating logs directory: $LOG_DIR"
    mkdir -p "$LOG_DIR"
fi

# Create the cron script
CRON_SCRIPT="$PROJECT_DIR/scripts/oauth_cron_job.sh"
echo "Creating cron script: $CRON_SCRIPT"

cat > "$CRON_SCRIPT" << EOF
#!/bin/bash

# OAuth Token Monitoring Cron Job
# Generated on: $(date)
# Project: $PROJECT_DIR

# Set environment variables
export PATH="/usr/local/bin:/usr/bin:/bin:/sbin"
export DJANGO_SETTINGS_MODULE="amportal_backend.settings"

# Change to project directory
cd "$PROJECT_DIR"

# Activate virtual environment if it exists
if [ -f "$PROJECT_DIR/env/bin/activate" ]; then
    source "$PROJECT_DIR/env/bin/activate"
elif [ -f "$PROJECT_DIR/venv/bin/activate" ]; then
    source "$PROJECT_DIR/venv/bin/activate"
elif [ -f "$PROJECT_DIR/.venv/bin/activate" ]; then
    source "$PROJECT_DIR/.venv/bin/activate"
fi

# Ensure log directory exists
mkdir -p "$LOG_DIR"

# Run the OAuth check with logging
echo "=== OAuth Cron Check - \$(date) ===" >> "$CRON_LOG"
"$PYTHON_PATH" "$MANAGE_PY" oauth_cron_check \\
    --refresh-threshold-hours 24 \\
    --alert-email "$ALERT_EMAIL" \\
    --quiet >> "$CRON_LOG" 2>&1

# Log completion
echo "OAuth cron check completed at \$(date)" >> "$CRON_LOG"
echo "" >> "$CRON_LOG"
EOF

# Make the cron script executable
chmod +x "$CRON_SCRIPT"

# Create the cron entry
CRON_ENTRY="0 9 * * * $CRON_SCRIPT"

echo "Cron script created successfully!"
echo ""
echo "To install the cron job, run:"
echo "  crontab -e"
echo ""
echo "Then add this line:"
echo "  $CRON_ENTRY"
echo ""
echo "Or run this command to add it automatically:"
echo "  (crontab -l 2>/dev/null; echo \"$CRON_ENTRY\") | crontab -"
echo ""

# Test the cron script
echo "Testing the cron script..."
echo "Running: $CRON_SCRIPT"
echo ""

# Check if required files exist
if [ ! -f "$MANAGE_PY" ]; then
    echo "❌ Error: manage.py not found at $MANAGE_PY"
    echo "Please run this script from your Django project root directory."
    exit 1
fi

# Test the OAuth command
cd "$PROJECT_DIR"

# Activate virtual environment if it exists
if [ -f "$PROJECT_DIR/env/bin/activate" ]; then
    source "$PROJECT_DIR/env/bin/activate"
elif [ -f "$PROJECT_DIR/venv/bin/activate" ]; then
    source "$PROJECT_DIR/venv/bin/activate"
elif [ -f "$PROJECT_DIR/.venv/bin/activate" ]; then
    source "$PROJECT_DIR/.venv/bin/activate"
fi

# Test the command
if "$PYTHON_PATH" "$MANAGE_PY" oauth_cron_check --alert-email "$ALERT_EMAIL"; then
    echo ""
    echo "✅ Test completed successfully!"
else
    echo ""
    echo "❌ Test failed. Please check your Django setup and OAuth configuration."
    exit 1
fi

echo ""
echo "Setup Summary:"
echo "=============="
echo "• Cron script: $CRON_SCRIPT"
echo "• Log file: $CRON_LOG"
echo "• Schedule: Daily at 9:00 AM"
echo "• Alert email: $ALERT_EMAIL"
echo "• Refresh threshold: 24 hours"
echo ""
echo "The cron job will:"
echo "• Test refresh token health daily"
echo "• Detect if refresh token is expired/invalid"
echo "• Automatically refresh access tokens as needed"
echo "• Send email alerts if refresh token fails"
echo "• Log all activities to $CRON_LOG"
echo ""
echo "To view logs: tail -f $CRON_LOG"
echo "To check cron status: crontab -l"
echo ""
echo "Manual commands:"
echo "• Check token status: python manage.py check_oauth_status"
echo "• Run cron check manually: python manage.py oauth_cron_check"
echo "• Refresh OAuth setup: python manage.py setup_oauth"

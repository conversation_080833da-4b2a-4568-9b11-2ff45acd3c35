from django.urls import include, path
from .views import *


urlpatterns = [
   path('dashboard/', StaffDashboardView.as_view(), name='staff_dashboard'),
   path('dashboard-stats/', StaffDashboardStatsView.as_view(), name='staff-dashboard-stats'),
   path('register-client/', RegisterClient.as_view(), name='register_client'),
   path('create-folio/', FolioMethods.as_view(), name='folio_methods'),
   path('search-clients/', SearchClientDataForStaff.as_view(), name='search_clients_staff'),
   path('upload/', FileUploadView.as_view(), name='file-upload'),
   path('download-document/<int:doc_id>/', DownloadDocumentView.as_view(), name='download-document'),
   path('documents/', StaffDocumentManagerView.as_view(), name='staff-documents'),
]


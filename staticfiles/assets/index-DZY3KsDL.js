import{r as Y,j as b,B as K,T as Wg,a as et,b as Mt,I as Ns,P as Pg,c as hl,V as Jg,d as Ig,L as $g,A as le,e as Ct,C as $e,f as yf,D as Vs,g as Vn,h as Fs,i as me,k as Pe,l as Je,m as Cf,n as ge,o as dl,p as Fb,u as Rf,q as kf,s as Bf,M as Nf,G as Qt,F as Yv,t as Xv,v as ml,w as on,x as gl,y as pl,z as yn,E as at,H as bl,U as Gv,J as Yb,K as Qv,R as Lf,N as Xb,O as Gb,Q as Qb,S as Zb,W as el,X as nl,Y as il,Z as sl,_ as tp,$ as ep,a0 as np,a1 as Za,a2 as Zv,a3 as Kb,a4 as Kv,a5 as Wv,a6 as Pv}from"./mui-Ct6pHtrK.js";import{r as Jv,a as Iv,g as $v,c as Po}from"./vendor-c5ypKtDW.js";import{u as Wb,L as ye,N as Hs,O as Tr,a as t1,R as e1,b as Bt,B as n1}from"./router-DTrbUQnz.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))a(o);new MutationObserver(o=>{for(const c of o)if(c.type==="childList")for(const u of c.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&a(u)}).observe(document,{childList:!0,subtree:!0});function s(o){const c={};return o.integrity&&(c.integrity=o.integrity),o.referrerPolicy&&(c.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?c.credentials="include":o.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function a(o){if(o.ep)return;o.ep=!0;const c=s(o);fetch(o.href,c)}})();var Wu={exports:{}},Ka={},Pu={exports:{}},Ju={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ip;function i1(){return ip||(ip=1,(function(i){function e(k,Q){var P=k.length;k.push(Q);t:for(;0<P;){var xt=P-1>>>1,pt=k[xt];if(0<o(pt,Q))k[xt]=Q,k[P]=pt,P=xt;else break t}}function s(k){return k.length===0?null:k[0]}function a(k){if(k.length===0)return null;var Q=k[0],P=k.pop();if(P!==Q){k[0]=P;t:for(var xt=0,pt=k.length,Tt=pt>>>1;xt<Tt;){var ft=2*(xt+1)-1,ct=k[ft],_t=ft+1,Jt=k[_t];if(0>o(ct,P))_t<pt&&0>o(Jt,ct)?(k[xt]=Jt,k[_t]=P,xt=_t):(k[xt]=ct,k[ft]=P,xt=ft);else if(_t<pt&&0>o(Jt,P))k[xt]=Jt,k[_t]=P,xt=_t;else break t}}return Q}function o(k,Q){var P=k.sortIndex-Q.sortIndex;return P!==0?P:k.id-Q.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var c=performance;i.unstable_now=function(){return c.now()}}else{var u=Date,h=u.now();i.unstable_now=function(){return u.now()-h}}var m=[],g=[],p=1,x=null,v=3,S=!1,M=!1,T=!1,w=!1,O=typeof setTimeout=="function"?setTimeout:null,H=typeof clearTimeout=="function"?clearTimeout:null,q=typeof setImmediate<"u"?setImmediate:null;function G(k){for(var Q=s(g);Q!==null;){if(Q.callback===null)a(g);else if(Q.startTime<=k)a(g),Q.sortIndex=Q.expirationTime,e(m,Q);else break;Q=s(g)}}function V(k){if(T=!1,G(k),!M)if(s(m)!==null)M=!0,F||(F=!0,nt());else{var Q=s(g);Q!==null&&gt(V,Q.startTime-k)}}var F=!1,X=-1,Z=5,I=-1;function rt(){return w?!0:!(i.unstable_now()-I<Z)}function st(){if(w=!1,F){var k=i.unstable_now();I=k;var Q=!0;try{t:{M=!1,T&&(T=!1,H(X),X=-1),S=!0;var P=v;try{e:{for(G(k),x=s(m);x!==null&&!(x.expirationTime>k&&rt());){var xt=x.callback;if(typeof xt=="function"){x.callback=null,v=x.priorityLevel;var pt=xt(x.expirationTime<=k);if(k=i.unstable_now(),typeof pt=="function"){x.callback=pt,G(k),Q=!0;break e}x===s(m)&&a(m),G(k)}else a(m);x=s(m)}if(x!==null)Q=!0;else{var Tt=s(g);Tt!==null&&gt(V,Tt.startTime-k),Q=!1}}break t}finally{x=null,v=P,S=!1}Q=void 0}}finally{Q?nt():F=!1}}}var nt;if(typeof q=="function")nt=function(){q(st)};else if(typeof MessageChannel<"u"){var Nt=new MessageChannel,Ht=Nt.port2;Nt.port1.onmessage=st,nt=function(){Ht.postMessage(null)}}else nt=function(){O(st,0)};function gt(k,Q){X=O(function(){k(i.unstable_now())},Q)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(k){k.callback=null},i.unstable_forceFrameRate=function(k){0>k||125<k?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Z=0<k?Math.floor(1e3/k):5},i.unstable_getCurrentPriorityLevel=function(){return v},i.unstable_next=function(k){switch(v){case 1:case 2:case 3:var Q=3;break;default:Q=v}var P=v;v=Q;try{return k()}finally{v=P}},i.unstable_requestPaint=function(){w=!0},i.unstable_runWithPriority=function(k,Q){switch(k){case 1:case 2:case 3:case 4:case 5:break;default:k=3}var P=v;v=k;try{return Q()}finally{v=P}},i.unstable_scheduleCallback=function(k,Q,P){var xt=i.unstable_now();switch(typeof P=="object"&&P!==null?(P=P.delay,P=typeof P=="number"&&0<P?xt+P:xt):P=xt,k){case 1:var pt=-1;break;case 2:pt=250;break;case 5:pt=1073741823;break;case 4:pt=1e4;break;default:pt=5e3}return pt=P+pt,k={id:p++,callback:Q,priorityLevel:k,startTime:P,expirationTime:pt,sortIndex:-1},P>xt?(k.sortIndex=P,e(g,k),s(m)===null&&k===s(g)&&(T?(H(X),X=-1):T=!0,gt(V,P-xt))):(k.sortIndex=pt,e(m,k),M||S||(M=!0,F||(F=!0,nt()))),k},i.unstable_shouldYield=rt,i.unstable_wrapCallback=function(k){var Q=v;return function(){var P=v;v=Q;try{return k.apply(this,arguments)}finally{v=P}}}})(Ju)),Ju}var sp;function s1(){return sp||(sp=1,Pu.exports=i1()),Pu.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ap;function a1(){if(ap)return Ka;ap=1;var i=s1(),e=Jv(),s=Iv();function a(t){var n="https://react.dev/errors/"+t;if(1<arguments.length){n+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)n+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function c(t){var n=t,l=t;if(t.alternate)for(;n.return;)n=n.return;else{t=n;do n=t,(n.flags&4098)!==0&&(l=n.return),t=n.return;while(t)}return n.tag===3?l:null}function u(t){if(t.tag===13){var n=t.memoizedState;if(n===null&&(t=t.alternate,t!==null&&(n=t.memoizedState)),n!==null)return n.dehydrated}return null}function h(t){if(c(t)!==t)throw Error(a(188))}function m(t){var n=t.alternate;if(!n){if(n=c(t),n===null)throw Error(a(188));return n!==t?null:t}for(var l=t,r=n;;){var f=l.return;if(f===null)break;var d=f.alternate;if(d===null){if(r=f.return,r!==null){l=r;continue}break}if(f.child===d.child){for(d=f.child;d;){if(d===l)return h(f),t;if(d===r)return h(f),n;d=d.sibling}throw Error(a(188))}if(l.return!==r.return)l=f,r=d;else{for(var y=!1,_=f.child;_;){if(_===l){y=!0,l=f,r=d;break}if(_===r){y=!0,r=f,l=d;break}_=_.sibling}if(!y){for(_=d.child;_;){if(_===l){y=!0,l=d,r=f;break}if(_===r){y=!0,r=d,l=f;break}_=_.sibling}if(!y)throw Error(a(189))}}if(l.alternate!==r)throw Error(a(190))}if(l.tag!==3)throw Error(a(188));return l.stateNode.current===l?t:n}function g(t){var n=t.tag;if(n===5||n===26||n===27||n===6)return t;for(t=t.child;t!==null;){if(n=g(t),n!==null)return n;t=t.sibling}return null}var p=Object.assign,x=Symbol.for("react.element"),v=Symbol.for("react.transitional.element"),S=Symbol.for("react.portal"),M=Symbol.for("react.fragment"),T=Symbol.for("react.strict_mode"),w=Symbol.for("react.profiler"),O=Symbol.for("react.provider"),H=Symbol.for("react.consumer"),q=Symbol.for("react.context"),G=Symbol.for("react.forward_ref"),V=Symbol.for("react.suspense"),F=Symbol.for("react.suspense_list"),X=Symbol.for("react.memo"),Z=Symbol.for("react.lazy"),I=Symbol.for("react.activity"),rt=Symbol.for("react.memo_cache_sentinel"),st=Symbol.iterator;function nt(t){return t===null||typeof t!="object"?null:(t=st&&t[st]||t["@@iterator"],typeof t=="function"?t:null)}var Nt=Symbol.for("react.client.reference");function Ht(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Nt?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case M:return"Fragment";case w:return"Profiler";case T:return"StrictMode";case V:return"Suspense";case F:return"SuspenseList";case I:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case S:return"Portal";case q:return(t.displayName||"Context")+".Provider";case H:return(t._context.displayName||"Context")+".Consumer";case G:var n=t.render;return t=t.displayName,t||(t=n.displayName||n.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case X:return n=t.displayName||null,n!==null?n:Ht(t.type)||"Memo";case Z:n=t._payload,t=t._init;try{return Ht(t(n))}catch{}}return null}var gt=Array.isArray,k=e.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Q=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,P={pending:!1,data:null,method:null,action:null},xt=[],pt=-1;function Tt(t){return{current:t}}function ft(t){0>pt||(t.current=xt[pt],xt[pt]=null,pt--)}function ct(t,n){pt++,xt[pt]=t.current,t.current=n}var _t=Tt(null),Jt=Tt(null),ue=Tt(null),Cl=Tt(null);function Rl(t,n){switch(ct(ue,n),ct(Jt,t),ct(_t,null),n.nodeType){case 9:case 11:t=(t=n.documentElement)&&(t=t.namespaceURI)?Mg(t):0;break;default:if(t=n.tagName,n=n.namespaceURI)n=Mg(n),t=wg(n,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}ft(_t),ct(_t,t)}function Pi(){ft(_t),ft(Jt),ft(ue)}function kr(t){t.memoizedState!==null&&ct(Cl,t);var n=_t.current,l=wg(n,t.type);n!==l&&(ct(Jt,t),ct(_t,l))}function kl(t){Jt.current===t&&(ft(_t),ft(Jt)),Cl.current===t&&(ft(Cl),Fa._currentValue=P)}var Br=Object.prototype.hasOwnProperty,Nr=i.unstable_scheduleCallback,Lr=i.unstable_cancelCallback,_x=i.unstable_shouldYield,Sx=i.unstable_requestPaint,cn=i.unstable_now,Mx=i.unstable_getCurrentPriorityLevel,ih=i.unstable_ImmediatePriority,sh=i.unstable_UserBlockingPriority,Bl=i.unstable_NormalPriority,wx=i.unstable_LowPriority,ah=i.unstable_IdlePriority,Tx=i.log,Ax=i.unstable_setDisableYieldValue,Ps=null,Oe=null;function Fn(t){if(typeof Tx=="function"&&Ax(t),Oe&&typeof Oe.setStrictMode=="function")try{Oe.setStrictMode(Ps,t)}catch{}}var Ee=Math.clz32?Math.clz32:Ox,jx=Math.log,Dx=Math.LN2;function Ox(t){return t>>>=0,t===0?32:31-(jx(t)/Dx|0)|0}var Nl=256,Ll=4194304;function xi(t){var n=t&42;if(n!==0)return n;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Hl(t,n,l){var r=t.pendingLanes;if(r===0)return 0;var f=0,d=t.suspendedLanes,y=t.pingedLanes;t=t.warmLanes;var _=r&134217727;return _!==0?(r=_&~d,r!==0?f=xi(r):(y&=_,y!==0?f=xi(y):l||(l=_&~t,l!==0&&(f=xi(l))))):(_=r&~d,_!==0?f=xi(_):y!==0?f=xi(y):l||(l=r&~t,l!==0&&(f=xi(l)))),f===0?0:n!==0&&n!==f&&(n&d)===0&&(d=f&-f,l=n&-n,d>=l||d===32&&(l&4194048)!==0)?n:f}function Js(t,n){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&n)===0}function Ex(t,n){switch(t){case 1:case 2:case 4:case 8:case 64:return n+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function lh(){var t=Nl;return Nl<<=1,(Nl&4194048)===0&&(Nl=256),t}function oh(){var t=Ll;return Ll<<=1,(Ll&62914560)===0&&(Ll=4194304),t}function Hr(t){for(var n=[],l=0;31>l;l++)n.push(t);return n}function Is(t,n){t.pendingLanes|=n,n!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function zx(t,n,l,r,f,d){var y=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var _=t.entanglements,A=t.expirationTimes,z=t.hiddenUpdates;for(l=y&~l;0<l;){var N=31-Ee(l),U=1<<N;_[N]=0,A[N]=-1;var C=z[N];if(C!==null)for(z[N]=null,N=0;N<C.length;N++){var R=C[N];R!==null&&(R.lane&=-536870913)}l&=~U}r!==0&&rh(t,r,0),d!==0&&f===0&&t.tag!==0&&(t.suspendedLanes|=d&~(y&~n))}function rh(t,n,l){t.pendingLanes|=n,t.suspendedLanes&=~n;var r=31-Ee(n);t.entangledLanes|=n,t.entanglements[r]=t.entanglements[r]|1073741824|l&4194090}function ch(t,n){var l=t.entangledLanes|=n;for(t=t.entanglements;l;){var r=31-Ee(l),f=1<<r;f&n|t[r]&n&&(t[r]|=n),l&=~f}}function Ur(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function qr(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function uh(){var t=Q.p;return t!==0?t:(t=window.event,t===void 0?32:Yg(t.type))}function Cx(t,n){var l=Q.p;try{return Q.p=t,n()}finally{Q.p=l}}var Yn=Math.random().toString(36).slice(2),fe="__reactFiber$"+Yn,ve="__reactProps$"+Yn,Ji="__reactContainer$"+Yn,Vr="__reactEvents$"+Yn,Rx="__reactListeners$"+Yn,kx="__reactHandles$"+Yn,fh="__reactResources$"+Yn,$s="__reactMarker$"+Yn;function Fr(t){delete t[fe],delete t[ve],delete t[Vr],delete t[Rx],delete t[kx]}function Ii(t){var n=t[fe];if(n)return n;for(var l=t.parentNode;l;){if(n=l[Ji]||l[fe]){if(l=n.alternate,n.child!==null||l!==null&&l.child!==null)for(t=Dg(t);t!==null;){if(l=t[fe])return l;t=Dg(t)}return n}t=l,l=t.parentNode}return null}function $i(t){if(t=t[fe]||t[Ji]){var n=t.tag;if(n===5||n===6||n===13||n===26||n===27||n===3)return t}return null}function ta(t){var n=t.tag;if(n===5||n===26||n===27||n===6)return t.stateNode;throw Error(a(33))}function ts(t){var n=t[fh];return n||(n=t[fh]={hoistableStyles:new Map,hoistableScripts:new Map}),n}function ee(t){t[$s]=!0}var hh=new Set,dh={};function yi(t,n){es(t,n),es(t+"Capture",n)}function es(t,n){for(dh[t]=n,t=0;t<n.length;t++)hh.add(n[t])}var Bx=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),mh={},gh={};function Nx(t){return Br.call(gh,t)?!0:Br.call(mh,t)?!1:Bx.test(t)?gh[t]=!0:(mh[t]=!0,!1)}function Ul(t,n,l){if(Nx(n))if(l===null)t.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(n);return;case"boolean":var r=n.toLowerCase().slice(0,5);if(r!=="data-"&&r!=="aria-"){t.removeAttribute(n);return}}t.setAttribute(n,""+l)}}function ql(t,n,l){if(l===null)t.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttribute(n,""+l)}}function Sn(t,n,l,r){if(r===null)t.removeAttribute(l);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(n,l,""+r)}}var Yr,ph;function ns(t){if(Yr===void 0)try{throw Error()}catch(l){var n=l.stack.trim().match(/\n( *(at )?)/);Yr=n&&n[1]||"",ph=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Yr+t+ph}var Xr=!1;function Gr(t,n){if(!t||Xr)return"";Xr=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(n){var U=function(){throw Error()};if(Object.defineProperty(U.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(U,[])}catch(R){var C=R}Reflect.construct(t,[],U)}else{try{U.call()}catch(R){C=R}t.call(U.prototype)}}else{try{throw Error()}catch(R){C=R}(U=t())&&typeof U.catch=="function"&&U.catch(function(){})}}catch(R){if(R&&C&&typeof R.stack=="string")return[R.stack,C.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var f=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");f&&f.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var d=r.DetermineComponentFrameRoot(),y=d[0],_=d[1];if(y&&_){var A=y.split(`
`),z=_.split(`
`);for(f=r=0;r<A.length&&!A[r].includes("DetermineComponentFrameRoot");)r++;for(;f<z.length&&!z[f].includes("DetermineComponentFrameRoot");)f++;if(r===A.length||f===z.length)for(r=A.length-1,f=z.length-1;1<=r&&0<=f&&A[r]!==z[f];)f--;for(;1<=r&&0<=f;r--,f--)if(A[r]!==z[f]){if(r!==1||f!==1)do if(r--,f--,0>f||A[r]!==z[f]){var N=`
`+A[r].replace(" at new "," at ");return t.displayName&&N.includes("<anonymous>")&&(N=N.replace("<anonymous>",t.displayName)),N}while(1<=r&&0<=f);break}}}finally{Xr=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?ns(l):""}function Lx(t){switch(t.tag){case 26:case 27:case 5:return ns(t.type);case 16:return ns("Lazy");case 13:return ns("Suspense");case 19:return ns("SuspenseList");case 0:case 15:return Gr(t.type,!1);case 11:return Gr(t.type.render,!1);case 1:return Gr(t.type,!0);case 31:return ns("Activity");default:return""}}function bh(t){try{var n="";do n+=Lx(t),t=t.return;while(t);return n}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function qe(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function xh(t){var n=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(n==="checkbox"||n==="radio")}function Hx(t){var n=xh(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,n),r=""+t[n];if(!t.hasOwnProperty(n)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var f=l.get,d=l.set;return Object.defineProperty(t,n,{configurable:!0,get:function(){return f.call(this)},set:function(y){r=""+y,d.call(this,y)}}),Object.defineProperty(t,n,{enumerable:l.enumerable}),{getValue:function(){return r},setValue:function(y){r=""+y},stopTracking:function(){t._valueTracker=null,delete t[n]}}}}function Vl(t){t._valueTracker||(t._valueTracker=Hx(t))}function yh(t){if(!t)return!1;var n=t._valueTracker;if(!n)return!0;var l=n.getValue(),r="";return t&&(r=xh(t)?t.checked?"true":"false":t.value),t=r,t!==l?(n.setValue(t),!0):!1}function Fl(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var Ux=/[\n"\\]/g;function Ve(t){return t.replace(Ux,function(n){return"\\"+n.charCodeAt(0).toString(16)+" "})}function Qr(t,n,l,r,f,d,y,_){t.name="",y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?t.type=y:t.removeAttribute("type"),n!=null?y==="number"?(n===0&&t.value===""||t.value!=n)&&(t.value=""+qe(n)):t.value!==""+qe(n)&&(t.value=""+qe(n)):y!=="submit"&&y!=="reset"||t.removeAttribute("value"),n!=null?Zr(t,y,qe(n)):l!=null?Zr(t,y,qe(l)):r!=null&&t.removeAttribute("value"),f==null&&d!=null&&(t.defaultChecked=!!d),f!=null&&(t.checked=f&&typeof f!="function"&&typeof f!="symbol"),_!=null&&typeof _!="function"&&typeof _!="symbol"&&typeof _!="boolean"?t.name=""+qe(_):t.removeAttribute("name")}function vh(t,n,l,r,f,d,y,_){if(d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(t.type=d),n!=null||l!=null){if(!(d!=="submit"&&d!=="reset"||n!=null))return;l=l!=null?""+qe(l):"",n=n!=null?""+qe(n):l,_||n===t.value||(t.value=n),t.defaultValue=n}r=r??f,r=typeof r!="function"&&typeof r!="symbol"&&!!r,t.checked=_?t.checked:!!r,t.defaultChecked=!!r,y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"&&(t.name=y)}function Zr(t,n,l){n==="number"&&Fl(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function is(t,n,l,r){if(t=t.options,n){n={};for(var f=0;f<l.length;f++)n["$"+l[f]]=!0;for(l=0;l<t.length;l++)f=n.hasOwnProperty("$"+t[l].value),t[l].selected!==f&&(t[l].selected=f),f&&r&&(t[l].defaultSelected=!0)}else{for(l=""+qe(l),n=null,f=0;f<t.length;f++){if(t[f].value===l){t[f].selected=!0,r&&(t[f].defaultSelected=!0);return}n!==null||t[f].disabled||(n=t[f])}n!==null&&(n.selected=!0)}}function _h(t,n,l){if(n!=null&&(n=""+qe(n),n!==t.value&&(t.value=n),l==null)){t.defaultValue!==n&&(t.defaultValue=n);return}t.defaultValue=l!=null?""+qe(l):""}function Sh(t,n,l,r){if(n==null){if(r!=null){if(l!=null)throw Error(a(92));if(gt(r)){if(1<r.length)throw Error(a(93));r=r[0]}l=r}l==null&&(l=""),n=l}l=qe(n),t.defaultValue=l,r=t.textContent,r===l&&r!==""&&r!==null&&(t.value=r)}function ss(t,n){if(n){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=n;return}}t.textContent=n}var qx=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Mh(t,n,l){var r=n.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?r?t.setProperty(n,""):n==="float"?t.cssFloat="":t[n]="":r?t.setProperty(n,l):typeof l!="number"||l===0||qx.has(n)?n==="float"?t.cssFloat=l:t[n]=(""+l).trim():t[n]=l+"px"}function wh(t,n,l){if(n!=null&&typeof n!="object")throw Error(a(62));if(t=t.style,l!=null){for(var r in l)!l.hasOwnProperty(r)||n!=null&&n.hasOwnProperty(r)||(r.indexOf("--")===0?t.setProperty(r,""):r==="float"?t.cssFloat="":t[r]="");for(var f in n)r=n[f],n.hasOwnProperty(f)&&l[f]!==r&&Mh(t,f,r)}else for(var d in n)n.hasOwnProperty(d)&&Mh(t,d,n[d])}function Kr(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Vx=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Fx=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Yl(t){return Fx.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Wr=null;function Pr(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var as=null,ls=null;function Th(t){var n=$i(t);if(n&&(t=n.stateNode)){var l=t[ve]||null;t:switch(t=n.stateNode,n.type){case"input":if(Qr(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),n=l.name,l.type==="radio"&&n!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+Ve(""+n)+'"][type="radio"]'),n=0;n<l.length;n++){var r=l[n];if(r!==t&&r.form===t.form){var f=r[ve]||null;if(!f)throw Error(a(90));Qr(r,f.value,f.defaultValue,f.defaultValue,f.checked,f.defaultChecked,f.type,f.name)}}for(n=0;n<l.length;n++)r=l[n],r.form===t.form&&yh(r)}break t;case"textarea":_h(t,l.value,l.defaultValue);break t;case"select":n=l.value,n!=null&&is(t,!!l.multiple,n,!1)}}}var Jr=!1;function Ah(t,n,l){if(Jr)return t(n,l);Jr=!0;try{var r=t(n);return r}finally{if(Jr=!1,(as!==null||ls!==null)&&(Oo(),as&&(n=as,t=ls,ls=as=null,Th(n),t)))for(n=0;n<t.length;n++)Th(t[n])}}function ea(t,n){var l=t.stateNode;if(l===null)return null;var r=l[ve]||null;if(r===null)return null;l=r[n];t:switch(n){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(t=t.type,r=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!r;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(a(231,n,typeof l));return l}var Mn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ir=!1;if(Mn)try{var na={};Object.defineProperty(na,"passive",{get:function(){Ir=!0}}),window.addEventListener("test",na,na),window.removeEventListener("test",na,na)}catch{Ir=!1}var Xn=null,$r=null,Xl=null;function jh(){if(Xl)return Xl;var t,n=$r,l=n.length,r,f="value"in Xn?Xn.value:Xn.textContent,d=f.length;for(t=0;t<l&&n[t]===f[t];t++);var y=l-t;for(r=1;r<=y&&n[l-r]===f[d-r];r++);return Xl=f.slice(t,1<r?1-r:void 0)}function Gl(t){var n=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&n===13&&(t=13)):t=n,t===10&&(t=13),32<=t||t===13?t:0}function Ql(){return!0}function Dh(){return!1}function _e(t){function n(l,r,f,d,y){this._reactName=l,this._targetInst=f,this.type=r,this.nativeEvent=d,this.target=y,this.currentTarget=null;for(var _ in t)t.hasOwnProperty(_)&&(l=t[_],this[_]=l?l(d):d[_]);return this.isDefaultPrevented=(d.defaultPrevented!=null?d.defaultPrevented:d.returnValue===!1)?Ql:Dh,this.isPropagationStopped=Dh,this}return p(n.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=Ql)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=Ql)},persist:function(){},isPersistent:Ql}),n}var vi={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zl=_e(vi),ia=p({},vi,{view:0,detail:0}),Yx=_e(ia),tc,ec,sa,Kl=p({},ia,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ic,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==sa&&(sa&&t.type==="mousemove"?(tc=t.screenX-sa.screenX,ec=t.screenY-sa.screenY):ec=tc=0,sa=t),tc)},movementY:function(t){return"movementY"in t?t.movementY:ec}}),Oh=_e(Kl),Xx=p({},Kl,{dataTransfer:0}),Gx=_e(Xx),Qx=p({},ia,{relatedTarget:0}),nc=_e(Qx),Zx=p({},vi,{animationName:0,elapsedTime:0,pseudoElement:0}),Kx=_e(Zx),Wx=p({},vi,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Px=_e(Wx),Jx=p({},vi,{data:0}),Eh=_e(Jx),Ix={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},$x={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ty={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ey(t){var n=this.nativeEvent;return n.getModifierState?n.getModifierState(t):(t=ty[t])?!!n[t]:!1}function ic(){return ey}var ny=p({},ia,{key:function(t){if(t.key){var n=Ix[t.key]||t.key;if(n!=="Unidentified")return n}return t.type==="keypress"?(t=Gl(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?$x[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ic,charCode:function(t){return t.type==="keypress"?Gl(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Gl(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),iy=_e(ny),sy=p({},Kl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),zh=_e(sy),ay=p({},ia,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ic}),ly=_e(ay),oy=p({},vi,{propertyName:0,elapsedTime:0,pseudoElement:0}),ry=_e(oy),cy=p({},Kl,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),uy=_e(cy),fy=p({},vi,{newState:0,oldState:0}),hy=_e(fy),dy=[9,13,27,32],sc=Mn&&"CompositionEvent"in window,aa=null;Mn&&"documentMode"in document&&(aa=document.documentMode);var my=Mn&&"TextEvent"in window&&!aa,Ch=Mn&&(!sc||aa&&8<aa&&11>=aa),Rh=" ",kh=!1;function Bh(t,n){switch(t){case"keyup":return dy.indexOf(n.keyCode)!==-1;case"keydown":return n.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Nh(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var os=!1;function gy(t,n){switch(t){case"compositionend":return Nh(n);case"keypress":return n.which!==32?null:(kh=!0,Rh);case"textInput":return t=n.data,t===Rh&&kh?null:t;default:return null}}function py(t,n){if(os)return t==="compositionend"||!sc&&Bh(t,n)?(t=jh(),Xl=$r=Xn=null,os=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(n.ctrlKey||n.altKey||n.metaKey)||n.ctrlKey&&n.altKey){if(n.char&&1<n.char.length)return n.char;if(n.which)return String.fromCharCode(n.which)}return null;case"compositionend":return Ch&&n.locale!=="ko"?null:n.data;default:return null}}var by={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Lh(t){var n=t&&t.nodeName&&t.nodeName.toLowerCase();return n==="input"?!!by[t.type]:n==="textarea"}function Hh(t,n,l,r){as?ls?ls.push(r):ls=[r]:as=r,n=Bo(n,"onChange"),0<n.length&&(l=new Zl("onChange","change",null,l,r),t.push({event:l,listeners:n}))}var la=null,oa=null;function xy(t){xg(t,0)}function Wl(t){var n=ta(t);if(yh(n))return t}function Uh(t,n){if(t==="change")return n}var qh=!1;if(Mn){var ac;if(Mn){var lc="oninput"in document;if(!lc){var Vh=document.createElement("div");Vh.setAttribute("oninput","return;"),lc=typeof Vh.oninput=="function"}ac=lc}else ac=!1;qh=ac&&(!document.documentMode||9<document.documentMode)}function Fh(){la&&(la.detachEvent("onpropertychange",Yh),oa=la=null)}function Yh(t){if(t.propertyName==="value"&&Wl(oa)){var n=[];Hh(n,oa,t,Pr(t)),Ah(xy,n)}}function yy(t,n,l){t==="focusin"?(Fh(),la=n,oa=l,la.attachEvent("onpropertychange",Yh)):t==="focusout"&&Fh()}function vy(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Wl(oa)}function _y(t,n){if(t==="click")return Wl(n)}function Sy(t,n){if(t==="input"||t==="change")return Wl(n)}function My(t,n){return t===n&&(t!==0||1/t===1/n)||t!==t&&n!==n}var ze=typeof Object.is=="function"?Object.is:My;function ra(t,n){if(ze(t,n))return!0;if(typeof t!="object"||t===null||typeof n!="object"||n===null)return!1;var l=Object.keys(t),r=Object.keys(n);if(l.length!==r.length)return!1;for(r=0;r<l.length;r++){var f=l[r];if(!Br.call(n,f)||!ze(t[f],n[f]))return!1}return!0}function Xh(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Gh(t,n){var l=Xh(t);t=0;for(var r;l;){if(l.nodeType===3){if(r=t+l.textContent.length,t<=n&&r>=n)return{node:l,offset:n-t};t=r}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=Xh(l)}}function Qh(t,n){return t&&n?t===n?!0:t&&t.nodeType===3?!1:n&&n.nodeType===3?Qh(t,n.parentNode):"contains"in t?t.contains(n):t.compareDocumentPosition?!!(t.compareDocumentPosition(n)&16):!1:!1}function Zh(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var n=Fl(t.document);n instanceof t.HTMLIFrameElement;){try{var l=typeof n.contentWindow.location.href=="string"}catch{l=!1}if(l)t=n.contentWindow;else break;n=Fl(t.document)}return n}function oc(t){var n=t&&t.nodeName&&t.nodeName.toLowerCase();return n&&(n==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||n==="textarea"||t.contentEditable==="true")}var wy=Mn&&"documentMode"in document&&11>=document.documentMode,rs=null,rc=null,ca=null,cc=!1;function Kh(t,n,l){var r=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;cc||rs==null||rs!==Fl(r)||(r=rs,"selectionStart"in r&&oc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),ca&&ra(ca,r)||(ca=r,r=Bo(rc,"onSelect"),0<r.length&&(n=new Zl("onSelect","select",null,n,l),t.push({event:n,listeners:r}),n.target=rs)))}function _i(t,n){var l={};return l[t.toLowerCase()]=n.toLowerCase(),l["Webkit"+t]="webkit"+n,l["Moz"+t]="moz"+n,l}var cs={animationend:_i("Animation","AnimationEnd"),animationiteration:_i("Animation","AnimationIteration"),animationstart:_i("Animation","AnimationStart"),transitionrun:_i("Transition","TransitionRun"),transitionstart:_i("Transition","TransitionStart"),transitioncancel:_i("Transition","TransitionCancel"),transitionend:_i("Transition","TransitionEnd")},uc={},Wh={};Mn&&(Wh=document.createElement("div").style,"AnimationEvent"in window||(delete cs.animationend.animation,delete cs.animationiteration.animation,delete cs.animationstart.animation),"TransitionEvent"in window||delete cs.transitionend.transition);function Si(t){if(uc[t])return uc[t];if(!cs[t])return t;var n=cs[t],l;for(l in n)if(n.hasOwnProperty(l)&&l in Wh)return uc[t]=n[l];return t}var Ph=Si("animationend"),Jh=Si("animationiteration"),Ih=Si("animationstart"),Ty=Si("transitionrun"),Ay=Si("transitionstart"),jy=Si("transitioncancel"),$h=Si("transitionend"),td=new Map,fc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");fc.push("scrollEnd");function nn(t,n){td.set(t,n),yi(n,[t])}var ed=new WeakMap;function Fe(t,n){if(typeof t=="object"&&t!==null){var l=ed.get(t);return l!==void 0?l:(n={value:t,source:n,stack:bh(n)},ed.set(t,n),n)}return{value:t,source:n,stack:bh(n)}}var Ye=[],us=0,hc=0;function Pl(){for(var t=us,n=hc=us=0;n<t;){var l=Ye[n];Ye[n++]=null;var r=Ye[n];Ye[n++]=null;var f=Ye[n];Ye[n++]=null;var d=Ye[n];if(Ye[n++]=null,r!==null&&f!==null){var y=r.pending;y===null?f.next=f:(f.next=y.next,y.next=f),r.pending=f}d!==0&&nd(l,f,d)}}function Jl(t,n,l,r){Ye[us++]=t,Ye[us++]=n,Ye[us++]=l,Ye[us++]=r,hc|=r,t.lanes|=r,t=t.alternate,t!==null&&(t.lanes|=r)}function dc(t,n,l,r){return Jl(t,n,l,r),Il(t)}function fs(t,n){return Jl(t,null,null,n),Il(t)}function nd(t,n,l){t.lanes|=l;var r=t.alternate;r!==null&&(r.lanes|=l);for(var f=!1,d=t.return;d!==null;)d.childLanes|=l,r=d.alternate,r!==null&&(r.childLanes|=l),d.tag===22&&(t=d.stateNode,t===null||t._visibility&1||(f=!0)),t=d,d=d.return;return t.tag===3?(d=t.stateNode,f&&n!==null&&(f=31-Ee(l),t=d.hiddenUpdates,r=t[f],r===null?t[f]=[n]:r.push(n),n.lane=l|536870912),d):null}function Il(t){if(50<ka)throw ka=0,yu=null,Error(a(185));for(var n=t.return;n!==null;)t=n,n=t.return;return t.tag===3?t.stateNode:null}var hs={};function Dy(t,n,l,r){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ce(t,n,l,r){return new Dy(t,n,l,r)}function mc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function wn(t,n){var l=t.alternate;return l===null?(l=Ce(t.tag,n,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=n,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,n=t.dependencies,l.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function id(t,n){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=n,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,n=l.dependencies,t.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext}),t}function $l(t,n,l,r,f,d){var y=0;if(r=t,typeof t=="function")mc(t)&&(y=1);else if(typeof t=="string")y=Ev(t,l,_t.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case I:return t=Ce(31,l,n,f),t.elementType=I,t.lanes=d,t;case M:return Mi(l.children,f,d,n);case T:y=8,f|=24;break;case w:return t=Ce(12,l,n,f|2),t.elementType=w,t.lanes=d,t;case V:return t=Ce(13,l,n,f),t.elementType=V,t.lanes=d,t;case F:return t=Ce(19,l,n,f),t.elementType=F,t.lanes=d,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case O:case q:y=10;break t;case H:y=9;break t;case G:y=11;break t;case X:y=14;break t;case Z:y=16,r=null;break t}y=29,l=Error(a(130,t===null?"null":typeof t,"")),r=null}return n=Ce(y,l,n,f),n.elementType=t,n.type=r,n.lanes=d,n}function Mi(t,n,l,r){return t=Ce(7,t,r,n),t.lanes=l,t}function gc(t,n,l){return t=Ce(6,t,null,n),t.lanes=l,t}function pc(t,n,l){return n=Ce(4,t.children!==null?t.children:[],t.key,n),n.lanes=l,n.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},n}var ds=[],ms=0,to=null,eo=0,Xe=[],Ge=0,wi=null,Tn=1,An="";function Ti(t,n){ds[ms++]=eo,ds[ms++]=to,to=t,eo=n}function sd(t,n,l){Xe[Ge++]=Tn,Xe[Ge++]=An,Xe[Ge++]=wi,wi=t;var r=Tn;t=An;var f=32-Ee(r)-1;r&=~(1<<f),l+=1;var d=32-Ee(n)+f;if(30<d){var y=f-f%5;d=(r&(1<<y)-1).toString(32),r>>=y,f-=y,Tn=1<<32-Ee(n)+f|l<<f|r,An=d+t}else Tn=1<<d|l<<f|r,An=t}function bc(t){t.return!==null&&(Ti(t,1),sd(t,1,0))}function xc(t){for(;t===to;)to=ds[--ms],ds[ms]=null,eo=ds[--ms],ds[ms]=null;for(;t===wi;)wi=Xe[--Ge],Xe[Ge]=null,An=Xe[--Ge],Xe[Ge]=null,Tn=Xe[--Ge],Xe[Ge]=null}var xe=null,Ft=null,St=!1,Ai=null,un=!1,yc=Error(a(519));function ji(t){var n=Error(a(418,""));throw ha(Fe(n,t)),yc}function ad(t){var n=t.stateNode,l=t.type,r=t.memoizedProps;switch(n[fe]=t,n[ve]=r,l){case"dialog":dt("cancel",n),dt("close",n);break;case"iframe":case"object":case"embed":dt("load",n);break;case"video":case"audio":for(l=0;l<Na.length;l++)dt(Na[l],n);break;case"source":dt("error",n);break;case"img":case"image":case"link":dt("error",n),dt("load",n);break;case"details":dt("toggle",n);break;case"input":dt("invalid",n),vh(n,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),Vl(n);break;case"select":dt("invalid",n);break;case"textarea":dt("invalid",n),Sh(n,r.value,r.defaultValue,r.children),Vl(n)}l=r.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||n.textContent===""+l||r.suppressHydrationWarning===!0||Sg(n.textContent,l)?(r.popover!=null&&(dt("beforetoggle",n),dt("toggle",n)),r.onScroll!=null&&dt("scroll",n),r.onScrollEnd!=null&&dt("scrollend",n),r.onClick!=null&&(n.onclick=No),n=!0):n=!1,n||ji(t)}function ld(t){for(xe=t.return;xe;)switch(xe.tag){case 5:case 13:un=!1;return;case 27:case 3:un=!0;return;default:xe=xe.return}}function ua(t){if(t!==xe)return!1;if(!St)return ld(t),St=!0,!1;var n=t.tag,l;if((l=n!==3&&n!==27)&&((l=n===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||Bu(t.type,t.memoizedProps)),l=!l),l&&Ft&&ji(t),ld(t),n===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(a(317));t:{for(t=t.nextSibling,n=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(n===0){Ft=an(t.nextSibling);break t}n--}else l!=="$"&&l!=="$!"&&l!=="$?"||n++;t=t.nextSibling}Ft=null}}else n===27?(n=Ft,li(t.type)?(t=Uu,Uu=null,Ft=t):Ft=n):Ft=xe?an(t.stateNode.nextSibling):null;return!0}function fa(){Ft=xe=null,St=!1}function od(){var t=Ai;return t!==null&&(we===null?we=t:we.push.apply(we,t),Ai=null),t}function ha(t){Ai===null?Ai=[t]:Ai.push(t)}var vc=Tt(null),Di=null,jn=null;function Gn(t,n,l){ct(vc,n._currentValue),n._currentValue=l}function Dn(t){t._currentValue=vc.current,ft(vc)}function _c(t,n,l){for(;t!==null;){var r=t.alternate;if((t.childLanes&n)!==n?(t.childLanes|=n,r!==null&&(r.childLanes|=n)):r!==null&&(r.childLanes&n)!==n&&(r.childLanes|=n),t===l)break;t=t.return}}function Sc(t,n,l,r){var f=t.child;for(f!==null&&(f.return=t);f!==null;){var d=f.dependencies;if(d!==null){var y=f.child;d=d.firstContext;t:for(;d!==null;){var _=d;d=f;for(var A=0;A<n.length;A++)if(_.context===n[A]){d.lanes|=l,_=d.alternate,_!==null&&(_.lanes|=l),_c(d.return,l,t),r||(y=null);break t}d=_.next}}else if(f.tag===18){if(y=f.return,y===null)throw Error(a(341));y.lanes|=l,d=y.alternate,d!==null&&(d.lanes|=l),_c(y,l,t),y=null}else y=f.child;if(y!==null)y.return=f;else for(y=f;y!==null;){if(y===t){y=null;break}if(f=y.sibling,f!==null){f.return=y.return,y=f;break}y=y.return}f=y}}function da(t,n,l,r){t=null;for(var f=n,d=!1;f!==null;){if(!d){if((f.flags&524288)!==0)d=!0;else if((f.flags&262144)!==0)break}if(f.tag===10){var y=f.alternate;if(y===null)throw Error(a(387));if(y=y.memoizedProps,y!==null){var _=f.type;ze(f.pendingProps.value,y.value)||(t!==null?t.push(_):t=[_])}}else if(f===Cl.current){if(y=f.alternate,y===null)throw Error(a(387));y.memoizedState.memoizedState!==f.memoizedState.memoizedState&&(t!==null?t.push(Fa):t=[Fa])}f=f.return}t!==null&&Sc(n,t,l,r),n.flags|=262144}function no(t){for(t=t.firstContext;t!==null;){if(!ze(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Oi(t){Di=t,jn=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function he(t){return rd(Di,t)}function io(t,n){return Di===null&&Oi(t),rd(t,n)}function rd(t,n){var l=n._currentValue;if(n={context:n,memoizedValue:l,next:null},jn===null){if(t===null)throw Error(a(308));jn=n,t.dependencies={lanes:0,firstContext:n},t.flags|=524288}else jn=jn.next=n;return l}var Oy=typeof AbortController<"u"?AbortController:function(){var t=[],n=this.signal={aborted:!1,addEventListener:function(l,r){t.push(r)}};this.abort=function(){n.aborted=!0,t.forEach(function(l){return l()})}},Ey=i.unstable_scheduleCallback,zy=i.unstable_NormalPriority,It={$$typeof:q,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Mc(){return{controller:new Oy,data:new Map,refCount:0}}function ma(t){t.refCount--,t.refCount===0&&Ey(zy,function(){t.controller.abort()})}var ga=null,wc=0,gs=0,ps=null;function Cy(t,n){if(ga===null){var l=ga=[];wc=0,gs=Au(),ps={status:"pending",value:void 0,then:function(r){l.push(r)}}}return wc++,n.then(cd,cd),n}function cd(){if(--wc===0&&ga!==null){ps!==null&&(ps.status="fulfilled");var t=ga;ga=null,gs=0,ps=null;for(var n=0;n<t.length;n++)(0,t[n])()}}function Ry(t,n){var l=[],r={status:"pending",value:null,reason:null,then:function(f){l.push(f)}};return t.then(function(){r.status="fulfilled",r.value=n;for(var f=0;f<l.length;f++)(0,l[f])(n)},function(f){for(r.status="rejected",r.reason=f,f=0;f<l.length;f++)(0,l[f])(void 0)}),r}var ud=k.S;k.S=function(t,n){typeof n=="object"&&n!==null&&typeof n.then=="function"&&Cy(t,n),ud!==null&&ud(t,n)};var Ei=Tt(null);function Tc(){var t=Ei.current;return t!==null?t:kt.pooledCache}function so(t,n){n===null?ct(Ei,Ei.current):ct(Ei,n.pool)}function fd(){var t=Tc();return t===null?null:{parent:It._currentValue,pool:t}}var pa=Error(a(460)),hd=Error(a(474)),ao=Error(a(542)),Ac={then:function(){}};function dd(t){return t=t.status,t==="fulfilled"||t==="rejected"}function lo(){}function md(t,n,l){switch(l=t[l],l===void 0?t.push(n):l!==n&&(n.then(lo,lo),n=l),n.status){case"fulfilled":return n.value;case"rejected":throw t=n.reason,pd(t),t;default:if(typeof n.status=="string")n.then(lo,lo);else{if(t=kt,t!==null&&100<t.shellSuspendCounter)throw Error(a(482));t=n,t.status="pending",t.then(function(r){if(n.status==="pending"){var f=n;f.status="fulfilled",f.value=r}},function(r){if(n.status==="pending"){var f=n;f.status="rejected",f.reason=r}})}switch(n.status){case"fulfilled":return n.value;case"rejected":throw t=n.reason,pd(t),t}throw ba=n,pa}}var ba=null;function gd(){if(ba===null)throw Error(a(459));var t=ba;return ba=null,t}function pd(t){if(t===pa||t===ao)throw Error(a(483))}var Qn=!1;function jc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Dc(t,n){t=t.updateQueue,n.updateQueue===t&&(n.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Zn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Kn(t,n,l){var r=t.updateQueue;if(r===null)return null;if(r=r.shared,(At&2)!==0){var f=r.pending;return f===null?n.next=n:(n.next=f.next,f.next=n),r.pending=n,n=Il(t),nd(t,null,l),n}return Jl(t,r,n,l),Il(t)}function xa(t,n,l){if(n=n.updateQueue,n!==null&&(n=n.shared,(l&4194048)!==0)){var r=n.lanes;r&=t.pendingLanes,l|=r,n.lanes=l,ch(t,l)}}function Oc(t,n){var l=t.updateQueue,r=t.alternate;if(r!==null&&(r=r.updateQueue,l===r)){var f=null,d=null;if(l=l.firstBaseUpdate,l!==null){do{var y={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};d===null?f=d=y:d=d.next=y,l=l.next}while(l!==null);d===null?f=d=n:d=d.next=n}else f=d=n;l={baseState:r.baseState,firstBaseUpdate:f,lastBaseUpdate:d,shared:r.shared,callbacks:r.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=n:t.next=n,l.lastBaseUpdate=n}var Ec=!1;function ya(){if(Ec){var t=ps;if(t!==null)throw t}}function va(t,n,l,r){Ec=!1;var f=t.updateQueue;Qn=!1;var d=f.firstBaseUpdate,y=f.lastBaseUpdate,_=f.shared.pending;if(_!==null){f.shared.pending=null;var A=_,z=A.next;A.next=null,y===null?d=z:y.next=z,y=A;var N=t.alternate;N!==null&&(N=N.updateQueue,_=N.lastBaseUpdate,_!==y&&(_===null?N.firstBaseUpdate=z:_.next=z,N.lastBaseUpdate=A))}if(d!==null){var U=f.baseState;y=0,N=z=A=null,_=d;do{var C=_.lane&-536870913,R=C!==_.lane;if(R?(bt&C)===C:(r&C)===C){C!==0&&C===gs&&(Ec=!0),N!==null&&(N=N.next={lane:0,tag:_.tag,payload:_.payload,callback:null,next:null});t:{var it=t,$=_;C=n;var zt=l;switch($.tag){case 1:if(it=$.payload,typeof it=="function"){U=it.call(zt,U,C);break t}U=it;break t;case 3:it.flags=it.flags&-65537|128;case 0:if(it=$.payload,C=typeof it=="function"?it.call(zt,U,C):it,C==null)break t;U=p({},U,C);break t;case 2:Qn=!0}}C=_.callback,C!==null&&(t.flags|=64,R&&(t.flags|=8192),R=f.callbacks,R===null?f.callbacks=[C]:R.push(C))}else R={lane:C,tag:_.tag,payload:_.payload,callback:_.callback,next:null},N===null?(z=N=R,A=U):N=N.next=R,y|=C;if(_=_.next,_===null){if(_=f.shared.pending,_===null)break;R=_,_=R.next,R.next=null,f.lastBaseUpdate=R,f.shared.pending=null}}while(!0);N===null&&(A=U),f.baseState=A,f.firstBaseUpdate=z,f.lastBaseUpdate=N,d===null&&(f.shared.lanes=0),ni|=y,t.lanes=y,t.memoizedState=U}}function bd(t,n){if(typeof t!="function")throw Error(a(191,t));t.call(n)}function xd(t,n){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)bd(l[t],n)}var bs=Tt(null),oo=Tt(0);function yd(t,n){t=Bn,ct(oo,t),ct(bs,n),Bn=t|n.baseLanes}function zc(){ct(oo,Bn),ct(bs,bs.current)}function Cc(){Bn=oo.current,ft(bs),ft(oo)}var Wn=0,ot=null,Ot=null,Wt=null,ro=!1,xs=!1,zi=!1,co=0,_a=0,ys=null,ky=0;function Xt(){throw Error(a(321))}function Rc(t,n){if(n===null)return!1;for(var l=0;l<n.length&&l<t.length;l++)if(!ze(t[l],n[l]))return!1;return!0}function kc(t,n,l,r,f,d){return Wn=d,ot=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,k.H=t===null||t.memoizedState===null?nm:im,zi=!1,d=l(r,f),zi=!1,xs&&(d=_d(n,l,r,f)),vd(t),d}function vd(t){k.H=po;var n=Ot!==null&&Ot.next!==null;if(Wn=0,Wt=Ot=ot=null,ro=!1,_a=0,ys=null,n)throw Error(a(300));t===null||ne||(t=t.dependencies,t!==null&&no(t)&&(ne=!0))}function _d(t,n,l,r){ot=t;var f=0;do{if(xs&&(ys=null),_a=0,xs=!1,25<=f)throw Error(a(301));if(f+=1,Wt=Ot=null,t.updateQueue!=null){var d=t.updateQueue;d.lastEffect=null,d.events=null,d.stores=null,d.memoCache!=null&&(d.memoCache.index=0)}k.H=Vy,d=n(l,r)}while(xs);return d}function By(){var t=k.H,n=t.useState()[0];return n=typeof n.then=="function"?Sa(n):n,t=t.useState()[0],(Ot!==null?Ot.memoizedState:null)!==t&&(ot.flags|=1024),n}function Bc(){var t=co!==0;return co=0,t}function Nc(t,n,l){n.updateQueue=t.updateQueue,n.flags&=-2053,t.lanes&=~l}function Lc(t){if(ro){for(t=t.memoizedState;t!==null;){var n=t.queue;n!==null&&(n.pending=null),t=t.next}ro=!1}Wn=0,Wt=Ot=ot=null,xs=!1,_a=co=0,ys=null}function Se(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Wt===null?ot.memoizedState=Wt=t:Wt=Wt.next=t,Wt}function Pt(){if(Ot===null){var t=ot.alternate;t=t!==null?t.memoizedState:null}else t=Ot.next;var n=Wt===null?ot.memoizedState:Wt.next;if(n!==null)Wt=n,Ot=t;else{if(t===null)throw ot.alternate===null?Error(a(467)):Error(a(310));Ot=t,t={memoizedState:Ot.memoizedState,baseState:Ot.baseState,baseQueue:Ot.baseQueue,queue:Ot.queue,next:null},Wt===null?ot.memoizedState=Wt=t:Wt=Wt.next=t}return Wt}function Hc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Sa(t){var n=_a;return _a+=1,ys===null&&(ys=[]),t=md(ys,t,n),n=ot,(Wt===null?n.memoizedState:Wt.next)===null&&(n=n.alternate,k.H=n===null||n.memoizedState===null?nm:im),t}function uo(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Sa(t);if(t.$$typeof===q)return he(t)}throw Error(a(438,String(t)))}function Uc(t){var n=null,l=ot.updateQueue;if(l!==null&&(n=l.memoCache),n==null){var r=ot.alternate;r!==null&&(r=r.updateQueue,r!==null&&(r=r.memoCache,r!=null&&(n={data:r.data.map(function(f){return f.slice()}),index:0})))}if(n==null&&(n={data:[],index:0}),l===null&&(l=Hc(),ot.updateQueue=l),l.memoCache=n,l=n.data[n.index],l===void 0)for(l=n.data[n.index]=Array(t),r=0;r<t;r++)l[r]=rt;return n.index++,l}function On(t,n){return typeof n=="function"?n(t):n}function fo(t){var n=Pt();return qc(n,Ot,t)}function qc(t,n,l){var r=t.queue;if(r===null)throw Error(a(311));r.lastRenderedReducer=l;var f=t.baseQueue,d=r.pending;if(d!==null){if(f!==null){var y=f.next;f.next=d.next,d.next=y}n.baseQueue=f=d,r.pending=null}if(d=t.baseState,f===null)t.memoizedState=d;else{n=f.next;var _=y=null,A=null,z=n,N=!1;do{var U=z.lane&-536870913;if(U!==z.lane?(bt&U)===U:(Wn&U)===U){var C=z.revertLane;if(C===0)A!==null&&(A=A.next={lane:0,revertLane:0,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null}),U===gs&&(N=!0);else if((Wn&C)===C){z=z.next,C===gs&&(N=!0);continue}else U={lane:0,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},A===null?(_=A=U,y=d):A=A.next=U,ot.lanes|=C,ni|=C;U=z.action,zi&&l(d,U),d=z.hasEagerState?z.eagerState:l(d,U)}else C={lane:U,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},A===null?(_=A=C,y=d):A=A.next=C,ot.lanes|=U,ni|=U;z=z.next}while(z!==null&&z!==n);if(A===null?y=d:A.next=_,!ze(d,t.memoizedState)&&(ne=!0,N&&(l=ps,l!==null)))throw l;t.memoizedState=d,t.baseState=y,t.baseQueue=A,r.lastRenderedState=d}return f===null&&(r.lanes=0),[t.memoizedState,r.dispatch]}function Vc(t){var n=Pt(),l=n.queue;if(l===null)throw Error(a(311));l.lastRenderedReducer=t;var r=l.dispatch,f=l.pending,d=n.memoizedState;if(f!==null){l.pending=null;var y=f=f.next;do d=t(d,y.action),y=y.next;while(y!==f);ze(d,n.memoizedState)||(ne=!0),n.memoizedState=d,n.baseQueue===null&&(n.baseState=d),l.lastRenderedState=d}return[d,r]}function Sd(t,n,l){var r=ot,f=Pt(),d=St;if(d){if(l===void 0)throw Error(a(407));l=l()}else l=n();var y=!ze((Ot||f).memoizedState,l);y&&(f.memoizedState=l,ne=!0),f=f.queue;var _=Td.bind(null,r,f,t);if(Ma(2048,8,_,[t]),f.getSnapshot!==n||y||Wt!==null&&Wt.memoizedState.tag&1){if(r.flags|=2048,vs(9,ho(),wd.bind(null,r,f,l,n),null),kt===null)throw Error(a(349));d||(Wn&124)!==0||Md(r,n,l)}return l}function Md(t,n,l){t.flags|=16384,t={getSnapshot:n,value:l},n=ot.updateQueue,n===null?(n=Hc(),ot.updateQueue=n,n.stores=[t]):(l=n.stores,l===null?n.stores=[t]:l.push(t))}function wd(t,n,l,r){n.value=l,n.getSnapshot=r,Ad(n)&&jd(t)}function Td(t,n,l){return l(function(){Ad(n)&&jd(t)})}function Ad(t){var n=t.getSnapshot;t=t.value;try{var l=n();return!ze(t,l)}catch{return!0}}function jd(t){var n=fs(t,2);n!==null&&Le(n,t,2)}function Fc(t){var n=Se();if(typeof t=="function"){var l=t;if(t=l(),zi){Fn(!0);try{l()}finally{Fn(!1)}}}return n.memoizedState=n.baseState=t,n.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:On,lastRenderedState:t},n}function Dd(t,n,l,r){return t.baseState=l,qc(t,Ot,typeof r=="function"?r:On)}function Ny(t,n,l,r,f){if(go(t))throw Error(a(485));if(t=n.action,t!==null){var d={payload:f,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(y){d.listeners.push(y)}};k.T!==null?l(!0):d.isTransition=!1,r(d),l=n.pending,l===null?(d.next=n.pending=d,Od(n,d)):(d.next=l.next,n.pending=l.next=d)}}function Od(t,n){var l=n.action,r=n.payload,f=t.state;if(n.isTransition){var d=k.T,y={};k.T=y;try{var _=l(f,r),A=k.S;A!==null&&A(y,_),Ed(t,n,_)}catch(z){Yc(t,n,z)}finally{k.T=d}}else try{d=l(f,r),Ed(t,n,d)}catch(z){Yc(t,n,z)}}function Ed(t,n,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(r){zd(t,n,r)},function(r){return Yc(t,n,r)}):zd(t,n,l)}function zd(t,n,l){n.status="fulfilled",n.value=l,Cd(n),t.state=l,n=t.pending,n!==null&&(l=n.next,l===n?t.pending=null:(l=l.next,n.next=l,Od(t,l)))}function Yc(t,n,l){var r=t.pending;if(t.pending=null,r!==null){r=r.next;do n.status="rejected",n.reason=l,Cd(n),n=n.next;while(n!==r)}t.action=null}function Cd(t){t=t.listeners;for(var n=0;n<t.length;n++)(0,t[n])()}function Rd(t,n){return n}function kd(t,n){if(St){var l=kt.formState;if(l!==null){t:{var r=ot;if(St){if(Ft){e:{for(var f=Ft,d=un;f.nodeType!==8;){if(!d){f=null;break e}if(f=an(f.nextSibling),f===null){f=null;break e}}d=f.data,f=d==="F!"||d==="F"?f:null}if(f){Ft=an(f.nextSibling),r=f.data==="F!";break t}}ji(r)}r=!1}r&&(n=l[0])}}return l=Se(),l.memoizedState=l.baseState=n,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Rd,lastRenderedState:n},l.queue=r,l=$d.bind(null,ot,r),r.dispatch=l,r=Fc(!1),d=Kc.bind(null,ot,!1,r.queue),r=Se(),f={state:n,dispatch:null,action:t,pending:null},r.queue=f,l=Ny.bind(null,ot,f,d,l),f.dispatch=l,r.memoizedState=t,[n,l,!1]}function Bd(t){var n=Pt();return Nd(n,Ot,t)}function Nd(t,n,l){if(n=qc(t,n,Rd)[0],t=fo(On)[0],typeof n=="object"&&n!==null&&typeof n.then=="function")try{var r=Sa(n)}catch(y){throw y===pa?ao:y}else r=n;n=Pt();var f=n.queue,d=f.dispatch;return l!==n.memoizedState&&(ot.flags|=2048,vs(9,ho(),Ly.bind(null,f,l),null)),[r,d,t]}function Ly(t,n){t.action=n}function Ld(t){var n=Pt(),l=Ot;if(l!==null)return Nd(n,l,t);Pt(),n=n.memoizedState,l=Pt();var r=l.queue.dispatch;return l.memoizedState=t,[n,r,!1]}function vs(t,n,l,r){return t={tag:t,create:l,deps:r,inst:n,next:null},n=ot.updateQueue,n===null&&(n=Hc(),ot.updateQueue=n),l=n.lastEffect,l===null?n.lastEffect=t.next=t:(r=l.next,l.next=t,t.next=r,n.lastEffect=t),t}function ho(){return{destroy:void 0,resource:void 0}}function Hd(){return Pt().memoizedState}function mo(t,n,l,r){var f=Se();r=r===void 0?null:r,ot.flags|=t,f.memoizedState=vs(1|n,ho(),l,r)}function Ma(t,n,l,r){var f=Pt();r=r===void 0?null:r;var d=f.memoizedState.inst;Ot!==null&&r!==null&&Rc(r,Ot.memoizedState.deps)?f.memoizedState=vs(n,d,l,r):(ot.flags|=t,f.memoizedState=vs(1|n,d,l,r))}function Ud(t,n){mo(8390656,8,t,n)}function qd(t,n){Ma(2048,8,t,n)}function Vd(t,n){return Ma(4,2,t,n)}function Fd(t,n){return Ma(4,4,t,n)}function Yd(t,n){if(typeof n=="function"){t=t();var l=n(t);return function(){typeof l=="function"?l():n(null)}}if(n!=null)return t=t(),n.current=t,function(){n.current=null}}function Xd(t,n,l){l=l!=null?l.concat([t]):null,Ma(4,4,Yd.bind(null,n,t),l)}function Xc(){}function Gd(t,n){var l=Pt();n=n===void 0?null:n;var r=l.memoizedState;return n!==null&&Rc(n,r[1])?r[0]:(l.memoizedState=[t,n],t)}function Qd(t,n){var l=Pt();n=n===void 0?null:n;var r=l.memoizedState;if(n!==null&&Rc(n,r[1]))return r[0];if(r=t(),zi){Fn(!0);try{t()}finally{Fn(!1)}}return l.memoizedState=[r,n],r}function Gc(t,n,l){return l===void 0||(Wn&1073741824)!==0?t.memoizedState=n:(t.memoizedState=l,t=Wm(),ot.lanes|=t,ni|=t,l)}function Zd(t,n,l,r){return ze(l,n)?l:bs.current!==null?(t=Gc(t,l,r),ze(t,n)||(ne=!0),t):(Wn&42)===0?(ne=!0,t.memoizedState=l):(t=Wm(),ot.lanes|=t,ni|=t,n)}function Kd(t,n,l,r,f){var d=Q.p;Q.p=d!==0&&8>d?d:8;var y=k.T,_={};k.T=_,Kc(t,!1,n,l);try{var A=f(),z=k.S;if(z!==null&&z(_,A),A!==null&&typeof A=="object"&&typeof A.then=="function"){var N=Ry(A,r);wa(t,n,N,Ne(t))}else wa(t,n,r,Ne(t))}catch(U){wa(t,n,{then:function(){},status:"rejected",reason:U},Ne())}finally{Q.p=d,k.T=y}}function Hy(){}function Qc(t,n,l,r){if(t.tag!==5)throw Error(a(476));var f=Wd(t).queue;Kd(t,f,n,P,l===null?Hy:function(){return Pd(t),l(r)})}function Wd(t){var n=t.memoizedState;if(n!==null)return n;n={memoizedState:P,baseState:P,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:On,lastRenderedState:P},next:null};var l={};return n.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:On,lastRenderedState:l},next:null},t.memoizedState=n,t=t.alternate,t!==null&&(t.memoizedState=n),n}function Pd(t){var n=Wd(t).next.queue;wa(t,n,{},Ne())}function Zc(){return he(Fa)}function Jd(){return Pt().memoizedState}function Id(){return Pt().memoizedState}function Uy(t){for(var n=t.return;n!==null;){switch(n.tag){case 24:case 3:var l=Ne();t=Zn(l);var r=Kn(n,t,l);r!==null&&(Le(r,n,l),xa(r,n,l)),n={cache:Mc()},t.payload=n;return}n=n.return}}function qy(t,n,l){var r=Ne();l={lane:r,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},go(t)?tm(n,l):(l=dc(t,n,l,r),l!==null&&(Le(l,t,r),em(l,n,r)))}function $d(t,n,l){var r=Ne();wa(t,n,l,r)}function wa(t,n,l,r){var f={lane:r,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(go(t))tm(n,f);else{var d=t.alternate;if(t.lanes===0&&(d===null||d.lanes===0)&&(d=n.lastRenderedReducer,d!==null))try{var y=n.lastRenderedState,_=d(y,l);if(f.hasEagerState=!0,f.eagerState=_,ze(_,y))return Jl(t,n,f,0),kt===null&&Pl(),!1}catch{}finally{}if(l=dc(t,n,f,r),l!==null)return Le(l,t,r),em(l,n,r),!0}return!1}function Kc(t,n,l,r){if(r={lane:2,revertLane:Au(),action:r,hasEagerState:!1,eagerState:null,next:null},go(t)){if(n)throw Error(a(479))}else n=dc(t,l,r,2),n!==null&&Le(n,t,2)}function go(t){var n=t.alternate;return t===ot||n!==null&&n===ot}function tm(t,n){xs=ro=!0;var l=t.pending;l===null?n.next=n:(n.next=l.next,l.next=n),t.pending=n}function em(t,n,l){if((l&4194048)!==0){var r=n.lanes;r&=t.pendingLanes,l|=r,n.lanes=l,ch(t,l)}}var po={readContext:he,use:uo,useCallback:Xt,useContext:Xt,useEffect:Xt,useImperativeHandle:Xt,useLayoutEffect:Xt,useInsertionEffect:Xt,useMemo:Xt,useReducer:Xt,useRef:Xt,useState:Xt,useDebugValue:Xt,useDeferredValue:Xt,useTransition:Xt,useSyncExternalStore:Xt,useId:Xt,useHostTransitionStatus:Xt,useFormState:Xt,useActionState:Xt,useOptimistic:Xt,useMemoCache:Xt,useCacheRefresh:Xt},nm={readContext:he,use:uo,useCallback:function(t,n){return Se().memoizedState=[t,n===void 0?null:n],t},useContext:he,useEffect:Ud,useImperativeHandle:function(t,n,l){l=l!=null?l.concat([t]):null,mo(4194308,4,Yd.bind(null,n,t),l)},useLayoutEffect:function(t,n){return mo(4194308,4,t,n)},useInsertionEffect:function(t,n){mo(4,2,t,n)},useMemo:function(t,n){var l=Se();n=n===void 0?null:n;var r=t();if(zi){Fn(!0);try{t()}finally{Fn(!1)}}return l.memoizedState=[r,n],r},useReducer:function(t,n,l){var r=Se();if(l!==void 0){var f=l(n);if(zi){Fn(!0);try{l(n)}finally{Fn(!1)}}}else f=n;return r.memoizedState=r.baseState=f,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:f},r.queue=t,t=t.dispatch=qy.bind(null,ot,t),[r.memoizedState,t]},useRef:function(t){var n=Se();return t={current:t},n.memoizedState=t},useState:function(t){t=Fc(t);var n=t.queue,l=$d.bind(null,ot,n);return n.dispatch=l,[t.memoizedState,l]},useDebugValue:Xc,useDeferredValue:function(t,n){var l=Se();return Gc(l,t,n)},useTransition:function(){var t=Fc(!1);return t=Kd.bind(null,ot,t.queue,!0,!1),Se().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,n,l){var r=ot,f=Se();if(St){if(l===void 0)throw Error(a(407));l=l()}else{if(l=n(),kt===null)throw Error(a(349));(bt&124)!==0||Md(r,n,l)}f.memoizedState=l;var d={value:l,getSnapshot:n};return f.queue=d,Ud(Td.bind(null,r,d,t),[t]),r.flags|=2048,vs(9,ho(),wd.bind(null,r,d,l,n),null),l},useId:function(){var t=Se(),n=kt.identifierPrefix;if(St){var l=An,r=Tn;l=(r&~(1<<32-Ee(r)-1)).toString(32)+l,n="«"+n+"R"+l,l=co++,0<l&&(n+="H"+l.toString(32)),n+="»"}else l=ky++,n="«"+n+"r"+l.toString(32)+"»";return t.memoizedState=n},useHostTransitionStatus:Zc,useFormState:kd,useActionState:kd,useOptimistic:function(t){var n=Se();n.memoizedState=n.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return n.queue=l,n=Kc.bind(null,ot,!0,l),l.dispatch=n,[t,n]},useMemoCache:Uc,useCacheRefresh:function(){return Se().memoizedState=Uy.bind(null,ot)}},im={readContext:he,use:uo,useCallback:Gd,useContext:he,useEffect:qd,useImperativeHandle:Xd,useInsertionEffect:Vd,useLayoutEffect:Fd,useMemo:Qd,useReducer:fo,useRef:Hd,useState:function(){return fo(On)},useDebugValue:Xc,useDeferredValue:function(t,n){var l=Pt();return Zd(l,Ot.memoizedState,t,n)},useTransition:function(){var t=fo(On)[0],n=Pt().memoizedState;return[typeof t=="boolean"?t:Sa(t),n]},useSyncExternalStore:Sd,useId:Jd,useHostTransitionStatus:Zc,useFormState:Bd,useActionState:Bd,useOptimistic:function(t,n){var l=Pt();return Dd(l,Ot,t,n)},useMemoCache:Uc,useCacheRefresh:Id},Vy={readContext:he,use:uo,useCallback:Gd,useContext:he,useEffect:qd,useImperativeHandle:Xd,useInsertionEffect:Vd,useLayoutEffect:Fd,useMemo:Qd,useReducer:Vc,useRef:Hd,useState:function(){return Vc(On)},useDebugValue:Xc,useDeferredValue:function(t,n){var l=Pt();return Ot===null?Gc(l,t,n):Zd(l,Ot.memoizedState,t,n)},useTransition:function(){var t=Vc(On)[0],n=Pt().memoizedState;return[typeof t=="boolean"?t:Sa(t),n]},useSyncExternalStore:Sd,useId:Jd,useHostTransitionStatus:Zc,useFormState:Ld,useActionState:Ld,useOptimistic:function(t,n){var l=Pt();return Ot!==null?Dd(l,Ot,t,n):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:Uc,useCacheRefresh:Id},_s=null,Ta=0;function bo(t){var n=Ta;return Ta+=1,_s===null&&(_s=[]),md(_s,t,n)}function Aa(t,n){n=n.props.ref,t.ref=n!==void 0?n:null}function xo(t,n){throw n.$$typeof===x?Error(a(525)):(t=Object.prototype.toString.call(n),Error(a(31,t==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":t)))}function sm(t){var n=t._init;return n(t._payload)}function am(t){function n(D,j){if(t){var E=D.deletions;E===null?(D.deletions=[j],D.flags|=16):E.push(j)}}function l(D,j){if(!t)return null;for(;j!==null;)n(D,j),j=j.sibling;return null}function r(D){for(var j=new Map;D!==null;)D.key!==null?j.set(D.key,D):j.set(D.index,D),D=D.sibling;return j}function f(D,j){return D=wn(D,j),D.index=0,D.sibling=null,D}function d(D,j,E){return D.index=E,t?(E=D.alternate,E!==null?(E=E.index,E<j?(D.flags|=67108866,j):E):(D.flags|=67108866,j)):(D.flags|=1048576,j)}function y(D){return t&&D.alternate===null&&(D.flags|=67108866),D}function _(D,j,E,L){return j===null||j.tag!==6?(j=gc(E,D.mode,L),j.return=D,j):(j=f(j,E),j.return=D,j)}function A(D,j,E,L){var W=E.type;return W===M?N(D,j,E.props.children,L,E.key):j!==null&&(j.elementType===W||typeof W=="object"&&W!==null&&W.$$typeof===Z&&sm(W)===j.type)?(j=f(j,E.props),Aa(j,E),j.return=D,j):(j=$l(E.type,E.key,E.props,null,D.mode,L),Aa(j,E),j.return=D,j)}function z(D,j,E,L){return j===null||j.tag!==4||j.stateNode.containerInfo!==E.containerInfo||j.stateNode.implementation!==E.implementation?(j=pc(E,D.mode,L),j.return=D,j):(j=f(j,E.children||[]),j.return=D,j)}function N(D,j,E,L,W){return j===null||j.tag!==7?(j=Mi(E,D.mode,L,W),j.return=D,j):(j=f(j,E),j.return=D,j)}function U(D,j,E){if(typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint")return j=gc(""+j,D.mode,E),j.return=D,j;if(typeof j=="object"&&j!==null){switch(j.$$typeof){case v:return E=$l(j.type,j.key,j.props,null,D.mode,E),Aa(E,j),E.return=D,E;case S:return j=pc(j,D.mode,E),j.return=D,j;case Z:var L=j._init;return j=L(j._payload),U(D,j,E)}if(gt(j)||nt(j))return j=Mi(j,D.mode,E,null),j.return=D,j;if(typeof j.then=="function")return U(D,bo(j),E);if(j.$$typeof===q)return U(D,io(D,j),E);xo(D,j)}return null}function C(D,j,E,L){var W=j!==null?j.key:null;if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return W!==null?null:_(D,j,""+E,L);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case v:return E.key===W?A(D,j,E,L):null;case S:return E.key===W?z(D,j,E,L):null;case Z:return W=E._init,E=W(E._payload),C(D,j,E,L)}if(gt(E)||nt(E))return W!==null?null:N(D,j,E,L,null);if(typeof E.then=="function")return C(D,j,bo(E),L);if(E.$$typeof===q)return C(D,j,io(D,E),L);xo(D,E)}return null}function R(D,j,E,L,W){if(typeof L=="string"&&L!==""||typeof L=="number"||typeof L=="bigint")return D=D.get(E)||null,_(j,D,""+L,W);if(typeof L=="object"&&L!==null){switch(L.$$typeof){case v:return D=D.get(L.key===null?E:L.key)||null,A(j,D,L,W);case S:return D=D.get(L.key===null?E:L.key)||null,z(j,D,L,W);case Z:var ut=L._init;return L=ut(L._payload),R(D,j,E,L,W)}if(gt(L)||nt(L))return D=D.get(E)||null,N(j,D,L,W,null);if(typeof L.then=="function")return R(D,j,E,bo(L),W);if(L.$$typeof===q)return R(D,j,E,io(j,L),W);xo(j,L)}return null}function it(D,j,E,L){for(var W=null,ut=null,J=j,tt=j=0,se=null;J!==null&&tt<E.length;tt++){J.index>tt?(se=J,J=null):se=J.sibling;var yt=C(D,J,E[tt],L);if(yt===null){J===null&&(J=se);break}t&&J&&yt.alternate===null&&n(D,J),j=d(yt,j,tt),ut===null?W=yt:ut.sibling=yt,ut=yt,J=se}if(tt===E.length)return l(D,J),St&&Ti(D,tt),W;if(J===null){for(;tt<E.length;tt++)J=U(D,E[tt],L),J!==null&&(j=d(J,j,tt),ut===null?W=J:ut.sibling=J,ut=J);return St&&Ti(D,tt),W}for(J=r(J);tt<E.length;tt++)se=R(J,D,tt,E[tt],L),se!==null&&(t&&se.alternate!==null&&J.delete(se.key===null?tt:se.key),j=d(se,j,tt),ut===null?W=se:ut.sibling=se,ut=se);return t&&J.forEach(function(fi){return n(D,fi)}),St&&Ti(D,tt),W}function $(D,j,E,L){if(E==null)throw Error(a(151));for(var W=null,ut=null,J=j,tt=j=0,se=null,yt=E.next();J!==null&&!yt.done;tt++,yt=E.next()){J.index>tt?(se=J,J=null):se=J.sibling;var fi=C(D,J,yt.value,L);if(fi===null){J===null&&(J=se);break}t&&J&&fi.alternate===null&&n(D,J),j=d(fi,j,tt),ut===null?W=fi:ut.sibling=fi,ut=fi,J=se}if(yt.done)return l(D,J),St&&Ti(D,tt),W;if(J===null){for(;!yt.done;tt++,yt=E.next())yt=U(D,yt.value,L),yt!==null&&(j=d(yt,j,tt),ut===null?W=yt:ut.sibling=yt,ut=yt);return St&&Ti(D,tt),W}for(J=r(J);!yt.done;tt++,yt=E.next())yt=R(J,D,tt,yt.value,L),yt!==null&&(t&&yt.alternate!==null&&J.delete(yt.key===null?tt:yt.key),j=d(yt,j,tt),ut===null?W=yt:ut.sibling=yt,ut=yt);return t&&J.forEach(function(Fv){return n(D,Fv)}),St&&Ti(D,tt),W}function zt(D,j,E,L){if(typeof E=="object"&&E!==null&&E.type===M&&E.key===null&&(E=E.props.children),typeof E=="object"&&E!==null){switch(E.$$typeof){case v:t:{for(var W=E.key;j!==null;){if(j.key===W){if(W=E.type,W===M){if(j.tag===7){l(D,j.sibling),L=f(j,E.props.children),L.return=D,D=L;break t}}else if(j.elementType===W||typeof W=="object"&&W!==null&&W.$$typeof===Z&&sm(W)===j.type){l(D,j.sibling),L=f(j,E.props),Aa(L,E),L.return=D,D=L;break t}l(D,j);break}else n(D,j);j=j.sibling}E.type===M?(L=Mi(E.props.children,D.mode,L,E.key),L.return=D,D=L):(L=$l(E.type,E.key,E.props,null,D.mode,L),Aa(L,E),L.return=D,D=L)}return y(D);case S:t:{for(W=E.key;j!==null;){if(j.key===W)if(j.tag===4&&j.stateNode.containerInfo===E.containerInfo&&j.stateNode.implementation===E.implementation){l(D,j.sibling),L=f(j,E.children||[]),L.return=D,D=L;break t}else{l(D,j);break}else n(D,j);j=j.sibling}L=pc(E,D.mode,L),L.return=D,D=L}return y(D);case Z:return W=E._init,E=W(E._payload),zt(D,j,E,L)}if(gt(E))return it(D,j,E,L);if(nt(E)){if(W=nt(E),typeof W!="function")throw Error(a(150));return E=W.call(E),$(D,j,E,L)}if(typeof E.then=="function")return zt(D,j,bo(E),L);if(E.$$typeof===q)return zt(D,j,io(D,E),L);xo(D,E)}return typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint"?(E=""+E,j!==null&&j.tag===6?(l(D,j.sibling),L=f(j,E),L.return=D,D=L):(l(D,j),L=gc(E,D.mode,L),L.return=D,D=L),y(D)):l(D,j)}return function(D,j,E,L){try{Ta=0;var W=zt(D,j,E,L);return _s=null,W}catch(J){if(J===pa||J===ao)throw J;var ut=Ce(29,J,null,D.mode);return ut.lanes=L,ut.return=D,ut}finally{}}}var Ss=am(!0),lm=am(!1),Qe=Tt(null),fn=null;function Pn(t){var n=t.alternate;ct($t,$t.current&1),ct(Qe,t),fn===null&&(n===null||bs.current!==null||n.memoizedState!==null)&&(fn=t)}function om(t){if(t.tag===22){if(ct($t,$t.current),ct(Qe,t),fn===null){var n=t.alternate;n!==null&&n.memoizedState!==null&&(fn=t)}}else Jn()}function Jn(){ct($t,$t.current),ct(Qe,Qe.current)}function En(t){ft(Qe),fn===t&&(fn=null),ft($t)}var $t=Tt(0);function yo(t){for(var n=t;n!==null;){if(n.tag===13){var l=n.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||Hu(l)))return n}else if(n.tag===19&&n.memoizedProps.revealOrder!==void 0){if((n.flags&128)!==0)return n}else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}function Wc(t,n,l,r){n=t.memoizedState,l=l(r,n),l=l==null?n:p({},n,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var Pc={enqueueSetState:function(t,n,l){t=t._reactInternals;var r=Ne(),f=Zn(r);f.payload=n,l!=null&&(f.callback=l),n=Kn(t,f,r),n!==null&&(Le(n,t,r),xa(n,t,r))},enqueueReplaceState:function(t,n,l){t=t._reactInternals;var r=Ne(),f=Zn(r);f.tag=1,f.payload=n,l!=null&&(f.callback=l),n=Kn(t,f,r),n!==null&&(Le(n,t,r),xa(n,t,r))},enqueueForceUpdate:function(t,n){t=t._reactInternals;var l=Ne(),r=Zn(l);r.tag=2,n!=null&&(r.callback=n),n=Kn(t,r,l),n!==null&&(Le(n,t,l),xa(n,t,l))}};function rm(t,n,l,r,f,d,y){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(r,d,y):n.prototype&&n.prototype.isPureReactComponent?!ra(l,r)||!ra(f,d):!0}function cm(t,n,l,r){t=n.state,typeof n.componentWillReceiveProps=="function"&&n.componentWillReceiveProps(l,r),typeof n.UNSAFE_componentWillReceiveProps=="function"&&n.UNSAFE_componentWillReceiveProps(l,r),n.state!==t&&Pc.enqueueReplaceState(n,n.state,null)}function Ci(t,n){var l=n;if("ref"in n){l={};for(var r in n)r!=="ref"&&(l[r]=n[r])}if(t=t.defaultProps){l===n&&(l=p({},l));for(var f in t)l[f]===void 0&&(l[f]=t[f])}return l}var vo=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var n=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(n))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function um(t){vo(t)}function fm(t){console.error(t)}function hm(t){vo(t)}function _o(t,n){try{var l=t.onUncaughtError;l(n.value,{componentStack:n.stack})}catch(r){setTimeout(function(){throw r})}}function dm(t,n,l){try{var r=t.onCaughtError;r(l.value,{componentStack:l.stack,errorBoundary:n.tag===1?n.stateNode:null})}catch(f){setTimeout(function(){throw f})}}function Jc(t,n,l){return l=Zn(l),l.tag=3,l.payload={element:null},l.callback=function(){_o(t,n)},l}function mm(t){return t=Zn(t),t.tag=3,t}function gm(t,n,l,r){var f=l.type.getDerivedStateFromError;if(typeof f=="function"){var d=r.value;t.payload=function(){return f(d)},t.callback=function(){dm(n,l,r)}}var y=l.stateNode;y!==null&&typeof y.componentDidCatch=="function"&&(t.callback=function(){dm(n,l,r),typeof f!="function"&&(ii===null?ii=new Set([this]):ii.add(this));var _=r.stack;this.componentDidCatch(r.value,{componentStack:_!==null?_:""})})}function Fy(t,n,l,r,f){if(l.flags|=32768,r!==null&&typeof r=="object"&&typeof r.then=="function"){if(n=l.alternate,n!==null&&da(n,l,f,!0),l=Qe.current,l!==null){switch(l.tag){case 13:return fn===null?_u():l.alternate===null&&Yt===0&&(Yt=3),l.flags&=-257,l.flags|=65536,l.lanes=f,r===Ac?l.flags|=16384:(n=l.updateQueue,n===null?l.updateQueue=new Set([r]):n.add(r),Mu(t,r,f)),!1;case 22:return l.flags|=65536,r===Ac?l.flags|=16384:(n=l.updateQueue,n===null?(n={transitions:null,markerInstances:null,retryQueue:new Set([r])},l.updateQueue=n):(l=n.retryQueue,l===null?n.retryQueue=new Set([r]):l.add(r)),Mu(t,r,f)),!1}throw Error(a(435,l.tag))}return Mu(t,r,f),_u(),!1}if(St)return n=Qe.current,n!==null?((n.flags&65536)===0&&(n.flags|=256),n.flags|=65536,n.lanes=f,r!==yc&&(t=Error(a(422),{cause:r}),ha(Fe(t,l)))):(r!==yc&&(n=Error(a(423),{cause:r}),ha(Fe(n,l))),t=t.current.alternate,t.flags|=65536,f&=-f,t.lanes|=f,r=Fe(r,l),f=Jc(t.stateNode,r,f),Oc(t,f),Yt!==4&&(Yt=2)),!1;var d=Error(a(520),{cause:r});if(d=Fe(d,l),Ra===null?Ra=[d]:Ra.push(d),Yt!==4&&(Yt=2),n===null)return!0;r=Fe(r,l),l=n;do{switch(l.tag){case 3:return l.flags|=65536,t=f&-f,l.lanes|=t,t=Jc(l.stateNode,r,t),Oc(l,t),!1;case 1:if(n=l.type,d=l.stateNode,(l.flags&128)===0&&(typeof n.getDerivedStateFromError=="function"||d!==null&&typeof d.componentDidCatch=="function"&&(ii===null||!ii.has(d))))return l.flags|=65536,f&=-f,l.lanes|=f,f=mm(f),gm(f,t,l,r),Oc(l,f),!1}l=l.return}while(l!==null);return!1}var pm=Error(a(461)),ne=!1;function oe(t,n,l,r){n.child=t===null?lm(n,null,l,r):Ss(n,t.child,l,r)}function bm(t,n,l,r,f){l=l.render;var d=n.ref;if("ref"in r){var y={};for(var _ in r)_!=="ref"&&(y[_]=r[_])}else y=r;return Oi(n),r=kc(t,n,l,y,d,f),_=Bc(),t!==null&&!ne?(Nc(t,n,f),zn(t,n,f)):(St&&_&&bc(n),n.flags|=1,oe(t,n,r,f),n.child)}function xm(t,n,l,r,f){if(t===null){var d=l.type;return typeof d=="function"&&!mc(d)&&d.defaultProps===void 0&&l.compare===null?(n.tag=15,n.type=d,ym(t,n,d,r,f)):(t=$l(l.type,null,r,n,n.mode,f),t.ref=n.ref,t.return=n,n.child=t)}if(d=t.child,!au(t,f)){var y=d.memoizedProps;if(l=l.compare,l=l!==null?l:ra,l(y,r)&&t.ref===n.ref)return zn(t,n,f)}return n.flags|=1,t=wn(d,r),t.ref=n.ref,t.return=n,n.child=t}function ym(t,n,l,r,f){if(t!==null){var d=t.memoizedProps;if(ra(d,r)&&t.ref===n.ref)if(ne=!1,n.pendingProps=r=d,au(t,f))(t.flags&131072)!==0&&(ne=!0);else return n.lanes=t.lanes,zn(t,n,f)}return Ic(t,n,l,r,f)}function vm(t,n,l){var r=n.pendingProps,f=r.children,d=t!==null?t.memoizedState:null;if(r.mode==="hidden"){if((n.flags&128)!==0){if(r=d!==null?d.baseLanes|l:l,t!==null){for(f=n.child=t.child,d=0;f!==null;)d=d|f.lanes|f.childLanes,f=f.sibling;n.childLanes=d&~r}else n.childLanes=0,n.child=null;return _m(t,n,r,l)}if((l&536870912)!==0)n.memoizedState={baseLanes:0,cachePool:null},t!==null&&so(n,d!==null?d.cachePool:null),d!==null?yd(n,d):zc(),om(n);else return n.lanes=n.childLanes=536870912,_m(t,n,d!==null?d.baseLanes|l:l,l)}else d!==null?(so(n,d.cachePool),yd(n,d),Jn(),n.memoizedState=null):(t!==null&&so(n,null),zc(),Jn());return oe(t,n,f,l),n.child}function _m(t,n,l,r){var f=Tc();return f=f===null?null:{parent:It._currentValue,pool:f},n.memoizedState={baseLanes:l,cachePool:f},t!==null&&so(n,null),zc(),om(n),t!==null&&da(t,n,r,!0),null}function So(t,n){var l=n.ref;if(l===null)t!==null&&t.ref!==null&&(n.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(a(284));(t===null||t.ref!==l)&&(n.flags|=4194816)}}function Ic(t,n,l,r,f){return Oi(n),l=kc(t,n,l,r,void 0,f),r=Bc(),t!==null&&!ne?(Nc(t,n,f),zn(t,n,f)):(St&&r&&bc(n),n.flags|=1,oe(t,n,l,f),n.child)}function Sm(t,n,l,r,f,d){return Oi(n),n.updateQueue=null,l=_d(n,r,l,f),vd(t),r=Bc(),t!==null&&!ne?(Nc(t,n,d),zn(t,n,d)):(St&&r&&bc(n),n.flags|=1,oe(t,n,l,d),n.child)}function Mm(t,n,l,r,f){if(Oi(n),n.stateNode===null){var d=hs,y=l.contextType;typeof y=="object"&&y!==null&&(d=he(y)),d=new l(r,d),n.memoizedState=d.state!==null&&d.state!==void 0?d.state:null,d.updater=Pc,n.stateNode=d,d._reactInternals=n,d=n.stateNode,d.props=r,d.state=n.memoizedState,d.refs={},jc(n),y=l.contextType,d.context=typeof y=="object"&&y!==null?he(y):hs,d.state=n.memoizedState,y=l.getDerivedStateFromProps,typeof y=="function"&&(Wc(n,l,y,r),d.state=n.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof d.getSnapshotBeforeUpdate=="function"||typeof d.UNSAFE_componentWillMount!="function"&&typeof d.componentWillMount!="function"||(y=d.state,typeof d.componentWillMount=="function"&&d.componentWillMount(),typeof d.UNSAFE_componentWillMount=="function"&&d.UNSAFE_componentWillMount(),y!==d.state&&Pc.enqueueReplaceState(d,d.state,null),va(n,r,d,f),ya(),d.state=n.memoizedState),typeof d.componentDidMount=="function"&&(n.flags|=4194308),r=!0}else if(t===null){d=n.stateNode;var _=n.memoizedProps,A=Ci(l,_);d.props=A;var z=d.context,N=l.contextType;y=hs,typeof N=="object"&&N!==null&&(y=he(N));var U=l.getDerivedStateFromProps;N=typeof U=="function"||typeof d.getSnapshotBeforeUpdate=="function",_=n.pendingProps!==_,N||typeof d.UNSAFE_componentWillReceiveProps!="function"&&typeof d.componentWillReceiveProps!="function"||(_||z!==y)&&cm(n,d,r,y),Qn=!1;var C=n.memoizedState;d.state=C,va(n,r,d,f),ya(),z=n.memoizedState,_||C!==z||Qn?(typeof U=="function"&&(Wc(n,l,U,r),z=n.memoizedState),(A=Qn||rm(n,l,A,r,C,z,y))?(N||typeof d.UNSAFE_componentWillMount!="function"&&typeof d.componentWillMount!="function"||(typeof d.componentWillMount=="function"&&d.componentWillMount(),typeof d.UNSAFE_componentWillMount=="function"&&d.UNSAFE_componentWillMount()),typeof d.componentDidMount=="function"&&(n.flags|=4194308)):(typeof d.componentDidMount=="function"&&(n.flags|=4194308),n.memoizedProps=r,n.memoizedState=z),d.props=r,d.state=z,d.context=y,r=A):(typeof d.componentDidMount=="function"&&(n.flags|=4194308),r=!1)}else{d=n.stateNode,Dc(t,n),y=n.memoizedProps,N=Ci(l,y),d.props=N,U=n.pendingProps,C=d.context,z=l.contextType,A=hs,typeof z=="object"&&z!==null&&(A=he(z)),_=l.getDerivedStateFromProps,(z=typeof _=="function"||typeof d.getSnapshotBeforeUpdate=="function")||typeof d.UNSAFE_componentWillReceiveProps!="function"&&typeof d.componentWillReceiveProps!="function"||(y!==U||C!==A)&&cm(n,d,r,A),Qn=!1,C=n.memoizedState,d.state=C,va(n,r,d,f),ya();var R=n.memoizedState;y!==U||C!==R||Qn||t!==null&&t.dependencies!==null&&no(t.dependencies)?(typeof _=="function"&&(Wc(n,l,_,r),R=n.memoizedState),(N=Qn||rm(n,l,N,r,C,R,A)||t!==null&&t.dependencies!==null&&no(t.dependencies))?(z||typeof d.UNSAFE_componentWillUpdate!="function"&&typeof d.componentWillUpdate!="function"||(typeof d.componentWillUpdate=="function"&&d.componentWillUpdate(r,R,A),typeof d.UNSAFE_componentWillUpdate=="function"&&d.UNSAFE_componentWillUpdate(r,R,A)),typeof d.componentDidUpdate=="function"&&(n.flags|=4),typeof d.getSnapshotBeforeUpdate=="function"&&(n.flags|=1024)):(typeof d.componentDidUpdate!="function"||y===t.memoizedProps&&C===t.memoizedState||(n.flags|=4),typeof d.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&C===t.memoizedState||(n.flags|=1024),n.memoizedProps=r,n.memoizedState=R),d.props=r,d.state=R,d.context=A,r=N):(typeof d.componentDidUpdate!="function"||y===t.memoizedProps&&C===t.memoizedState||(n.flags|=4),typeof d.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&C===t.memoizedState||(n.flags|=1024),r=!1)}return d=r,So(t,n),r=(n.flags&128)!==0,d||r?(d=n.stateNode,l=r&&typeof l.getDerivedStateFromError!="function"?null:d.render(),n.flags|=1,t!==null&&r?(n.child=Ss(n,t.child,null,f),n.child=Ss(n,null,l,f)):oe(t,n,l,f),n.memoizedState=d.state,t=n.child):t=zn(t,n,f),t}function wm(t,n,l,r){return fa(),n.flags|=256,oe(t,n,l,r),n.child}var $c={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function tu(t){return{baseLanes:t,cachePool:fd()}}function eu(t,n,l){return t=t!==null?t.childLanes&~l:0,n&&(t|=Ze),t}function Tm(t,n,l){var r=n.pendingProps,f=!1,d=(n.flags&128)!==0,y;if((y=d)||(y=t!==null&&t.memoizedState===null?!1:($t.current&2)!==0),y&&(f=!0,n.flags&=-129),y=(n.flags&32)!==0,n.flags&=-33,t===null){if(St){if(f?Pn(n):Jn(),St){var _=Ft,A;if(A=_){t:{for(A=_,_=un;A.nodeType!==8;){if(!_){_=null;break t}if(A=an(A.nextSibling),A===null){_=null;break t}}_=A}_!==null?(n.memoizedState={dehydrated:_,treeContext:wi!==null?{id:Tn,overflow:An}:null,retryLane:536870912,hydrationErrors:null},A=Ce(18,null,null,0),A.stateNode=_,A.return=n,n.child=A,xe=n,Ft=null,A=!0):A=!1}A||ji(n)}if(_=n.memoizedState,_!==null&&(_=_.dehydrated,_!==null))return Hu(_)?n.lanes=32:n.lanes=536870912,null;En(n)}return _=r.children,r=r.fallback,f?(Jn(),f=n.mode,_=Mo({mode:"hidden",children:_},f),r=Mi(r,f,l,null),_.return=n,r.return=n,_.sibling=r,n.child=_,f=n.child,f.memoizedState=tu(l),f.childLanes=eu(t,y,l),n.memoizedState=$c,r):(Pn(n),nu(n,_))}if(A=t.memoizedState,A!==null&&(_=A.dehydrated,_!==null)){if(d)n.flags&256?(Pn(n),n.flags&=-257,n=iu(t,n,l)):n.memoizedState!==null?(Jn(),n.child=t.child,n.flags|=128,n=null):(Jn(),f=r.fallback,_=n.mode,r=Mo({mode:"visible",children:r.children},_),f=Mi(f,_,l,null),f.flags|=2,r.return=n,f.return=n,r.sibling=f,n.child=r,Ss(n,t.child,null,l),r=n.child,r.memoizedState=tu(l),r.childLanes=eu(t,y,l),n.memoizedState=$c,n=f);else if(Pn(n),Hu(_)){if(y=_.nextSibling&&_.nextSibling.dataset,y)var z=y.dgst;y=z,r=Error(a(419)),r.stack="",r.digest=y,ha({value:r,source:null,stack:null}),n=iu(t,n,l)}else if(ne||da(t,n,l,!1),y=(l&t.childLanes)!==0,ne||y){if(y=kt,y!==null&&(r=l&-l,r=(r&42)!==0?1:Ur(r),r=(r&(y.suspendedLanes|l))!==0?0:r,r!==0&&r!==A.retryLane))throw A.retryLane=r,fs(t,r),Le(y,t,r),pm;_.data==="$?"||_u(),n=iu(t,n,l)}else _.data==="$?"?(n.flags|=192,n.child=t.child,n=null):(t=A.treeContext,Ft=an(_.nextSibling),xe=n,St=!0,Ai=null,un=!1,t!==null&&(Xe[Ge++]=Tn,Xe[Ge++]=An,Xe[Ge++]=wi,Tn=t.id,An=t.overflow,wi=n),n=nu(n,r.children),n.flags|=4096);return n}return f?(Jn(),f=r.fallback,_=n.mode,A=t.child,z=A.sibling,r=wn(A,{mode:"hidden",children:r.children}),r.subtreeFlags=A.subtreeFlags&65011712,z!==null?f=wn(z,f):(f=Mi(f,_,l,null),f.flags|=2),f.return=n,r.return=n,r.sibling=f,n.child=r,r=f,f=n.child,_=t.child.memoizedState,_===null?_=tu(l):(A=_.cachePool,A!==null?(z=It._currentValue,A=A.parent!==z?{parent:z,pool:z}:A):A=fd(),_={baseLanes:_.baseLanes|l,cachePool:A}),f.memoizedState=_,f.childLanes=eu(t,y,l),n.memoizedState=$c,r):(Pn(n),l=t.child,t=l.sibling,l=wn(l,{mode:"visible",children:r.children}),l.return=n,l.sibling=null,t!==null&&(y=n.deletions,y===null?(n.deletions=[t],n.flags|=16):y.push(t)),n.child=l,n.memoizedState=null,l)}function nu(t,n){return n=Mo({mode:"visible",children:n},t.mode),n.return=t,t.child=n}function Mo(t,n){return t=Ce(22,t,null,n),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function iu(t,n,l){return Ss(n,t.child,null,l),t=nu(n,n.pendingProps.children),t.flags|=2,n.memoizedState=null,t}function Am(t,n,l){t.lanes|=n;var r=t.alternate;r!==null&&(r.lanes|=n),_c(t.return,n,l)}function su(t,n,l,r,f){var d=t.memoizedState;d===null?t.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:r,tail:l,tailMode:f}:(d.isBackwards=n,d.rendering=null,d.renderingStartTime=0,d.last=r,d.tail=l,d.tailMode=f)}function jm(t,n,l){var r=n.pendingProps,f=r.revealOrder,d=r.tail;if(oe(t,n,r.children,l),r=$t.current,(r&2)!==0)r=r&1|2,n.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=n.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Am(t,l,n);else if(t.tag===19)Am(t,l,n);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===n)break t;for(;t.sibling===null;){if(t.return===null||t.return===n)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}switch(ct($t,r),f){case"forwards":for(l=n.child,f=null;l!==null;)t=l.alternate,t!==null&&yo(t)===null&&(f=l),l=l.sibling;l=f,l===null?(f=n.child,n.child=null):(f=l.sibling,l.sibling=null),su(n,!1,f,l,d);break;case"backwards":for(l=null,f=n.child,n.child=null;f!==null;){if(t=f.alternate,t!==null&&yo(t)===null){n.child=f;break}t=f.sibling,f.sibling=l,l=f,f=t}su(n,!0,l,null,d);break;case"together":su(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function zn(t,n,l){if(t!==null&&(n.dependencies=t.dependencies),ni|=n.lanes,(l&n.childLanes)===0)if(t!==null){if(da(t,n,l,!1),(l&n.childLanes)===0)return null}else return null;if(t!==null&&n.child!==t.child)throw Error(a(153));if(n.child!==null){for(t=n.child,l=wn(t,t.pendingProps),n.child=l,l.return=n;t.sibling!==null;)t=t.sibling,l=l.sibling=wn(t,t.pendingProps),l.return=n;l.sibling=null}return n.child}function au(t,n){return(t.lanes&n)!==0?!0:(t=t.dependencies,!!(t!==null&&no(t)))}function Yy(t,n,l){switch(n.tag){case 3:Rl(n,n.stateNode.containerInfo),Gn(n,It,t.memoizedState.cache),fa();break;case 27:case 5:kr(n);break;case 4:Rl(n,n.stateNode.containerInfo);break;case 10:Gn(n,n.type,n.memoizedProps.value);break;case 13:var r=n.memoizedState;if(r!==null)return r.dehydrated!==null?(Pn(n),n.flags|=128,null):(l&n.child.childLanes)!==0?Tm(t,n,l):(Pn(n),t=zn(t,n,l),t!==null?t.sibling:null);Pn(n);break;case 19:var f=(t.flags&128)!==0;if(r=(l&n.childLanes)!==0,r||(da(t,n,l,!1),r=(l&n.childLanes)!==0),f){if(r)return jm(t,n,l);n.flags|=128}if(f=n.memoizedState,f!==null&&(f.rendering=null,f.tail=null,f.lastEffect=null),ct($t,$t.current),r)break;return null;case 22:case 23:return n.lanes=0,vm(t,n,l);case 24:Gn(n,It,t.memoizedState.cache)}return zn(t,n,l)}function Dm(t,n,l){if(t!==null)if(t.memoizedProps!==n.pendingProps)ne=!0;else{if(!au(t,l)&&(n.flags&128)===0)return ne=!1,Yy(t,n,l);ne=(t.flags&131072)!==0}else ne=!1,St&&(n.flags&1048576)!==0&&sd(n,eo,n.index);switch(n.lanes=0,n.tag){case 16:t:{t=n.pendingProps;var r=n.elementType,f=r._init;if(r=f(r._payload),n.type=r,typeof r=="function")mc(r)?(t=Ci(r,t),n.tag=1,n=Mm(null,n,r,t,l)):(n.tag=0,n=Ic(null,n,r,t,l));else{if(r!=null){if(f=r.$$typeof,f===G){n.tag=11,n=bm(null,n,r,t,l);break t}else if(f===X){n.tag=14,n=xm(null,n,r,t,l);break t}}throw n=Ht(r)||r,Error(a(306,n,""))}}return n;case 0:return Ic(t,n,n.type,n.pendingProps,l);case 1:return r=n.type,f=Ci(r,n.pendingProps),Mm(t,n,r,f,l);case 3:t:{if(Rl(n,n.stateNode.containerInfo),t===null)throw Error(a(387));r=n.pendingProps;var d=n.memoizedState;f=d.element,Dc(t,n),va(n,r,null,l);var y=n.memoizedState;if(r=y.cache,Gn(n,It,r),r!==d.cache&&Sc(n,[It],l,!0),ya(),r=y.element,d.isDehydrated)if(d={element:r,isDehydrated:!1,cache:y.cache},n.updateQueue.baseState=d,n.memoizedState=d,n.flags&256){n=wm(t,n,r,l);break t}else if(r!==f){f=Fe(Error(a(424)),n),ha(f),n=wm(t,n,r,l);break t}else{switch(t=n.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Ft=an(t.firstChild),xe=n,St=!0,Ai=null,un=!0,l=lm(n,null,r,l),n.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(fa(),r===f){n=zn(t,n,l);break t}oe(t,n,r,l)}n=n.child}return n;case 26:return So(t,n),t===null?(l=Cg(n.type,null,n.pendingProps,null))?n.memoizedState=l:St||(l=n.type,t=n.pendingProps,r=Lo(ue.current).createElement(l),r[fe]=n,r[ve]=t,ce(r,l,t),ee(r),n.stateNode=r):n.memoizedState=Cg(n.type,t.memoizedProps,n.pendingProps,t.memoizedState),null;case 27:return kr(n),t===null&&St&&(r=n.stateNode=Og(n.type,n.pendingProps,ue.current),xe=n,un=!0,f=Ft,li(n.type)?(Uu=f,Ft=an(r.firstChild)):Ft=f),oe(t,n,n.pendingProps.children,l),So(t,n),t===null&&(n.flags|=4194304),n.child;case 5:return t===null&&St&&((f=r=Ft)&&(r=bv(r,n.type,n.pendingProps,un),r!==null?(n.stateNode=r,xe=n,Ft=an(r.firstChild),un=!1,f=!0):f=!1),f||ji(n)),kr(n),f=n.type,d=n.pendingProps,y=t!==null?t.memoizedProps:null,r=d.children,Bu(f,d)?r=null:y!==null&&Bu(f,y)&&(n.flags|=32),n.memoizedState!==null&&(f=kc(t,n,By,null,null,l),Fa._currentValue=f),So(t,n),oe(t,n,r,l),n.child;case 6:return t===null&&St&&((t=l=Ft)&&(l=xv(l,n.pendingProps,un),l!==null?(n.stateNode=l,xe=n,Ft=null,t=!0):t=!1),t||ji(n)),null;case 13:return Tm(t,n,l);case 4:return Rl(n,n.stateNode.containerInfo),r=n.pendingProps,t===null?n.child=Ss(n,null,r,l):oe(t,n,r,l),n.child;case 11:return bm(t,n,n.type,n.pendingProps,l);case 7:return oe(t,n,n.pendingProps,l),n.child;case 8:return oe(t,n,n.pendingProps.children,l),n.child;case 12:return oe(t,n,n.pendingProps.children,l),n.child;case 10:return r=n.pendingProps,Gn(n,n.type,r.value),oe(t,n,r.children,l),n.child;case 9:return f=n.type._context,r=n.pendingProps.children,Oi(n),f=he(f),r=r(f),n.flags|=1,oe(t,n,r,l),n.child;case 14:return xm(t,n,n.type,n.pendingProps,l);case 15:return ym(t,n,n.type,n.pendingProps,l);case 19:return jm(t,n,l);case 31:return r=n.pendingProps,l=n.mode,r={mode:r.mode,children:r.children},t===null?(l=Mo(r,l),l.ref=n.ref,n.child=l,l.return=n,n=l):(l=wn(t.child,r),l.ref=n.ref,n.child=l,l.return=n,n=l),n;case 22:return vm(t,n,l);case 24:return Oi(n),r=he(It),t===null?(f=Tc(),f===null&&(f=kt,d=Mc(),f.pooledCache=d,d.refCount++,d!==null&&(f.pooledCacheLanes|=l),f=d),n.memoizedState={parent:r,cache:f},jc(n),Gn(n,It,f)):((t.lanes&l)!==0&&(Dc(t,n),va(n,null,null,l),ya()),f=t.memoizedState,d=n.memoizedState,f.parent!==r?(f={parent:r,cache:r},n.memoizedState=f,n.lanes===0&&(n.memoizedState=n.updateQueue.baseState=f),Gn(n,It,r)):(r=d.cache,Gn(n,It,r),r!==f.cache&&Sc(n,[It],l,!0))),oe(t,n,n.pendingProps.children,l),n.child;case 29:throw n.pendingProps}throw Error(a(156,n.tag))}function Cn(t){t.flags|=4}function Om(t,n){if(n.type!=="stylesheet"||(n.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Lg(n)){if(n=Qe.current,n!==null&&((bt&4194048)===bt?fn!==null:(bt&62914560)!==bt&&(bt&536870912)===0||n!==fn))throw ba=Ac,hd;t.flags|=8192}}function wo(t,n){n!==null&&(t.flags|=4),t.flags&16384&&(n=t.tag!==22?oh():536870912,t.lanes|=n,As|=n)}function ja(t,n){if(!St)switch(t.tailMode){case"hidden":n=t.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var r=null;l!==null;)l.alternate!==null&&(r=l),l=l.sibling;r===null?n||t.tail===null?t.tail=null:t.tail.sibling=null:r.sibling=null}}function qt(t){var n=t.alternate!==null&&t.alternate.child===t.child,l=0,r=0;if(n)for(var f=t.child;f!==null;)l|=f.lanes|f.childLanes,r|=f.subtreeFlags&65011712,r|=f.flags&65011712,f.return=t,f=f.sibling;else for(f=t.child;f!==null;)l|=f.lanes|f.childLanes,r|=f.subtreeFlags,r|=f.flags,f.return=t,f=f.sibling;return t.subtreeFlags|=r,t.childLanes=l,n}function Xy(t,n,l){var r=n.pendingProps;switch(xc(n),n.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qt(n),null;case 1:return qt(n),null;case 3:return l=n.stateNode,r=null,t!==null&&(r=t.memoizedState.cache),n.memoizedState.cache!==r&&(n.flags|=2048),Dn(It),Pi(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(ua(n)?Cn(n):t===null||t.memoizedState.isDehydrated&&(n.flags&256)===0||(n.flags|=1024,od())),qt(n),null;case 26:return l=n.memoizedState,t===null?(Cn(n),l!==null?(qt(n),Om(n,l)):(qt(n),n.flags&=-16777217)):l?l!==t.memoizedState?(Cn(n),qt(n),Om(n,l)):(qt(n),n.flags&=-16777217):(t.memoizedProps!==r&&Cn(n),qt(n),n.flags&=-16777217),null;case 27:kl(n),l=ue.current;var f=n.type;if(t!==null&&n.stateNode!=null)t.memoizedProps!==r&&Cn(n);else{if(!r){if(n.stateNode===null)throw Error(a(166));return qt(n),null}t=_t.current,ua(n)?ad(n):(t=Og(f,r,l),n.stateNode=t,Cn(n))}return qt(n),null;case 5:if(kl(n),l=n.type,t!==null&&n.stateNode!=null)t.memoizedProps!==r&&Cn(n);else{if(!r){if(n.stateNode===null)throw Error(a(166));return qt(n),null}if(t=_t.current,ua(n))ad(n);else{switch(f=Lo(ue.current),t){case 1:t=f.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=f.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=f.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=f.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=f.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof r.is=="string"?f.createElement("select",{is:r.is}):f.createElement("select"),r.multiple?t.multiple=!0:r.size&&(t.size=r.size);break;default:t=typeof r.is=="string"?f.createElement(l,{is:r.is}):f.createElement(l)}}t[fe]=n,t[ve]=r;t:for(f=n.child;f!==null;){if(f.tag===5||f.tag===6)t.appendChild(f.stateNode);else if(f.tag!==4&&f.tag!==27&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===n)break t;for(;f.sibling===null;){if(f.return===null||f.return===n)break t;f=f.return}f.sibling.return=f.return,f=f.sibling}n.stateNode=t;t:switch(ce(t,l,r),l){case"button":case"input":case"select":case"textarea":t=!!r.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Cn(n)}}return qt(n),n.flags&=-16777217,null;case 6:if(t&&n.stateNode!=null)t.memoizedProps!==r&&Cn(n);else{if(typeof r!="string"&&n.stateNode===null)throw Error(a(166));if(t=ue.current,ua(n)){if(t=n.stateNode,l=n.memoizedProps,r=null,f=xe,f!==null)switch(f.tag){case 27:case 5:r=f.memoizedProps}t[fe]=n,t=!!(t.nodeValue===l||r!==null&&r.suppressHydrationWarning===!0||Sg(t.nodeValue,l)),t||ji(n)}else t=Lo(t).createTextNode(r),t[fe]=n,n.stateNode=t}return qt(n),null;case 13:if(r=n.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(f=ua(n),r!==null&&r.dehydrated!==null){if(t===null){if(!f)throw Error(a(318));if(f=n.memoizedState,f=f!==null?f.dehydrated:null,!f)throw Error(a(317));f[fe]=n}else fa(),(n.flags&128)===0&&(n.memoizedState=null),n.flags|=4;qt(n),f=!1}else f=od(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=f),f=!0;if(!f)return n.flags&256?(En(n),n):(En(n),null)}if(En(n),(n.flags&128)!==0)return n.lanes=l,n;if(l=r!==null,t=t!==null&&t.memoizedState!==null,l){r=n.child,f=null,r.alternate!==null&&r.alternate.memoizedState!==null&&r.alternate.memoizedState.cachePool!==null&&(f=r.alternate.memoizedState.cachePool.pool);var d=null;r.memoizedState!==null&&r.memoizedState.cachePool!==null&&(d=r.memoizedState.cachePool.pool),d!==f&&(r.flags|=2048)}return l!==t&&l&&(n.child.flags|=8192),wo(n,n.updateQueue),qt(n),null;case 4:return Pi(),t===null&&Eu(n.stateNode.containerInfo),qt(n),null;case 10:return Dn(n.type),qt(n),null;case 19:if(ft($t),f=n.memoizedState,f===null)return qt(n),null;if(r=(n.flags&128)!==0,d=f.rendering,d===null)if(r)ja(f,!1);else{if(Yt!==0||t!==null&&(t.flags&128)!==0)for(t=n.child;t!==null;){if(d=yo(t),d!==null){for(n.flags|=128,ja(f,!1),t=d.updateQueue,n.updateQueue=t,wo(n,t),n.subtreeFlags=0,t=l,l=n.child;l!==null;)id(l,t),l=l.sibling;return ct($t,$t.current&1|2),n.child}t=t.sibling}f.tail!==null&&cn()>jo&&(n.flags|=128,r=!0,ja(f,!1),n.lanes=4194304)}else{if(!r)if(t=yo(d),t!==null){if(n.flags|=128,r=!0,t=t.updateQueue,n.updateQueue=t,wo(n,t),ja(f,!0),f.tail===null&&f.tailMode==="hidden"&&!d.alternate&&!St)return qt(n),null}else 2*cn()-f.renderingStartTime>jo&&l!==536870912&&(n.flags|=128,r=!0,ja(f,!1),n.lanes=4194304);f.isBackwards?(d.sibling=n.child,n.child=d):(t=f.last,t!==null?t.sibling=d:n.child=d,f.last=d)}return f.tail!==null?(n=f.tail,f.rendering=n,f.tail=n.sibling,f.renderingStartTime=cn(),n.sibling=null,t=$t.current,ct($t,r?t&1|2:t&1),n):(qt(n),null);case 22:case 23:return En(n),Cc(),r=n.memoizedState!==null,t!==null?t.memoizedState!==null!==r&&(n.flags|=8192):r&&(n.flags|=8192),r?(l&536870912)!==0&&(n.flags&128)===0&&(qt(n),n.subtreeFlags&6&&(n.flags|=8192)):qt(n),l=n.updateQueue,l!==null&&wo(n,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),r=null,n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(r=n.memoizedState.cachePool.pool),r!==l&&(n.flags|=2048),t!==null&&ft(Ei),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),n.memoizedState.cache!==l&&(n.flags|=2048),Dn(It),qt(n),null;case 25:return null;case 30:return null}throw Error(a(156,n.tag))}function Gy(t,n){switch(xc(n),n.tag){case 1:return t=n.flags,t&65536?(n.flags=t&-65537|128,n):null;case 3:return Dn(It),Pi(),t=n.flags,(t&65536)!==0&&(t&128)===0?(n.flags=t&-65537|128,n):null;case 26:case 27:case 5:return kl(n),null;case 13:if(En(n),t=n.memoizedState,t!==null&&t.dehydrated!==null){if(n.alternate===null)throw Error(a(340));fa()}return t=n.flags,t&65536?(n.flags=t&-65537|128,n):null;case 19:return ft($t),null;case 4:return Pi(),null;case 10:return Dn(n.type),null;case 22:case 23:return En(n),Cc(),t!==null&&ft(Ei),t=n.flags,t&65536?(n.flags=t&-65537|128,n):null;case 24:return Dn(It),null;case 25:return null;default:return null}}function Em(t,n){switch(xc(n),n.tag){case 3:Dn(It),Pi();break;case 26:case 27:case 5:kl(n);break;case 4:Pi();break;case 13:En(n);break;case 19:ft($t);break;case 10:Dn(n.type);break;case 22:case 23:En(n),Cc(),t!==null&&ft(Ei);break;case 24:Dn(It)}}function Da(t,n){try{var l=n.updateQueue,r=l!==null?l.lastEffect:null;if(r!==null){var f=r.next;l=f;do{if((l.tag&t)===t){r=void 0;var d=l.create,y=l.inst;r=d(),y.destroy=r}l=l.next}while(l!==f)}}catch(_){Rt(n,n.return,_)}}function In(t,n,l){try{var r=n.updateQueue,f=r!==null?r.lastEffect:null;if(f!==null){var d=f.next;r=d;do{if((r.tag&t)===t){var y=r.inst,_=y.destroy;if(_!==void 0){y.destroy=void 0,f=n;var A=l,z=_;try{z()}catch(N){Rt(f,A,N)}}}r=r.next}while(r!==d)}}catch(N){Rt(n,n.return,N)}}function zm(t){var n=t.updateQueue;if(n!==null){var l=t.stateNode;try{xd(n,l)}catch(r){Rt(t,t.return,r)}}}function Cm(t,n,l){l.props=Ci(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(r){Rt(t,n,r)}}function Oa(t,n){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var r=t.stateNode;break;case 30:r=t.stateNode;break;default:r=t.stateNode}typeof l=="function"?t.refCleanup=l(r):l.current=r}}catch(f){Rt(t,n,f)}}function hn(t,n){var l=t.ref,r=t.refCleanup;if(l!==null)if(typeof r=="function")try{r()}catch(f){Rt(t,n,f)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(f){Rt(t,n,f)}else l.current=null}function Rm(t){var n=t.type,l=t.memoizedProps,r=t.stateNode;try{t:switch(n){case"button":case"input":case"select":case"textarea":l.autoFocus&&r.focus();break t;case"img":l.src?r.src=l.src:l.srcSet&&(r.srcset=l.srcSet)}}catch(f){Rt(t,t.return,f)}}function lu(t,n,l){try{var r=t.stateNode;hv(r,t.type,l,n),r[ve]=n}catch(f){Rt(t,t.return,f)}}function km(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&li(t.type)||t.tag===4}function ou(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||km(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&li(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function ru(t,n,l){var r=t.tag;if(r===5||r===6)t=t.stateNode,n?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,n):(n=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,n.appendChild(t),l=l._reactRootContainer,l!=null||n.onclick!==null||(n.onclick=No));else if(r!==4&&(r===27&&li(t.type)&&(l=t.stateNode,n=null),t=t.child,t!==null))for(ru(t,n,l),t=t.sibling;t!==null;)ru(t,n,l),t=t.sibling}function To(t,n,l){var r=t.tag;if(r===5||r===6)t=t.stateNode,n?l.insertBefore(t,n):l.appendChild(t);else if(r!==4&&(r===27&&li(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(To(t,n,l),t=t.sibling;t!==null;)To(t,n,l),t=t.sibling}function Bm(t){var n=t.stateNode,l=t.memoizedProps;try{for(var r=t.type,f=n.attributes;f.length;)n.removeAttributeNode(f[0]);ce(n,r,l),n[fe]=t,n[ve]=l}catch(d){Rt(t,t.return,d)}}var Rn=!1,Gt=!1,cu=!1,Nm=typeof WeakSet=="function"?WeakSet:Set,ie=null;function Qy(t,n){if(t=t.containerInfo,Ru=Yo,t=Zh(t),oc(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var r=l.getSelection&&l.getSelection();if(r&&r.rangeCount!==0){l=r.anchorNode;var f=r.anchorOffset,d=r.focusNode;r=r.focusOffset;try{l.nodeType,d.nodeType}catch{l=null;break t}var y=0,_=-1,A=-1,z=0,N=0,U=t,C=null;e:for(;;){for(var R;U!==l||f!==0&&U.nodeType!==3||(_=y+f),U!==d||r!==0&&U.nodeType!==3||(A=y+r),U.nodeType===3&&(y+=U.nodeValue.length),(R=U.firstChild)!==null;)C=U,U=R;for(;;){if(U===t)break e;if(C===l&&++z===f&&(_=y),C===d&&++N===r&&(A=y),(R=U.nextSibling)!==null)break;U=C,C=U.parentNode}U=R}l=_===-1||A===-1?null:{start:_,end:A}}else l=null}l=l||{start:0,end:0}}else l=null;for(ku={focusedElem:t,selectionRange:l},Yo=!1,ie=n;ie!==null;)if(n=ie,t=n.child,(n.subtreeFlags&1024)!==0&&t!==null)t.return=n,ie=t;else for(;ie!==null;){switch(n=ie,d=n.alternate,t=n.flags,n.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&d!==null){t=void 0,l=n,f=d.memoizedProps,d=d.memoizedState,r=l.stateNode;try{var it=Ci(l.type,f,l.elementType===l.type);t=r.getSnapshotBeforeUpdate(it,d),r.__reactInternalSnapshotBeforeUpdate=t}catch($){Rt(l,l.return,$)}}break;case 3:if((t&1024)!==0){if(t=n.stateNode.containerInfo,l=t.nodeType,l===9)Lu(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":Lu(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(a(163))}if(t=n.sibling,t!==null){t.return=n.return,ie=t;break}ie=n.return}}function Lm(t,n,l){var r=l.flags;switch(l.tag){case 0:case 11:case 15:$n(t,l),r&4&&Da(5,l);break;case 1:if($n(t,l),r&4)if(t=l.stateNode,n===null)try{t.componentDidMount()}catch(y){Rt(l,l.return,y)}else{var f=Ci(l.type,n.memoizedProps);n=n.memoizedState;try{t.componentDidUpdate(f,n,t.__reactInternalSnapshotBeforeUpdate)}catch(y){Rt(l,l.return,y)}}r&64&&zm(l),r&512&&Oa(l,l.return);break;case 3:if($n(t,l),r&64&&(t=l.updateQueue,t!==null)){if(n=null,l.child!==null)switch(l.child.tag){case 27:case 5:n=l.child.stateNode;break;case 1:n=l.child.stateNode}try{xd(t,n)}catch(y){Rt(l,l.return,y)}}break;case 27:n===null&&r&4&&Bm(l);case 26:case 5:$n(t,l),n===null&&r&4&&Rm(l),r&512&&Oa(l,l.return);break;case 12:$n(t,l);break;case 13:$n(t,l),r&4&&qm(t,l),r&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=ev.bind(null,l),yv(t,l))));break;case 22:if(r=l.memoizedState!==null||Rn,!r){n=n!==null&&n.memoizedState!==null||Gt,f=Rn;var d=Gt;Rn=r,(Gt=n)&&!d?ti(t,l,(l.subtreeFlags&8772)!==0):$n(t,l),Rn=f,Gt=d}break;case 30:break;default:$n(t,l)}}function Hm(t){var n=t.alternate;n!==null&&(t.alternate=null,Hm(n)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(n=t.stateNode,n!==null&&Fr(n)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Ut=null,Me=!1;function kn(t,n,l){for(l=l.child;l!==null;)Um(t,n,l),l=l.sibling}function Um(t,n,l){if(Oe&&typeof Oe.onCommitFiberUnmount=="function")try{Oe.onCommitFiberUnmount(Ps,l)}catch{}switch(l.tag){case 26:Gt||hn(l,n),kn(t,n,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Gt||hn(l,n);var r=Ut,f=Me;li(l.type)&&(Ut=l.stateNode,Me=!1),kn(t,n,l),Ha(l.stateNode),Ut=r,Me=f;break;case 5:Gt||hn(l,n);case 6:if(r=Ut,f=Me,Ut=null,kn(t,n,l),Ut=r,Me=f,Ut!==null)if(Me)try{(Ut.nodeType===9?Ut.body:Ut.nodeName==="HTML"?Ut.ownerDocument.body:Ut).removeChild(l.stateNode)}catch(d){Rt(l,n,d)}else try{Ut.removeChild(l.stateNode)}catch(d){Rt(l,n,d)}break;case 18:Ut!==null&&(Me?(t=Ut,jg(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),Qa(t)):jg(Ut,l.stateNode));break;case 4:r=Ut,f=Me,Ut=l.stateNode.containerInfo,Me=!0,kn(t,n,l),Ut=r,Me=f;break;case 0:case 11:case 14:case 15:Gt||In(2,l,n),Gt||In(4,l,n),kn(t,n,l);break;case 1:Gt||(hn(l,n),r=l.stateNode,typeof r.componentWillUnmount=="function"&&Cm(l,n,r)),kn(t,n,l);break;case 21:kn(t,n,l);break;case 22:Gt=(r=Gt)||l.memoizedState!==null,kn(t,n,l),Gt=r;break;default:kn(t,n,l)}}function qm(t,n){if(n.memoizedState===null&&(t=n.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Qa(t)}catch(l){Rt(n,n.return,l)}}function Zy(t){switch(t.tag){case 13:case 19:var n=t.stateNode;return n===null&&(n=t.stateNode=new Nm),n;case 22:return t=t.stateNode,n=t._retryCache,n===null&&(n=t._retryCache=new Nm),n;default:throw Error(a(435,t.tag))}}function uu(t,n){var l=Zy(t);n.forEach(function(r){var f=nv.bind(null,t,r);l.has(r)||(l.add(r),r.then(f,f))})}function Re(t,n){var l=n.deletions;if(l!==null)for(var r=0;r<l.length;r++){var f=l[r],d=t,y=n,_=y;t:for(;_!==null;){switch(_.tag){case 27:if(li(_.type)){Ut=_.stateNode,Me=!1;break t}break;case 5:Ut=_.stateNode,Me=!1;break t;case 3:case 4:Ut=_.stateNode.containerInfo,Me=!0;break t}_=_.return}if(Ut===null)throw Error(a(160));Um(d,y,f),Ut=null,Me=!1,d=f.alternate,d!==null&&(d.return=null),f.return=null}if(n.subtreeFlags&13878)for(n=n.child;n!==null;)Vm(n,t),n=n.sibling}var sn=null;function Vm(t,n){var l=t.alternate,r=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Re(n,t),ke(t),r&4&&(In(3,t,t.return),Da(3,t),In(5,t,t.return));break;case 1:Re(n,t),ke(t),r&512&&(Gt||l===null||hn(l,l.return)),r&64&&Rn&&(t=t.updateQueue,t!==null&&(r=t.callbacks,r!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?r:l.concat(r))));break;case 26:var f=sn;if(Re(n,t),ke(t),r&512&&(Gt||l===null||hn(l,l.return)),r&4){var d=l!==null?l.memoizedState:null;if(r=t.memoizedState,l===null)if(r===null)if(t.stateNode===null){t:{r=t.type,l=t.memoizedProps,f=f.ownerDocument||f;e:switch(r){case"title":d=f.getElementsByTagName("title")[0],(!d||d[$s]||d[fe]||d.namespaceURI==="http://www.w3.org/2000/svg"||d.hasAttribute("itemprop"))&&(d=f.createElement(r),f.head.insertBefore(d,f.querySelector("head > title"))),ce(d,r,l),d[fe]=t,ee(d),r=d;break t;case"link":var y=Bg("link","href",f).get(r+(l.href||""));if(y){for(var _=0;_<y.length;_++)if(d=y[_],d.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&d.getAttribute("rel")===(l.rel==null?null:l.rel)&&d.getAttribute("title")===(l.title==null?null:l.title)&&d.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){y.splice(_,1);break e}}d=f.createElement(r),ce(d,r,l),f.head.appendChild(d);break;case"meta":if(y=Bg("meta","content",f).get(r+(l.content||""))){for(_=0;_<y.length;_++)if(d=y[_],d.getAttribute("content")===(l.content==null?null:""+l.content)&&d.getAttribute("name")===(l.name==null?null:l.name)&&d.getAttribute("property")===(l.property==null?null:l.property)&&d.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&d.getAttribute("charset")===(l.charSet==null?null:l.charSet)){y.splice(_,1);break e}}d=f.createElement(r),ce(d,r,l),f.head.appendChild(d);break;default:throw Error(a(468,r))}d[fe]=t,ee(d),r=d}t.stateNode=r}else Ng(f,t.type,t.stateNode);else t.stateNode=kg(f,r,t.memoizedProps);else d!==r?(d===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):d.count--,r===null?Ng(f,t.type,t.stateNode):kg(f,r,t.memoizedProps)):r===null&&t.stateNode!==null&&lu(t,t.memoizedProps,l.memoizedProps)}break;case 27:Re(n,t),ke(t),r&512&&(Gt||l===null||hn(l,l.return)),l!==null&&r&4&&lu(t,t.memoizedProps,l.memoizedProps);break;case 5:if(Re(n,t),ke(t),r&512&&(Gt||l===null||hn(l,l.return)),t.flags&32){f=t.stateNode;try{ss(f,"")}catch(R){Rt(t,t.return,R)}}r&4&&t.stateNode!=null&&(f=t.memoizedProps,lu(t,f,l!==null?l.memoizedProps:f)),r&1024&&(cu=!0);break;case 6:if(Re(n,t),ke(t),r&4){if(t.stateNode===null)throw Error(a(162));r=t.memoizedProps,l=t.stateNode;try{l.nodeValue=r}catch(R){Rt(t,t.return,R)}}break;case 3:if(qo=null,f=sn,sn=Ho(n.containerInfo),Re(n,t),sn=f,ke(t),r&4&&l!==null&&l.memoizedState.isDehydrated)try{Qa(n.containerInfo)}catch(R){Rt(t,t.return,R)}cu&&(cu=!1,Fm(t));break;case 4:r=sn,sn=Ho(t.stateNode.containerInfo),Re(n,t),ke(t),sn=r;break;case 12:Re(n,t),ke(t);break;case 13:Re(n,t),ke(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(pu=cn()),r&4&&(r=t.updateQueue,r!==null&&(t.updateQueue=null,uu(t,r)));break;case 22:f=t.memoizedState!==null;var A=l!==null&&l.memoizedState!==null,z=Rn,N=Gt;if(Rn=z||f,Gt=N||A,Re(n,t),Gt=N,Rn=z,ke(t),r&8192)t:for(n=t.stateNode,n._visibility=f?n._visibility&-2:n._visibility|1,f&&(l===null||A||Rn||Gt||Ri(t)),l=null,n=t;;){if(n.tag===5||n.tag===26){if(l===null){A=l=n;try{if(d=A.stateNode,f)y=d.style,typeof y.setProperty=="function"?y.setProperty("display","none","important"):y.display="none";else{_=A.stateNode;var U=A.memoizedProps.style,C=U!=null&&U.hasOwnProperty("display")?U.display:null;_.style.display=C==null||typeof C=="boolean"?"":(""+C).trim()}}catch(R){Rt(A,A.return,R)}}}else if(n.tag===6){if(l===null){A=n;try{A.stateNode.nodeValue=f?"":A.memoizedProps}catch(R){Rt(A,A.return,R)}}}else if((n.tag!==22&&n.tag!==23||n.memoizedState===null||n===t)&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break t;for(;n.sibling===null;){if(n.return===null||n.return===t)break t;l===n&&(l=null),n=n.return}l===n&&(l=null),n.sibling.return=n.return,n=n.sibling}r&4&&(r=t.updateQueue,r!==null&&(l=r.retryQueue,l!==null&&(r.retryQueue=null,uu(t,l))));break;case 19:Re(n,t),ke(t),r&4&&(r=t.updateQueue,r!==null&&(t.updateQueue=null,uu(t,r)));break;case 30:break;case 21:break;default:Re(n,t),ke(t)}}function ke(t){var n=t.flags;if(n&2){try{for(var l,r=t.return;r!==null;){if(km(r)){l=r;break}r=r.return}if(l==null)throw Error(a(160));switch(l.tag){case 27:var f=l.stateNode,d=ou(t);To(t,d,f);break;case 5:var y=l.stateNode;l.flags&32&&(ss(y,""),l.flags&=-33);var _=ou(t);To(t,_,y);break;case 3:case 4:var A=l.stateNode.containerInfo,z=ou(t);ru(t,z,A);break;default:throw Error(a(161))}}catch(N){Rt(t,t.return,N)}t.flags&=-3}n&4096&&(t.flags&=-4097)}function Fm(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var n=t;Fm(n),n.tag===5&&n.flags&1024&&n.stateNode.reset(),t=t.sibling}}function $n(t,n){if(n.subtreeFlags&8772)for(n=n.child;n!==null;)Lm(t,n.alternate,n),n=n.sibling}function Ri(t){for(t=t.child;t!==null;){var n=t;switch(n.tag){case 0:case 11:case 14:case 15:In(4,n,n.return),Ri(n);break;case 1:hn(n,n.return);var l=n.stateNode;typeof l.componentWillUnmount=="function"&&Cm(n,n.return,l),Ri(n);break;case 27:Ha(n.stateNode);case 26:case 5:hn(n,n.return),Ri(n);break;case 22:n.memoizedState===null&&Ri(n);break;case 30:Ri(n);break;default:Ri(n)}t=t.sibling}}function ti(t,n,l){for(l=l&&(n.subtreeFlags&8772)!==0,n=n.child;n!==null;){var r=n.alternate,f=t,d=n,y=d.flags;switch(d.tag){case 0:case 11:case 15:ti(f,d,l),Da(4,d);break;case 1:if(ti(f,d,l),r=d,f=r.stateNode,typeof f.componentDidMount=="function")try{f.componentDidMount()}catch(z){Rt(r,r.return,z)}if(r=d,f=r.updateQueue,f!==null){var _=r.stateNode;try{var A=f.shared.hiddenCallbacks;if(A!==null)for(f.shared.hiddenCallbacks=null,f=0;f<A.length;f++)bd(A[f],_)}catch(z){Rt(r,r.return,z)}}l&&y&64&&zm(d),Oa(d,d.return);break;case 27:Bm(d);case 26:case 5:ti(f,d,l),l&&r===null&&y&4&&Rm(d),Oa(d,d.return);break;case 12:ti(f,d,l);break;case 13:ti(f,d,l),l&&y&4&&qm(f,d);break;case 22:d.memoizedState===null&&ti(f,d,l),Oa(d,d.return);break;case 30:break;default:ti(f,d,l)}n=n.sibling}}function fu(t,n){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(t=n.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&ma(l))}function hu(t,n){t=null,n.alternate!==null&&(t=n.alternate.memoizedState.cache),n=n.memoizedState.cache,n!==t&&(n.refCount++,t!=null&&ma(t))}function dn(t,n,l,r){if(n.subtreeFlags&10256)for(n=n.child;n!==null;)Ym(t,n,l,r),n=n.sibling}function Ym(t,n,l,r){var f=n.flags;switch(n.tag){case 0:case 11:case 15:dn(t,n,l,r),f&2048&&Da(9,n);break;case 1:dn(t,n,l,r);break;case 3:dn(t,n,l,r),f&2048&&(t=null,n.alternate!==null&&(t=n.alternate.memoizedState.cache),n=n.memoizedState.cache,n!==t&&(n.refCount++,t!=null&&ma(t)));break;case 12:if(f&2048){dn(t,n,l,r),t=n.stateNode;try{var d=n.memoizedProps,y=d.id,_=d.onPostCommit;typeof _=="function"&&_(y,n.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(A){Rt(n,n.return,A)}}else dn(t,n,l,r);break;case 13:dn(t,n,l,r);break;case 23:break;case 22:d=n.stateNode,y=n.alternate,n.memoizedState!==null?d._visibility&2?dn(t,n,l,r):Ea(t,n):d._visibility&2?dn(t,n,l,r):(d._visibility|=2,Ms(t,n,l,r,(n.subtreeFlags&10256)!==0)),f&2048&&fu(y,n);break;case 24:dn(t,n,l,r),f&2048&&hu(n.alternate,n);break;default:dn(t,n,l,r)}}function Ms(t,n,l,r,f){for(f=f&&(n.subtreeFlags&10256)!==0,n=n.child;n!==null;){var d=t,y=n,_=l,A=r,z=y.flags;switch(y.tag){case 0:case 11:case 15:Ms(d,y,_,A,f),Da(8,y);break;case 23:break;case 22:var N=y.stateNode;y.memoizedState!==null?N._visibility&2?Ms(d,y,_,A,f):Ea(d,y):(N._visibility|=2,Ms(d,y,_,A,f)),f&&z&2048&&fu(y.alternate,y);break;case 24:Ms(d,y,_,A,f),f&&z&2048&&hu(y.alternate,y);break;default:Ms(d,y,_,A,f)}n=n.sibling}}function Ea(t,n){if(n.subtreeFlags&10256)for(n=n.child;n!==null;){var l=t,r=n,f=r.flags;switch(r.tag){case 22:Ea(l,r),f&2048&&fu(r.alternate,r);break;case 24:Ea(l,r),f&2048&&hu(r.alternate,r);break;default:Ea(l,r)}n=n.sibling}}var za=8192;function ws(t){if(t.subtreeFlags&za)for(t=t.child;t!==null;)Xm(t),t=t.sibling}function Xm(t){switch(t.tag){case 26:ws(t),t.flags&za&&t.memoizedState!==null&&Cv(sn,t.memoizedState,t.memoizedProps);break;case 5:ws(t);break;case 3:case 4:var n=sn;sn=Ho(t.stateNode.containerInfo),ws(t),sn=n;break;case 22:t.memoizedState===null&&(n=t.alternate,n!==null&&n.memoizedState!==null?(n=za,za=16777216,ws(t),za=n):ws(t));break;default:ws(t)}}function Gm(t){var n=t.alternate;if(n!==null&&(t=n.child,t!==null)){n.child=null;do n=t.sibling,t.sibling=null,t=n;while(t!==null)}}function Ca(t){var n=t.deletions;if((t.flags&16)!==0){if(n!==null)for(var l=0;l<n.length;l++){var r=n[l];ie=r,Zm(r,t)}Gm(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Qm(t),t=t.sibling}function Qm(t){switch(t.tag){case 0:case 11:case 15:Ca(t),t.flags&2048&&In(9,t,t.return);break;case 3:Ca(t);break;case 12:Ca(t);break;case 22:var n=t.stateNode;t.memoizedState!==null&&n._visibility&2&&(t.return===null||t.return.tag!==13)?(n._visibility&=-3,Ao(t)):Ca(t);break;default:Ca(t)}}function Ao(t){var n=t.deletions;if((t.flags&16)!==0){if(n!==null)for(var l=0;l<n.length;l++){var r=n[l];ie=r,Zm(r,t)}Gm(t)}for(t=t.child;t!==null;){switch(n=t,n.tag){case 0:case 11:case 15:In(8,n,n.return),Ao(n);break;case 22:l=n.stateNode,l._visibility&2&&(l._visibility&=-3,Ao(n));break;default:Ao(n)}t=t.sibling}}function Zm(t,n){for(;ie!==null;){var l=ie;switch(l.tag){case 0:case 11:case 15:In(8,l,n);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var r=l.memoizedState.cachePool.pool;r!=null&&r.refCount++}break;case 24:ma(l.memoizedState.cache)}if(r=l.child,r!==null)r.return=l,ie=r;else t:for(l=t;ie!==null;){r=ie;var f=r.sibling,d=r.return;if(Hm(r),r===l){ie=null;break t}if(f!==null){f.return=d,ie=f;break t}ie=d}}}var Ky={getCacheForType:function(t){var n=he(It),l=n.data.get(t);return l===void 0&&(l=t(),n.data.set(t,l)),l}},Wy=typeof WeakMap=="function"?WeakMap:Map,At=0,kt=null,ht=null,bt=0,jt=0,Be=null,ei=!1,Ts=!1,du=!1,Bn=0,Yt=0,ni=0,ki=0,mu=0,Ze=0,As=0,Ra=null,we=null,gu=!1,pu=0,jo=1/0,Do=null,ii=null,re=0,si=null,js=null,Ds=0,bu=0,xu=null,Km=null,ka=0,yu=null;function Ne(){if((At&2)!==0&&bt!==0)return bt&-bt;if(k.T!==null){var t=gs;return t!==0?t:Au()}return uh()}function Wm(){Ze===0&&(Ze=(bt&536870912)===0||St?lh():536870912);var t=Qe.current;return t!==null&&(t.flags|=32),Ze}function Le(t,n,l){(t===kt&&(jt===2||jt===9)||t.cancelPendingCommit!==null)&&(Os(t,0),ai(t,bt,Ze,!1)),Is(t,l),((At&2)===0||t!==kt)&&(t===kt&&((At&2)===0&&(ki|=l),Yt===4&&ai(t,bt,Ze,!1)),mn(t))}function Pm(t,n,l){if((At&6)!==0)throw Error(a(327));var r=!l&&(n&124)===0&&(n&t.expiredLanes)===0||Js(t,n),f=r?Iy(t,n):Su(t,n,!0),d=r;do{if(f===0){Ts&&!r&&ai(t,n,0,!1);break}else{if(l=t.current.alternate,d&&!Py(l)){f=Su(t,n,!1),d=!1;continue}if(f===2){if(d=n,t.errorRecoveryDisabledLanes&d)var y=0;else y=t.pendingLanes&-536870913,y=y!==0?y:y&536870912?536870912:0;if(y!==0){n=y;t:{var _=t;f=Ra;var A=_.current.memoizedState.isDehydrated;if(A&&(Os(_,y).flags|=256),y=Su(_,y,!1),y!==2){if(du&&!A){_.errorRecoveryDisabledLanes|=d,ki|=d,f=4;break t}d=we,we=f,d!==null&&(we===null?we=d:we.push.apply(we,d))}f=y}if(d=!1,f!==2)continue}}if(f===1){Os(t,0),ai(t,n,0,!0);break}t:{switch(r=t,d=f,d){case 0:case 1:throw Error(a(345));case 4:if((n&4194048)!==n)break;case 6:ai(r,n,Ze,!ei);break t;case 2:we=null;break;case 3:case 5:break;default:throw Error(a(329))}if((n&62914560)===n&&(f=pu+300-cn(),10<f)){if(ai(r,n,Ze,!ei),Hl(r,0,!0)!==0)break t;r.timeoutHandle=Tg(Jm.bind(null,r,l,we,Do,gu,n,Ze,ki,As,ei,d,2,-0,0),f);break t}Jm(r,l,we,Do,gu,n,Ze,ki,As,ei,d,0,-0,0)}}break}while(!0);mn(t)}function Jm(t,n,l,r,f,d,y,_,A,z,N,U,C,R){if(t.timeoutHandle=-1,U=n.subtreeFlags,(U&8192||(U&16785408)===16785408)&&(Va={stylesheets:null,count:0,unsuspend:zv},Xm(n),U=Rv(),U!==null)){t.cancelPendingCommit=U(sg.bind(null,t,n,d,l,r,f,y,_,A,N,1,C,R)),ai(t,d,y,!z);return}sg(t,n,d,l,r,f,y,_,A)}function Py(t){for(var n=t;;){var l=n.tag;if((l===0||l===11||l===15)&&n.flags&16384&&(l=n.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var r=0;r<l.length;r++){var f=l[r],d=f.getSnapshot;f=f.value;try{if(!ze(d(),f))return!1}catch{return!1}}if(l=n.child,n.subtreeFlags&16384&&l!==null)l.return=n,n=l;else{if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function ai(t,n,l,r){n&=~mu,n&=~ki,t.suspendedLanes|=n,t.pingedLanes&=~n,r&&(t.warmLanes|=n),r=t.expirationTimes;for(var f=n;0<f;){var d=31-Ee(f),y=1<<d;r[d]=-1,f&=~y}l!==0&&rh(t,l,n)}function Oo(){return(At&6)===0?(Ba(0),!1):!0}function vu(){if(ht!==null){if(jt===0)var t=ht.return;else t=ht,jn=Di=null,Lc(t),_s=null,Ta=0,t=ht;for(;t!==null;)Em(t.alternate,t),t=t.return;ht=null}}function Os(t,n){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,mv(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),vu(),kt=t,ht=l=wn(t.current,null),bt=n,jt=0,Be=null,ei=!1,Ts=Js(t,n),du=!1,As=Ze=mu=ki=ni=Yt=0,we=Ra=null,gu=!1,(n&8)!==0&&(n|=n&32);var r=t.entangledLanes;if(r!==0)for(t=t.entanglements,r&=n;0<r;){var f=31-Ee(r),d=1<<f;n|=t[f],r&=~d}return Bn=n,Pl(),l}function Im(t,n){ot=null,k.H=po,n===pa||n===ao?(n=gd(),jt=3):n===hd?(n=gd(),jt=4):jt=n===pm?8:n!==null&&typeof n=="object"&&typeof n.then=="function"?6:1,Be=n,ht===null&&(Yt=1,_o(t,Fe(n,t.current)))}function $m(){var t=k.H;return k.H=po,t===null?po:t}function tg(){var t=k.A;return k.A=Ky,t}function _u(){Yt=4,ei||(bt&4194048)!==bt&&Qe.current!==null||(Ts=!0),(ni&134217727)===0&&(ki&134217727)===0||kt===null||ai(kt,bt,Ze,!1)}function Su(t,n,l){var r=At;At|=2;var f=$m(),d=tg();(kt!==t||bt!==n)&&(Do=null,Os(t,n)),n=!1;var y=Yt;t:do try{if(jt!==0&&ht!==null){var _=ht,A=Be;switch(jt){case 8:vu(),y=6;break t;case 3:case 2:case 9:case 6:Qe.current===null&&(n=!0);var z=jt;if(jt=0,Be=null,Es(t,_,A,z),l&&Ts){y=0;break t}break;default:z=jt,jt=0,Be=null,Es(t,_,A,z)}}Jy(),y=Yt;break}catch(N){Im(t,N)}while(!0);return n&&t.shellSuspendCounter++,jn=Di=null,At=r,k.H=f,k.A=d,ht===null&&(kt=null,bt=0,Pl()),y}function Jy(){for(;ht!==null;)eg(ht)}function Iy(t,n){var l=At;At|=2;var r=$m(),f=tg();kt!==t||bt!==n?(Do=null,jo=cn()+500,Os(t,n)):Ts=Js(t,n);t:do try{if(jt!==0&&ht!==null){n=ht;var d=Be;e:switch(jt){case 1:jt=0,Be=null,Es(t,n,d,1);break;case 2:case 9:if(dd(d)){jt=0,Be=null,ng(n);break}n=function(){jt!==2&&jt!==9||kt!==t||(jt=7),mn(t)},d.then(n,n);break t;case 3:jt=7;break t;case 4:jt=5;break t;case 7:dd(d)?(jt=0,Be=null,ng(n)):(jt=0,Be=null,Es(t,n,d,7));break;case 5:var y=null;switch(ht.tag){case 26:y=ht.memoizedState;case 5:case 27:var _=ht;if(!y||Lg(y)){jt=0,Be=null;var A=_.sibling;if(A!==null)ht=A;else{var z=_.return;z!==null?(ht=z,Eo(z)):ht=null}break e}}jt=0,Be=null,Es(t,n,d,5);break;case 6:jt=0,Be=null,Es(t,n,d,6);break;case 8:vu(),Yt=6;break t;default:throw Error(a(462))}}$y();break}catch(N){Im(t,N)}while(!0);return jn=Di=null,k.H=r,k.A=f,At=l,ht!==null?0:(kt=null,bt=0,Pl(),Yt)}function $y(){for(;ht!==null&&!_x();)eg(ht)}function eg(t){var n=Dm(t.alternate,t,Bn);t.memoizedProps=t.pendingProps,n===null?Eo(t):ht=n}function ng(t){var n=t,l=n.alternate;switch(n.tag){case 15:case 0:n=Sm(l,n,n.pendingProps,n.type,void 0,bt);break;case 11:n=Sm(l,n,n.pendingProps,n.type.render,n.ref,bt);break;case 5:Lc(n);default:Em(l,n),n=ht=id(n,Bn),n=Dm(l,n,Bn)}t.memoizedProps=t.pendingProps,n===null?Eo(t):ht=n}function Es(t,n,l,r){jn=Di=null,Lc(n),_s=null,Ta=0;var f=n.return;try{if(Fy(t,f,n,l,bt)){Yt=1,_o(t,Fe(l,t.current)),ht=null;return}}catch(d){if(f!==null)throw ht=f,d;Yt=1,_o(t,Fe(l,t.current)),ht=null;return}n.flags&32768?(St||r===1?t=!0:Ts||(bt&536870912)!==0?t=!1:(ei=t=!0,(r===2||r===9||r===3||r===6)&&(r=Qe.current,r!==null&&r.tag===13&&(r.flags|=16384))),ig(n,t)):Eo(n)}function Eo(t){var n=t;do{if((n.flags&32768)!==0){ig(n,ei);return}t=n.return;var l=Xy(n.alternate,n,Bn);if(l!==null){ht=l;return}if(n=n.sibling,n!==null){ht=n;return}ht=n=t}while(n!==null);Yt===0&&(Yt=5)}function ig(t,n){do{var l=Gy(t.alternate,t);if(l!==null){l.flags&=32767,ht=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!n&&(t=t.sibling,t!==null)){ht=t;return}ht=t=l}while(t!==null);Yt=6,ht=null}function sg(t,n,l,r,f,d,y,_,A){t.cancelPendingCommit=null;do zo();while(re!==0);if((At&6)!==0)throw Error(a(327));if(n!==null){if(n===t.current)throw Error(a(177));if(d=n.lanes|n.childLanes,d|=hc,zx(t,l,d,y,_,A),t===kt&&(ht=kt=null,bt=0),js=n,si=t,Ds=l,bu=d,xu=f,Km=r,(n.subtreeFlags&10256)!==0||(n.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,iv(Bl,function(){return cg(),null})):(t.callbackNode=null,t.callbackPriority=0),r=(n.flags&13878)!==0,(n.subtreeFlags&13878)!==0||r){r=k.T,k.T=null,f=Q.p,Q.p=2,y=At,At|=4;try{Qy(t,n,l)}finally{At=y,Q.p=f,k.T=r}}re=1,ag(),lg(),og()}}function ag(){if(re===1){re=0;var t=si,n=js,l=(n.flags&13878)!==0;if((n.subtreeFlags&13878)!==0||l){l=k.T,k.T=null;var r=Q.p;Q.p=2;var f=At;At|=4;try{Vm(n,t);var d=ku,y=Zh(t.containerInfo),_=d.focusedElem,A=d.selectionRange;if(y!==_&&_&&_.ownerDocument&&Qh(_.ownerDocument.documentElement,_)){if(A!==null&&oc(_)){var z=A.start,N=A.end;if(N===void 0&&(N=z),"selectionStart"in _)_.selectionStart=z,_.selectionEnd=Math.min(N,_.value.length);else{var U=_.ownerDocument||document,C=U&&U.defaultView||window;if(C.getSelection){var R=C.getSelection(),it=_.textContent.length,$=Math.min(A.start,it),zt=A.end===void 0?$:Math.min(A.end,it);!R.extend&&$>zt&&(y=zt,zt=$,$=y);var D=Gh(_,$),j=Gh(_,zt);if(D&&j&&(R.rangeCount!==1||R.anchorNode!==D.node||R.anchorOffset!==D.offset||R.focusNode!==j.node||R.focusOffset!==j.offset)){var E=U.createRange();E.setStart(D.node,D.offset),R.removeAllRanges(),$>zt?(R.addRange(E),R.extend(j.node,j.offset)):(E.setEnd(j.node,j.offset),R.addRange(E))}}}}for(U=[],R=_;R=R.parentNode;)R.nodeType===1&&U.push({element:R,left:R.scrollLeft,top:R.scrollTop});for(typeof _.focus=="function"&&_.focus(),_=0;_<U.length;_++){var L=U[_];L.element.scrollLeft=L.left,L.element.scrollTop=L.top}}Yo=!!Ru,ku=Ru=null}finally{At=f,Q.p=r,k.T=l}}t.current=n,re=2}}function lg(){if(re===2){re=0;var t=si,n=js,l=(n.flags&8772)!==0;if((n.subtreeFlags&8772)!==0||l){l=k.T,k.T=null;var r=Q.p;Q.p=2;var f=At;At|=4;try{Lm(t,n.alternate,n)}finally{At=f,Q.p=r,k.T=l}}re=3}}function og(){if(re===4||re===3){re=0,Sx();var t=si,n=js,l=Ds,r=Km;(n.subtreeFlags&10256)!==0||(n.flags&10256)!==0?re=5:(re=0,js=si=null,rg(t,t.pendingLanes));var f=t.pendingLanes;if(f===0&&(ii=null),qr(l),n=n.stateNode,Oe&&typeof Oe.onCommitFiberRoot=="function")try{Oe.onCommitFiberRoot(Ps,n,void 0,(n.current.flags&128)===128)}catch{}if(r!==null){n=k.T,f=Q.p,Q.p=2,k.T=null;try{for(var d=t.onRecoverableError,y=0;y<r.length;y++){var _=r[y];d(_.value,{componentStack:_.stack})}}finally{k.T=n,Q.p=f}}(Ds&3)!==0&&zo(),mn(t),f=t.pendingLanes,(l&4194090)!==0&&(f&42)!==0?t===yu?ka++:(ka=0,yu=t):ka=0,Ba(0)}}function rg(t,n){(t.pooledCacheLanes&=n)===0&&(n=t.pooledCache,n!=null&&(t.pooledCache=null,ma(n)))}function zo(t){return ag(),lg(),og(),cg()}function cg(){if(re!==5)return!1;var t=si,n=bu;bu=0;var l=qr(Ds),r=k.T,f=Q.p;try{Q.p=32>l?32:l,k.T=null,l=xu,xu=null;var d=si,y=Ds;if(re=0,js=si=null,Ds=0,(At&6)!==0)throw Error(a(331));var _=At;if(At|=4,Qm(d.current),Ym(d,d.current,y,l),At=_,Ba(0,!1),Oe&&typeof Oe.onPostCommitFiberRoot=="function")try{Oe.onPostCommitFiberRoot(Ps,d)}catch{}return!0}finally{Q.p=f,k.T=r,rg(t,n)}}function ug(t,n,l){n=Fe(l,n),n=Jc(t.stateNode,n,2),t=Kn(t,n,2),t!==null&&(Is(t,2),mn(t))}function Rt(t,n,l){if(t.tag===3)ug(t,t,l);else for(;n!==null;){if(n.tag===3){ug(n,t,l);break}else if(n.tag===1){var r=n.stateNode;if(typeof n.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(ii===null||!ii.has(r))){t=Fe(l,t),l=mm(2),r=Kn(n,l,2),r!==null&&(gm(l,r,n,t),Is(r,2),mn(r));break}}n=n.return}}function Mu(t,n,l){var r=t.pingCache;if(r===null){r=t.pingCache=new Wy;var f=new Set;r.set(n,f)}else f=r.get(n),f===void 0&&(f=new Set,r.set(n,f));f.has(l)||(du=!0,f.add(l),t=tv.bind(null,t,n,l),n.then(t,t))}function tv(t,n,l){var r=t.pingCache;r!==null&&r.delete(n),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,kt===t&&(bt&l)===l&&(Yt===4||Yt===3&&(bt&62914560)===bt&&300>cn()-pu?(At&2)===0&&Os(t,0):mu|=l,As===bt&&(As=0)),mn(t)}function fg(t,n){n===0&&(n=oh()),t=fs(t,n),t!==null&&(Is(t,n),mn(t))}function ev(t){var n=t.memoizedState,l=0;n!==null&&(l=n.retryLane),fg(t,l)}function nv(t,n){var l=0;switch(t.tag){case 13:var r=t.stateNode,f=t.memoizedState;f!==null&&(l=f.retryLane);break;case 19:r=t.stateNode;break;case 22:r=t.stateNode._retryCache;break;default:throw Error(a(314))}r!==null&&r.delete(n),fg(t,l)}function iv(t,n){return Nr(t,n)}var Co=null,zs=null,wu=!1,Ro=!1,Tu=!1,Bi=0;function mn(t){t!==zs&&t.next===null&&(zs===null?Co=zs=t:zs=zs.next=t),Ro=!0,wu||(wu=!0,av())}function Ba(t,n){if(!Tu&&Ro){Tu=!0;do for(var l=!1,r=Co;r!==null;){if(t!==0){var f=r.pendingLanes;if(f===0)var d=0;else{var y=r.suspendedLanes,_=r.pingedLanes;d=(1<<31-Ee(42|t)+1)-1,d&=f&~(y&~_),d=d&201326741?d&201326741|1:d?d|2:0}d!==0&&(l=!0,gg(r,d))}else d=bt,d=Hl(r,r===kt?d:0,r.cancelPendingCommit!==null||r.timeoutHandle!==-1),(d&3)===0||Js(r,d)||(l=!0,gg(r,d));r=r.next}while(l);Tu=!1}}function sv(){hg()}function hg(){Ro=wu=!1;var t=0;Bi!==0&&(dv()&&(t=Bi),Bi=0);for(var n=cn(),l=null,r=Co;r!==null;){var f=r.next,d=dg(r,n);d===0?(r.next=null,l===null?Co=f:l.next=f,f===null&&(zs=l)):(l=r,(t!==0||(d&3)!==0)&&(Ro=!0)),r=f}Ba(t)}function dg(t,n){for(var l=t.suspendedLanes,r=t.pingedLanes,f=t.expirationTimes,d=t.pendingLanes&-62914561;0<d;){var y=31-Ee(d),_=1<<y,A=f[y];A===-1?((_&l)===0||(_&r)!==0)&&(f[y]=Ex(_,n)):A<=n&&(t.expiredLanes|=_),d&=~_}if(n=kt,l=bt,l=Hl(t,t===n?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),r=t.callbackNode,l===0||t===n&&(jt===2||jt===9)||t.cancelPendingCommit!==null)return r!==null&&r!==null&&Lr(r),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||Js(t,l)){if(n=l&-l,n===t.callbackPriority)return n;switch(r!==null&&Lr(r),qr(l)){case 2:case 8:l=sh;break;case 32:l=Bl;break;case 268435456:l=ah;break;default:l=Bl}return r=mg.bind(null,t),l=Nr(l,r),t.callbackPriority=n,t.callbackNode=l,n}return r!==null&&r!==null&&Lr(r),t.callbackPriority=2,t.callbackNode=null,2}function mg(t,n){if(re!==0&&re!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(zo()&&t.callbackNode!==l)return null;var r=bt;return r=Hl(t,t===kt?r:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),r===0?null:(Pm(t,r,n),dg(t,cn()),t.callbackNode!=null&&t.callbackNode===l?mg.bind(null,t):null)}function gg(t,n){if(zo())return null;Pm(t,n,!0)}function av(){gv(function(){(At&6)!==0?Nr(ih,sv):hg()})}function Au(){return Bi===0&&(Bi=lh()),Bi}function pg(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Yl(""+t)}function bg(t,n){var l=n.ownerDocument.createElement("input");return l.name=n.name,l.value=n.value,t.id&&l.setAttribute("form",t.id),n.parentNode.insertBefore(l,n),t=new FormData(t),l.parentNode.removeChild(l),t}function lv(t,n,l,r,f){if(n==="submit"&&l&&l.stateNode===f){var d=pg((f[ve]||null).action),y=r.submitter;y&&(n=(n=y[ve]||null)?pg(n.formAction):y.getAttribute("formAction"),n!==null&&(d=n,y=null));var _=new Zl("action","action",null,r,f);t.push({event:_,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(Bi!==0){var A=y?bg(f,y):new FormData(f);Qc(l,{pending:!0,data:A,method:f.method,action:d},null,A)}}else typeof d=="function"&&(_.preventDefault(),A=y?bg(f,y):new FormData(f),Qc(l,{pending:!0,data:A,method:f.method,action:d},d,A))},currentTarget:f}]})}}for(var ju=0;ju<fc.length;ju++){var Du=fc[ju],ov=Du.toLowerCase(),rv=Du[0].toUpperCase()+Du.slice(1);nn(ov,"on"+rv)}nn(Ph,"onAnimationEnd"),nn(Jh,"onAnimationIteration"),nn(Ih,"onAnimationStart"),nn("dblclick","onDoubleClick"),nn("focusin","onFocus"),nn("focusout","onBlur"),nn(Ty,"onTransitionRun"),nn(Ay,"onTransitionStart"),nn(jy,"onTransitionCancel"),nn($h,"onTransitionEnd"),es("onMouseEnter",["mouseout","mouseover"]),es("onMouseLeave",["mouseout","mouseover"]),es("onPointerEnter",["pointerout","pointerover"]),es("onPointerLeave",["pointerout","pointerover"]),yi("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),yi("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),yi("onBeforeInput",["compositionend","keypress","textInput","paste"]),yi("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),yi("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),yi("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Na="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),cv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Na));function xg(t,n){n=(n&4)!==0;for(var l=0;l<t.length;l++){var r=t[l],f=r.event;r=r.listeners;t:{var d=void 0;if(n)for(var y=r.length-1;0<=y;y--){var _=r[y],A=_.instance,z=_.currentTarget;if(_=_.listener,A!==d&&f.isPropagationStopped())break t;d=_,f.currentTarget=z;try{d(f)}catch(N){vo(N)}f.currentTarget=null,d=A}else for(y=0;y<r.length;y++){if(_=r[y],A=_.instance,z=_.currentTarget,_=_.listener,A!==d&&f.isPropagationStopped())break t;d=_,f.currentTarget=z;try{d(f)}catch(N){vo(N)}f.currentTarget=null,d=A}}}}function dt(t,n){var l=n[Vr];l===void 0&&(l=n[Vr]=new Set);var r=t+"__bubble";l.has(r)||(yg(n,t,2,!1),l.add(r))}function Ou(t,n,l){var r=0;n&&(r|=4),yg(l,t,r,n)}var ko="_reactListening"+Math.random().toString(36).slice(2);function Eu(t){if(!t[ko]){t[ko]=!0,hh.forEach(function(l){l!=="selectionchange"&&(cv.has(l)||Ou(l,!1,t),Ou(l,!0,t))});var n=t.nodeType===9?t:t.ownerDocument;n===null||n[ko]||(n[ko]=!0,Ou("selectionchange",!1,n))}}function yg(t,n,l,r){switch(Yg(n)){case 2:var f=Nv;break;case 8:f=Lv;break;default:f=Xu}l=f.bind(null,n,l,t),f=void 0,!Ir||n!=="touchstart"&&n!=="touchmove"&&n!=="wheel"||(f=!0),r?f!==void 0?t.addEventListener(n,l,{capture:!0,passive:f}):t.addEventListener(n,l,!0):f!==void 0?t.addEventListener(n,l,{passive:f}):t.addEventListener(n,l,!1)}function zu(t,n,l,r,f){var d=r;if((n&1)===0&&(n&2)===0&&r!==null)t:for(;;){if(r===null)return;var y=r.tag;if(y===3||y===4){var _=r.stateNode.containerInfo;if(_===f)break;if(y===4)for(y=r.return;y!==null;){var A=y.tag;if((A===3||A===4)&&y.stateNode.containerInfo===f)return;y=y.return}for(;_!==null;){if(y=Ii(_),y===null)return;if(A=y.tag,A===5||A===6||A===26||A===27){r=d=y;continue t}_=_.parentNode}}r=r.return}Ah(function(){var z=d,N=Pr(l),U=[];t:{var C=td.get(t);if(C!==void 0){var R=Zl,it=t;switch(t){case"keypress":if(Gl(l)===0)break t;case"keydown":case"keyup":R=iy;break;case"focusin":it="focus",R=nc;break;case"focusout":it="blur",R=nc;break;case"beforeblur":case"afterblur":R=nc;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":R=Oh;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":R=Gx;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":R=ly;break;case Ph:case Jh:case Ih:R=Kx;break;case $h:R=ry;break;case"scroll":case"scrollend":R=Yx;break;case"wheel":R=uy;break;case"copy":case"cut":case"paste":R=Px;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":R=zh;break;case"toggle":case"beforetoggle":R=hy}var $=(n&4)!==0,zt=!$&&(t==="scroll"||t==="scrollend"),D=$?C!==null?C+"Capture":null:C;$=[];for(var j=z,E;j!==null;){var L=j;if(E=L.stateNode,L=L.tag,L!==5&&L!==26&&L!==27||E===null||D===null||(L=ea(j,D),L!=null&&$.push(La(j,L,E))),zt)break;j=j.return}0<$.length&&(C=new R(C,it,null,l,N),U.push({event:C,listeners:$}))}}if((n&7)===0){t:{if(C=t==="mouseover"||t==="pointerover",R=t==="mouseout"||t==="pointerout",C&&l!==Wr&&(it=l.relatedTarget||l.fromElement)&&(Ii(it)||it[Ji]))break t;if((R||C)&&(C=N.window===N?N:(C=N.ownerDocument)?C.defaultView||C.parentWindow:window,R?(it=l.relatedTarget||l.toElement,R=z,it=it?Ii(it):null,it!==null&&(zt=c(it),$=it.tag,it!==zt||$!==5&&$!==27&&$!==6)&&(it=null)):(R=null,it=z),R!==it)){if($=Oh,L="onMouseLeave",D="onMouseEnter",j="mouse",(t==="pointerout"||t==="pointerover")&&($=zh,L="onPointerLeave",D="onPointerEnter",j="pointer"),zt=R==null?C:ta(R),E=it==null?C:ta(it),C=new $(L,j+"leave",R,l,N),C.target=zt,C.relatedTarget=E,L=null,Ii(N)===z&&($=new $(D,j+"enter",it,l,N),$.target=E,$.relatedTarget=zt,L=$),zt=L,R&&it)e:{for($=R,D=it,j=0,E=$;E;E=Cs(E))j++;for(E=0,L=D;L;L=Cs(L))E++;for(;0<j-E;)$=Cs($),j--;for(;0<E-j;)D=Cs(D),E--;for(;j--;){if($===D||D!==null&&$===D.alternate)break e;$=Cs($),D=Cs(D)}$=null}else $=null;R!==null&&vg(U,C,R,$,!1),it!==null&&zt!==null&&vg(U,zt,it,$,!0)}}t:{if(C=z?ta(z):window,R=C.nodeName&&C.nodeName.toLowerCase(),R==="select"||R==="input"&&C.type==="file")var W=Uh;else if(Lh(C))if(qh)W=Sy;else{W=vy;var ut=yy}else R=C.nodeName,!R||R.toLowerCase()!=="input"||C.type!=="checkbox"&&C.type!=="radio"?z&&Kr(z.elementType)&&(W=Uh):W=_y;if(W&&(W=W(t,z))){Hh(U,W,l,N);break t}ut&&ut(t,C,z),t==="focusout"&&z&&C.type==="number"&&z.memoizedProps.value!=null&&Zr(C,"number",C.value)}switch(ut=z?ta(z):window,t){case"focusin":(Lh(ut)||ut.contentEditable==="true")&&(rs=ut,rc=z,ca=null);break;case"focusout":ca=rc=rs=null;break;case"mousedown":cc=!0;break;case"contextmenu":case"mouseup":case"dragend":cc=!1,Kh(U,l,N);break;case"selectionchange":if(wy)break;case"keydown":case"keyup":Kh(U,l,N)}var J;if(sc)t:{switch(t){case"compositionstart":var tt="onCompositionStart";break t;case"compositionend":tt="onCompositionEnd";break t;case"compositionupdate":tt="onCompositionUpdate";break t}tt=void 0}else os?Bh(t,l)&&(tt="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&(tt="onCompositionStart");tt&&(Ch&&l.locale!=="ko"&&(os||tt!=="onCompositionStart"?tt==="onCompositionEnd"&&os&&(J=jh()):(Xn=N,$r="value"in Xn?Xn.value:Xn.textContent,os=!0)),ut=Bo(z,tt),0<ut.length&&(tt=new Eh(tt,t,null,l,N),U.push({event:tt,listeners:ut}),J?tt.data=J:(J=Nh(l),J!==null&&(tt.data=J)))),(J=my?gy(t,l):py(t,l))&&(tt=Bo(z,"onBeforeInput"),0<tt.length&&(ut=new Eh("onBeforeInput","beforeinput",null,l,N),U.push({event:ut,listeners:tt}),ut.data=J)),lv(U,t,z,l,N)}xg(U,n)})}function La(t,n,l){return{instance:t,listener:n,currentTarget:l}}function Bo(t,n){for(var l=n+"Capture",r=[];t!==null;){var f=t,d=f.stateNode;if(f=f.tag,f!==5&&f!==26&&f!==27||d===null||(f=ea(t,l),f!=null&&r.unshift(La(t,f,d)),f=ea(t,n),f!=null&&r.push(La(t,f,d))),t.tag===3)return r;t=t.return}return[]}function Cs(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function vg(t,n,l,r,f){for(var d=n._reactName,y=[];l!==null&&l!==r;){var _=l,A=_.alternate,z=_.stateNode;if(_=_.tag,A!==null&&A===r)break;_!==5&&_!==26&&_!==27||z===null||(A=z,f?(z=ea(l,d),z!=null&&y.unshift(La(l,z,A))):f||(z=ea(l,d),z!=null&&y.push(La(l,z,A)))),l=l.return}y.length!==0&&t.push({event:n,listeners:y})}var uv=/\r\n?/g,fv=/\u0000|\uFFFD/g;function _g(t){return(typeof t=="string"?t:""+t).replace(uv,`
`).replace(fv,"")}function Sg(t,n){return n=_g(n),_g(t)===n}function No(){}function Et(t,n,l,r,f,d){switch(l){case"children":typeof r=="string"?n==="body"||n==="textarea"&&r===""||ss(t,r):(typeof r=="number"||typeof r=="bigint")&&n!=="body"&&ss(t,""+r);break;case"className":ql(t,"class",r);break;case"tabIndex":ql(t,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":ql(t,l,r);break;case"style":wh(t,r,d);break;case"data":if(n!=="object"){ql(t,"data",r);break}case"src":case"href":if(r===""&&(n!=="a"||l!=="href")){t.removeAttribute(l);break}if(r==null||typeof r=="function"||typeof r=="symbol"||typeof r=="boolean"){t.removeAttribute(l);break}r=Yl(""+r),t.setAttribute(l,r);break;case"action":case"formAction":if(typeof r=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof d=="function"&&(l==="formAction"?(n!=="input"&&Et(t,n,"name",f.name,f,null),Et(t,n,"formEncType",f.formEncType,f,null),Et(t,n,"formMethod",f.formMethod,f,null),Et(t,n,"formTarget",f.formTarget,f,null)):(Et(t,n,"encType",f.encType,f,null),Et(t,n,"method",f.method,f,null),Et(t,n,"target",f.target,f,null)));if(r==null||typeof r=="symbol"||typeof r=="boolean"){t.removeAttribute(l);break}r=Yl(""+r),t.setAttribute(l,r);break;case"onClick":r!=null&&(t.onclick=No);break;case"onScroll":r!=null&&dt("scroll",t);break;case"onScrollEnd":r!=null&&dt("scrollend",t);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(a(61));if(l=r.__html,l!=null){if(f.children!=null)throw Error(a(60));t.innerHTML=l}}break;case"multiple":t.multiple=r&&typeof r!="function"&&typeof r!="symbol";break;case"muted":t.muted=r&&typeof r!="function"&&typeof r!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(r==null||typeof r=="function"||typeof r=="boolean"||typeof r=="symbol"){t.removeAttribute("xlink:href");break}l=Yl(""+r),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":r!=null&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(l,""+r):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":r===!0?t.setAttribute(l,""):r!==!1&&r!=null&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(l,r):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":r!=null&&typeof r!="function"&&typeof r!="symbol"&&!isNaN(r)&&1<=r?t.setAttribute(l,r):t.removeAttribute(l);break;case"rowSpan":case"start":r==null||typeof r=="function"||typeof r=="symbol"||isNaN(r)?t.removeAttribute(l):t.setAttribute(l,r);break;case"popover":dt("beforetoggle",t),dt("toggle",t),Ul(t,"popover",r);break;case"xlinkActuate":Sn(t,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":Sn(t,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":Sn(t,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":Sn(t,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":Sn(t,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":Sn(t,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":Sn(t,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":Sn(t,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":Sn(t,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":Ul(t,"is",r);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=Vx.get(l)||l,Ul(t,l,r))}}function Cu(t,n,l,r,f,d){switch(l){case"style":wh(t,r,d);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(a(61));if(l=r.__html,l!=null){if(f.children!=null)throw Error(a(60));t.innerHTML=l}}break;case"children":typeof r=="string"?ss(t,r):(typeof r=="number"||typeof r=="bigint")&&ss(t,""+r);break;case"onScroll":r!=null&&dt("scroll",t);break;case"onScrollEnd":r!=null&&dt("scrollend",t);break;case"onClick":r!=null&&(t.onclick=No);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!dh.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(f=l.endsWith("Capture"),n=l.slice(2,f?l.length-7:void 0),d=t[ve]||null,d=d!=null?d[l]:null,typeof d=="function"&&t.removeEventListener(n,d,f),typeof r=="function")){typeof d!="function"&&d!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(n,r,f);break t}l in t?t[l]=r:r===!0?t.setAttribute(l,""):Ul(t,l,r)}}}function ce(t,n,l){switch(n){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":dt("error",t),dt("load",t);var r=!1,f=!1,d;for(d in l)if(l.hasOwnProperty(d)){var y=l[d];if(y!=null)switch(d){case"src":r=!0;break;case"srcSet":f=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(a(137,n));default:Et(t,n,d,y,l,null)}}f&&Et(t,n,"srcSet",l.srcSet,l,null),r&&Et(t,n,"src",l.src,l,null);return;case"input":dt("invalid",t);var _=d=y=f=null,A=null,z=null;for(r in l)if(l.hasOwnProperty(r)){var N=l[r];if(N!=null)switch(r){case"name":f=N;break;case"type":y=N;break;case"checked":A=N;break;case"defaultChecked":z=N;break;case"value":d=N;break;case"defaultValue":_=N;break;case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(a(137,n));break;default:Et(t,n,r,N,l,null)}}vh(t,d,_,A,z,y,f,!1),Vl(t);return;case"select":dt("invalid",t),r=y=d=null;for(f in l)if(l.hasOwnProperty(f)&&(_=l[f],_!=null))switch(f){case"value":d=_;break;case"defaultValue":y=_;break;case"multiple":r=_;default:Et(t,n,f,_,l,null)}n=d,l=y,t.multiple=!!r,n!=null?is(t,!!r,n,!1):l!=null&&is(t,!!r,l,!0);return;case"textarea":dt("invalid",t),d=f=r=null;for(y in l)if(l.hasOwnProperty(y)&&(_=l[y],_!=null))switch(y){case"value":r=_;break;case"defaultValue":f=_;break;case"children":d=_;break;case"dangerouslySetInnerHTML":if(_!=null)throw Error(a(91));break;default:Et(t,n,y,_,l,null)}Sh(t,r,f,d),Vl(t);return;case"option":for(A in l)if(l.hasOwnProperty(A)&&(r=l[A],r!=null))switch(A){case"selected":t.selected=r&&typeof r!="function"&&typeof r!="symbol";break;default:Et(t,n,A,r,l,null)}return;case"dialog":dt("beforetoggle",t),dt("toggle",t),dt("cancel",t),dt("close",t);break;case"iframe":case"object":dt("load",t);break;case"video":case"audio":for(r=0;r<Na.length;r++)dt(Na[r],t);break;case"image":dt("error",t),dt("load",t);break;case"details":dt("toggle",t);break;case"embed":case"source":case"link":dt("error",t),dt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(z in l)if(l.hasOwnProperty(z)&&(r=l[z],r!=null))switch(z){case"children":case"dangerouslySetInnerHTML":throw Error(a(137,n));default:Et(t,n,z,r,l,null)}return;default:if(Kr(n)){for(N in l)l.hasOwnProperty(N)&&(r=l[N],r!==void 0&&Cu(t,n,N,r,l,void 0));return}}for(_ in l)l.hasOwnProperty(_)&&(r=l[_],r!=null&&Et(t,n,_,r,l,null))}function hv(t,n,l,r){switch(n){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var f=null,d=null,y=null,_=null,A=null,z=null,N=null;for(R in l){var U=l[R];if(l.hasOwnProperty(R)&&U!=null)switch(R){case"checked":break;case"value":break;case"defaultValue":A=U;default:r.hasOwnProperty(R)||Et(t,n,R,null,r,U)}}for(var C in r){var R=r[C];if(U=l[C],r.hasOwnProperty(C)&&(R!=null||U!=null))switch(C){case"type":d=R;break;case"name":f=R;break;case"checked":z=R;break;case"defaultChecked":N=R;break;case"value":y=R;break;case"defaultValue":_=R;break;case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(a(137,n));break;default:R!==U&&Et(t,n,C,R,r,U)}}Qr(t,y,_,A,z,N,d,f);return;case"select":R=y=_=C=null;for(d in l)if(A=l[d],l.hasOwnProperty(d)&&A!=null)switch(d){case"value":break;case"multiple":R=A;default:r.hasOwnProperty(d)||Et(t,n,d,null,r,A)}for(f in r)if(d=r[f],A=l[f],r.hasOwnProperty(f)&&(d!=null||A!=null))switch(f){case"value":C=d;break;case"defaultValue":_=d;break;case"multiple":y=d;default:d!==A&&Et(t,n,f,d,r,A)}n=_,l=y,r=R,C!=null?is(t,!!l,C,!1):!!r!=!!l&&(n!=null?is(t,!!l,n,!0):is(t,!!l,l?[]:"",!1));return;case"textarea":R=C=null;for(_ in l)if(f=l[_],l.hasOwnProperty(_)&&f!=null&&!r.hasOwnProperty(_))switch(_){case"value":break;case"children":break;default:Et(t,n,_,null,r,f)}for(y in r)if(f=r[y],d=l[y],r.hasOwnProperty(y)&&(f!=null||d!=null))switch(y){case"value":C=f;break;case"defaultValue":R=f;break;case"children":break;case"dangerouslySetInnerHTML":if(f!=null)throw Error(a(91));break;default:f!==d&&Et(t,n,y,f,r,d)}_h(t,C,R);return;case"option":for(var it in l)if(C=l[it],l.hasOwnProperty(it)&&C!=null&&!r.hasOwnProperty(it))switch(it){case"selected":t.selected=!1;break;default:Et(t,n,it,null,r,C)}for(A in r)if(C=r[A],R=l[A],r.hasOwnProperty(A)&&C!==R&&(C!=null||R!=null))switch(A){case"selected":t.selected=C&&typeof C!="function"&&typeof C!="symbol";break;default:Et(t,n,A,C,r,R)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var $ in l)C=l[$],l.hasOwnProperty($)&&C!=null&&!r.hasOwnProperty($)&&Et(t,n,$,null,r,C);for(z in r)if(C=r[z],R=l[z],r.hasOwnProperty(z)&&C!==R&&(C!=null||R!=null))switch(z){case"children":case"dangerouslySetInnerHTML":if(C!=null)throw Error(a(137,n));break;default:Et(t,n,z,C,r,R)}return;default:if(Kr(n)){for(var zt in l)C=l[zt],l.hasOwnProperty(zt)&&C!==void 0&&!r.hasOwnProperty(zt)&&Cu(t,n,zt,void 0,r,C);for(N in r)C=r[N],R=l[N],!r.hasOwnProperty(N)||C===R||C===void 0&&R===void 0||Cu(t,n,N,C,r,R);return}}for(var D in l)C=l[D],l.hasOwnProperty(D)&&C!=null&&!r.hasOwnProperty(D)&&Et(t,n,D,null,r,C);for(U in r)C=r[U],R=l[U],!r.hasOwnProperty(U)||C===R||C==null&&R==null||Et(t,n,U,C,r,R)}var Ru=null,ku=null;function Lo(t){return t.nodeType===9?t:t.ownerDocument}function Mg(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function wg(t,n){if(t===0)switch(n){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&n==="foreignObject"?0:t}function Bu(t,n){return t==="textarea"||t==="noscript"||typeof n.children=="string"||typeof n.children=="number"||typeof n.children=="bigint"||typeof n.dangerouslySetInnerHTML=="object"&&n.dangerouslySetInnerHTML!==null&&n.dangerouslySetInnerHTML.__html!=null}var Nu=null;function dv(){var t=window.event;return t&&t.type==="popstate"?t===Nu?!1:(Nu=t,!0):(Nu=null,!1)}var Tg=typeof setTimeout=="function"?setTimeout:void 0,mv=typeof clearTimeout=="function"?clearTimeout:void 0,Ag=typeof Promise=="function"?Promise:void 0,gv=typeof queueMicrotask=="function"?queueMicrotask:typeof Ag<"u"?function(t){return Ag.resolve(null).then(t).catch(pv)}:Tg;function pv(t){setTimeout(function(){throw t})}function li(t){return t==="head"}function jg(t,n){var l=n,r=0,f=0;do{var d=l.nextSibling;if(t.removeChild(l),d&&d.nodeType===8)if(l=d.data,l==="/$"){if(0<r&&8>r){l=r;var y=t.ownerDocument;if(l&1&&Ha(y.documentElement),l&2&&Ha(y.body),l&4)for(l=y.head,Ha(l),y=l.firstChild;y;){var _=y.nextSibling,A=y.nodeName;y[$s]||A==="SCRIPT"||A==="STYLE"||A==="LINK"&&y.rel.toLowerCase()==="stylesheet"||l.removeChild(y),y=_}}if(f===0){t.removeChild(d),Qa(n);return}f--}else l==="$"||l==="$?"||l==="$!"?f++:r=l.charCodeAt(0)-48;else r=0;l=d}while(l);Qa(n)}function Lu(t){var n=t.firstChild;for(n&&n.nodeType===10&&(n=n.nextSibling);n;){var l=n;switch(n=n.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":Lu(l),Fr(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function bv(t,n,l,r){for(;t.nodeType===1;){var f=l;if(t.nodeName.toLowerCase()!==n.toLowerCase()){if(!r&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(r){if(!t[$s])switch(n){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(d=t.getAttribute("rel"),d==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(d!==f.rel||t.getAttribute("href")!==(f.href==null||f.href===""?null:f.href)||t.getAttribute("crossorigin")!==(f.crossOrigin==null?null:f.crossOrigin)||t.getAttribute("title")!==(f.title==null?null:f.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(d=t.getAttribute("src"),(d!==(f.src==null?null:f.src)||t.getAttribute("type")!==(f.type==null?null:f.type)||t.getAttribute("crossorigin")!==(f.crossOrigin==null?null:f.crossOrigin))&&d&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(n==="input"&&t.type==="hidden"){var d=f.name==null?null:""+f.name;if(f.type==="hidden"&&t.getAttribute("name")===d)return t}else return t;if(t=an(t.nextSibling),t===null)break}return null}function xv(t,n,l){if(n==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=an(t.nextSibling),t===null))return null;return t}function Hu(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function yv(t,n){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")n();else{var r=function(){n(),l.removeEventListener("DOMContentLoaded",r)};l.addEventListener("DOMContentLoaded",r),t._reactRetry=r}}function an(t){for(;t!=null;t=t.nextSibling){var n=t.nodeType;if(n===1||n===3)break;if(n===8){if(n=t.data,n==="$"||n==="$!"||n==="$?"||n==="F!"||n==="F")break;if(n==="/$")return null}}return t}var Uu=null;function Dg(t){t=t.previousSibling;for(var n=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(n===0)return t;n--}else l==="/$"&&n++}t=t.previousSibling}return null}function Og(t,n,l){switch(n=Lo(l),t){case"html":if(t=n.documentElement,!t)throw Error(a(452));return t;case"head":if(t=n.head,!t)throw Error(a(453));return t;case"body":if(t=n.body,!t)throw Error(a(454));return t;default:throw Error(a(451))}}function Ha(t){for(var n=t.attributes;n.length;)t.removeAttributeNode(n[0]);Fr(t)}var Ke=new Map,Eg=new Set;function Ho(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Nn=Q.d;Q.d={f:vv,r:_v,D:Sv,C:Mv,L:wv,m:Tv,X:jv,S:Av,M:Dv};function vv(){var t=Nn.f(),n=Oo();return t||n}function _v(t){var n=$i(t);n!==null&&n.tag===5&&n.type==="form"?Pd(n):Nn.r(t)}var Rs=typeof document>"u"?null:document;function zg(t,n,l){var r=Rs;if(r&&typeof n=="string"&&n){var f=Ve(n);f='link[rel="'+t+'"][href="'+f+'"]',typeof l=="string"&&(f+='[crossorigin="'+l+'"]'),Eg.has(f)||(Eg.add(f),t={rel:t,crossOrigin:l,href:n},r.querySelector(f)===null&&(n=r.createElement("link"),ce(n,"link",t),ee(n),r.head.appendChild(n)))}}function Sv(t){Nn.D(t),zg("dns-prefetch",t,null)}function Mv(t,n){Nn.C(t,n),zg("preconnect",t,n)}function wv(t,n,l){Nn.L(t,n,l);var r=Rs;if(r&&t&&n){var f='link[rel="preload"][as="'+Ve(n)+'"]';n==="image"&&l&&l.imageSrcSet?(f+='[imagesrcset="'+Ve(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(f+='[imagesizes="'+Ve(l.imageSizes)+'"]')):f+='[href="'+Ve(t)+'"]';var d=f;switch(n){case"style":d=ks(t);break;case"script":d=Bs(t)}Ke.has(d)||(t=p({rel:"preload",href:n==="image"&&l&&l.imageSrcSet?void 0:t,as:n},l),Ke.set(d,t),r.querySelector(f)!==null||n==="style"&&r.querySelector(Ua(d))||n==="script"&&r.querySelector(qa(d))||(n=r.createElement("link"),ce(n,"link",t),ee(n),r.head.appendChild(n)))}}function Tv(t,n){Nn.m(t,n);var l=Rs;if(l&&t){var r=n&&typeof n.as=="string"?n.as:"script",f='link[rel="modulepreload"][as="'+Ve(r)+'"][href="'+Ve(t)+'"]',d=f;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":d=Bs(t)}if(!Ke.has(d)&&(t=p({rel:"modulepreload",href:t},n),Ke.set(d,t),l.querySelector(f)===null)){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(qa(d)))return}r=l.createElement("link"),ce(r,"link",t),ee(r),l.head.appendChild(r)}}}function Av(t,n,l){Nn.S(t,n,l);var r=Rs;if(r&&t){var f=ts(r).hoistableStyles,d=ks(t);n=n||"default";var y=f.get(d);if(!y){var _={loading:0,preload:null};if(y=r.querySelector(Ua(d)))_.loading=5;else{t=p({rel:"stylesheet",href:t,"data-precedence":n},l),(l=Ke.get(d))&&qu(t,l);var A=y=r.createElement("link");ee(A),ce(A,"link",t),A._p=new Promise(function(z,N){A.onload=z,A.onerror=N}),A.addEventListener("load",function(){_.loading|=1}),A.addEventListener("error",function(){_.loading|=2}),_.loading|=4,Uo(y,n,r)}y={type:"stylesheet",instance:y,count:1,state:_},f.set(d,y)}}}function jv(t,n){Nn.X(t,n);var l=Rs;if(l&&t){var r=ts(l).hoistableScripts,f=Bs(t),d=r.get(f);d||(d=l.querySelector(qa(f)),d||(t=p({src:t,async:!0},n),(n=Ke.get(f))&&Vu(t,n),d=l.createElement("script"),ee(d),ce(d,"link",t),l.head.appendChild(d)),d={type:"script",instance:d,count:1,state:null},r.set(f,d))}}function Dv(t,n){Nn.M(t,n);var l=Rs;if(l&&t){var r=ts(l).hoistableScripts,f=Bs(t),d=r.get(f);d||(d=l.querySelector(qa(f)),d||(t=p({src:t,async:!0,type:"module"},n),(n=Ke.get(f))&&Vu(t,n),d=l.createElement("script"),ee(d),ce(d,"link",t),l.head.appendChild(d)),d={type:"script",instance:d,count:1,state:null},r.set(f,d))}}function Cg(t,n,l,r){var f=(f=ue.current)?Ho(f):null;if(!f)throw Error(a(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(n=ks(l.href),l=ts(f).hoistableStyles,r=l.get(n),r||(r={type:"style",instance:null,count:0,state:null},l.set(n,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=ks(l.href);var d=ts(f).hoistableStyles,y=d.get(t);if(y||(f=f.ownerDocument||f,y={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},d.set(t,y),(d=f.querySelector(Ua(t)))&&!d._p&&(y.instance=d,y.state.loading=5),Ke.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},Ke.set(t,l),d||Ov(f,t,l,y.state))),n&&r===null)throw Error(a(528,""));return y}if(n&&r!==null)throw Error(a(529,""));return null;case"script":return n=l.async,l=l.src,typeof l=="string"&&n&&typeof n!="function"&&typeof n!="symbol"?(n=Bs(l),l=ts(f).hoistableScripts,r=l.get(n),r||(r={type:"script",instance:null,count:0,state:null},l.set(n,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(a(444,t))}}function ks(t){return'href="'+Ve(t)+'"'}function Ua(t){return'link[rel="stylesheet"]['+t+"]"}function Rg(t){return p({},t,{"data-precedence":t.precedence,precedence:null})}function Ov(t,n,l,r){t.querySelector('link[rel="preload"][as="style"]['+n+"]")?r.loading=1:(n=t.createElement("link"),r.preload=n,n.addEventListener("load",function(){return r.loading|=1}),n.addEventListener("error",function(){return r.loading|=2}),ce(n,"link",l),ee(n),t.head.appendChild(n))}function Bs(t){return'[src="'+Ve(t)+'"]'}function qa(t){return"script[async]"+t}function kg(t,n,l){if(n.count++,n.instance===null)switch(n.type){case"style":var r=t.querySelector('style[data-href~="'+Ve(l.href)+'"]');if(r)return n.instance=r,ee(r),r;var f=p({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return r=(t.ownerDocument||t).createElement("style"),ee(r),ce(r,"style",f),Uo(r,l.precedence,t),n.instance=r;case"stylesheet":f=ks(l.href);var d=t.querySelector(Ua(f));if(d)return n.state.loading|=4,n.instance=d,ee(d),d;r=Rg(l),(f=Ke.get(f))&&qu(r,f),d=(t.ownerDocument||t).createElement("link"),ee(d);var y=d;return y._p=new Promise(function(_,A){y.onload=_,y.onerror=A}),ce(d,"link",r),n.state.loading|=4,Uo(d,l.precedence,t),n.instance=d;case"script":return d=Bs(l.src),(f=t.querySelector(qa(d)))?(n.instance=f,ee(f),f):(r=l,(f=Ke.get(d))&&(r=p({},l),Vu(r,f)),t=t.ownerDocument||t,f=t.createElement("script"),ee(f),ce(f,"link",r),t.head.appendChild(f),n.instance=f);case"void":return null;default:throw Error(a(443,n.type))}else n.type==="stylesheet"&&(n.state.loading&4)===0&&(r=n.instance,n.state.loading|=4,Uo(r,l.precedence,t));return n.instance}function Uo(t,n,l){for(var r=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),f=r.length?r[r.length-1]:null,d=f,y=0;y<r.length;y++){var _=r[y];if(_.dataset.precedence===n)d=_;else if(d!==f)break}d?d.parentNode.insertBefore(t,d.nextSibling):(n=l.nodeType===9?l.head:l,n.insertBefore(t,n.firstChild))}function qu(t,n){t.crossOrigin==null&&(t.crossOrigin=n.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=n.referrerPolicy),t.title==null&&(t.title=n.title)}function Vu(t,n){t.crossOrigin==null&&(t.crossOrigin=n.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=n.referrerPolicy),t.integrity==null&&(t.integrity=n.integrity)}var qo=null;function Bg(t,n,l){if(qo===null){var r=new Map,f=qo=new Map;f.set(l,r)}else f=qo,r=f.get(l),r||(r=new Map,f.set(l,r));if(r.has(t))return r;for(r.set(t,null),l=l.getElementsByTagName(t),f=0;f<l.length;f++){var d=l[f];if(!(d[$s]||d[fe]||t==="link"&&d.getAttribute("rel")==="stylesheet")&&d.namespaceURI!=="http://www.w3.org/2000/svg"){var y=d.getAttribute(n)||"";y=t+y;var _=r.get(y);_?_.push(d):r.set(y,[d])}}return r}function Ng(t,n,l){t=t.ownerDocument||t,t.head.insertBefore(l,n==="title"?t.querySelector("head > title"):null)}function Ev(t,n,l){if(l===1||n.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof n.precedence!="string"||typeof n.href!="string"||n.href==="")break;return!0;case"link":if(typeof n.rel!="string"||typeof n.href!="string"||n.href===""||n.onLoad||n.onError)break;switch(n.rel){case"stylesheet":return t=n.disabled,typeof n.precedence=="string"&&t==null;default:return!0}case"script":if(n.async&&typeof n.async!="function"&&typeof n.async!="symbol"&&!n.onLoad&&!n.onError&&n.src&&typeof n.src=="string")return!0}return!1}function Lg(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Va=null;function zv(){}function Cv(t,n,l){if(Va===null)throw Error(a(475));var r=Va;if(n.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(n.state.loading&4)===0){if(n.instance===null){var f=ks(l.href),d=t.querySelector(Ua(f));if(d){t=d._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(r.count++,r=Vo.bind(r),t.then(r,r)),n.state.loading|=4,n.instance=d,ee(d);return}d=t.ownerDocument||t,l=Rg(l),(f=Ke.get(f))&&qu(l,f),d=d.createElement("link"),ee(d);var y=d;y._p=new Promise(function(_,A){y.onload=_,y.onerror=A}),ce(d,"link",l),n.instance=d}r.stylesheets===null&&(r.stylesheets=new Map),r.stylesheets.set(n,t),(t=n.state.preload)&&(n.state.loading&3)===0&&(r.count++,n=Vo.bind(r),t.addEventListener("load",n),t.addEventListener("error",n))}}function Rv(){if(Va===null)throw Error(a(475));var t=Va;return t.stylesheets&&t.count===0&&Fu(t,t.stylesheets),0<t.count?function(n){var l=setTimeout(function(){if(t.stylesheets&&Fu(t,t.stylesheets),t.unsuspend){var r=t.unsuspend;t.unsuspend=null,r()}},6e4);return t.unsuspend=n,function(){t.unsuspend=null,clearTimeout(l)}}:null}function Vo(){if(this.count--,this.count===0){if(this.stylesheets)Fu(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Fo=null;function Fu(t,n){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Fo=new Map,n.forEach(kv,t),Fo=null,Vo.call(t))}function kv(t,n){if(!(n.state.loading&4)){var l=Fo.get(t);if(l)var r=l.get(null);else{l=new Map,Fo.set(t,l);for(var f=t.querySelectorAll("link[data-precedence],style[data-precedence]"),d=0;d<f.length;d++){var y=f[d];(y.nodeName==="LINK"||y.getAttribute("media")!=="not all")&&(l.set(y.dataset.precedence,y),r=y)}r&&l.set(null,r)}f=n.instance,y=f.getAttribute("data-precedence"),d=l.get(y)||r,d===r&&l.set(null,f),l.set(y,f),this.count++,r=Vo.bind(this),f.addEventListener("load",r),f.addEventListener("error",r),d?d.parentNode.insertBefore(f,d.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(f,t.firstChild)),n.state.loading|=4}}var Fa={$$typeof:q,Provider:null,Consumer:null,_currentValue:P,_currentValue2:P,_threadCount:0};function Bv(t,n,l,r,f,d,y,_){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Hr(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Hr(0),this.hiddenUpdates=Hr(null),this.identifierPrefix=r,this.onUncaughtError=f,this.onCaughtError=d,this.onRecoverableError=y,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=_,this.incompleteTransitions=new Map}function Hg(t,n,l,r,f,d,y,_,A,z,N,U){return t=new Bv(t,n,l,y,_,A,z,U),n=1,d===!0&&(n|=24),d=Ce(3,null,null,n),t.current=d,d.stateNode=t,n=Mc(),n.refCount++,t.pooledCache=n,n.refCount++,d.memoizedState={element:r,isDehydrated:l,cache:n},jc(d),t}function Ug(t){return t?(t=hs,t):hs}function qg(t,n,l,r,f,d){f=Ug(f),r.context===null?r.context=f:r.pendingContext=f,r=Zn(n),r.payload={element:l},d=d===void 0?null:d,d!==null&&(r.callback=d),l=Kn(t,r,n),l!==null&&(Le(l,t,n),xa(l,t,n))}function Vg(t,n){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<n?l:n}}function Yu(t,n){Vg(t,n),(t=t.alternate)&&Vg(t,n)}function Fg(t){if(t.tag===13){var n=fs(t,67108864);n!==null&&Le(n,t,67108864),Yu(t,67108864)}}var Yo=!0;function Nv(t,n,l,r){var f=k.T;k.T=null;var d=Q.p;try{Q.p=2,Xu(t,n,l,r)}finally{Q.p=d,k.T=f}}function Lv(t,n,l,r){var f=k.T;k.T=null;var d=Q.p;try{Q.p=8,Xu(t,n,l,r)}finally{Q.p=d,k.T=f}}function Xu(t,n,l,r){if(Yo){var f=Gu(r);if(f===null)zu(t,n,r,Xo,l),Xg(t,r);else if(Uv(f,t,n,l,r))r.stopPropagation();else if(Xg(t,r),n&4&&-1<Hv.indexOf(t)){for(;f!==null;){var d=$i(f);if(d!==null)switch(d.tag){case 3:if(d=d.stateNode,d.current.memoizedState.isDehydrated){var y=xi(d.pendingLanes);if(y!==0){var _=d;for(_.pendingLanes|=2,_.entangledLanes|=2;y;){var A=1<<31-Ee(y);_.entanglements[1]|=A,y&=~A}mn(d),(At&6)===0&&(jo=cn()+500,Ba(0))}}break;case 13:_=fs(d,2),_!==null&&Le(_,d,2),Oo(),Yu(d,2)}if(d=Gu(r),d===null&&zu(t,n,r,Xo,l),d===f)break;f=d}f!==null&&r.stopPropagation()}else zu(t,n,r,null,l)}}function Gu(t){return t=Pr(t),Qu(t)}var Xo=null;function Qu(t){if(Xo=null,t=Ii(t),t!==null){var n=c(t);if(n===null)t=null;else{var l=n.tag;if(l===13){if(t=u(n),t!==null)return t;t=null}else if(l===3){if(n.stateNode.current.memoizedState.isDehydrated)return n.tag===3?n.stateNode.containerInfo:null;t=null}else n!==t&&(t=null)}}return Xo=t,null}function Yg(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Mx()){case ih:return 2;case sh:return 8;case Bl:case wx:return 32;case ah:return 268435456;default:return 32}default:return 32}}var Zu=!1,oi=null,ri=null,ci=null,Ya=new Map,Xa=new Map,ui=[],Hv="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Xg(t,n){switch(t){case"focusin":case"focusout":oi=null;break;case"dragenter":case"dragleave":ri=null;break;case"mouseover":case"mouseout":ci=null;break;case"pointerover":case"pointerout":Ya.delete(n.pointerId);break;case"gotpointercapture":case"lostpointercapture":Xa.delete(n.pointerId)}}function Ga(t,n,l,r,f,d){return t===null||t.nativeEvent!==d?(t={blockedOn:n,domEventName:l,eventSystemFlags:r,nativeEvent:d,targetContainers:[f]},n!==null&&(n=$i(n),n!==null&&Fg(n)),t):(t.eventSystemFlags|=r,n=t.targetContainers,f!==null&&n.indexOf(f)===-1&&n.push(f),t)}function Uv(t,n,l,r,f){switch(n){case"focusin":return oi=Ga(oi,t,n,l,r,f),!0;case"dragenter":return ri=Ga(ri,t,n,l,r,f),!0;case"mouseover":return ci=Ga(ci,t,n,l,r,f),!0;case"pointerover":var d=f.pointerId;return Ya.set(d,Ga(Ya.get(d)||null,t,n,l,r,f)),!0;case"gotpointercapture":return d=f.pointerId,Xa.set(d,Ga(Xa.get(d)||null,t,n,l,r,f)),!0}return!1}function Gg(t){var n=Ii(t.target);if(n!==null){var l=c(n);if(l!==null){if(n=l.tag,n===13){if(n=u(l),n!==null){t.blockedOn=n,Cx(t.priority,function(){if(l.tag===13){var r=Ne();r=Ur(r);var f=fs(l,r);f!==null&&Le(f,l,r),Yu(l,r)}});return}}else if(n===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Go(t){if(t.blockedOn!==null)return!1;for(var n=t.targetContainers;0<n.length;){var l=Gu(t.nativeEvent);if(l===null){l=t.nativeEvent;var r=new l.constructor(l.type,l);Wr=r,l.target.dispatchEvent(r),Wr=null}else return n=$i(l),n!==null&&Fg(n),t.blockedOn=l,!1;n.shift()}return!0}function Qg(t,n,l){Go(t)&&l.delete(n)}function qv(){Zu=!1,oi!==null&&Go(oi)&&(oi=null),ri!==null&&Go(ri)&&(ri=null),ci!==null&&Go(ci)&&(ci=null),Ya.forEach(Qg),Xa.forEach(Qg)}function Qo(t,n){t.blockedOn===n&&(t.blockedOn=null,Zu||(Zu=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,qv)))}var Zo=null;function Zg(t){Zo!==t&&(Zo=t,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){Zo===t&&(Zo=null);for(var n=0;n<t.length;n+=3){var l=t[n],r=t[n+1],f=t[n+2];if(typeof r!="function"){if(Qu(r||l)===null)continue;break}var d=$i(l);d!==null&&(t.splice(n,3),n-=3,Qc(d,{pending:!0,data:f,method:l.method,action:r},r,f))}}))}function Qa(t){function n(A){return Qo(A,t)}oi!==null&&Qo(oi,t),ri!==null&&Qo(ri,t),ci!==null&&Qo(ci,t),Ya.forEach(n),Xa.forEach(n);for(var l=0;l<ui.length;l++){var r=ui[l];r.blockedOn===t&&(r.blockedOn=null)}for(;0<ui.length&&(l=ui[0],l.blockedOn===null);)Gg(l),l.blockedOn===null&&ui.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(r=0;r<l.length;r+=3){var f=l[r],d=l[r+1],y=f[ve]||null;if(typeof d=="function")y||Zg(l);else if(y){var _=null;if(d&&d.hasAttribute("formAction")){if(f=d,y=d[ve]||null)_=y.formAction;else if(Qu(f)!==null)continue}else _=y.action;typeof _=="function"?l[r+1]=_:(l.splice(r,3),r-=3),Zg(l)}}}function Ku(t){this._internalRoot=t}Ko.prototype.render=Ku.prototype.render=function(t){var n=this._internalRoot;if(n===null)throw Error(a(409));var l=n.current,r=Ne();qg(l,r,t,n,null,null)},Ko.prototype.unmount=Ku.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var n=t.containerInfo;qg(t.current,2,null,t,null,null),Oo(),n[Ji]=null}};function Ko(t){this._internalRoot=t}Ko.prototype.unstable_scheduleHydration=function(t){if(t){var n=uh();t={blockedOn:null,target:t,priority:n};for(var l=0;l<ui.length&&n!==0&&n<ui[l].priority;l++);ui.splice(l,0,t),l===0&&Gg(t)}};var Kg=e.version;if(Kg!=="19.1.1")throw Error(a(527,Kg,"19.1.1"));Q.findDOMNode=function(t){var n=t._reactInternals;if(n===void 0)throw typeof t.render=="function"?Error(a(188)):(t=Object.keys(t).join(","),Error(a(268,t)));return t=m(n),t=t!==null?g(t):null,t=t===null?null:t.stateNode,t};var Vv={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:k,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Wo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Wo.isDisabled&&Wo.supportsFiber)try{Ps=Wo.inject(Vv),Oe=Wo}catch{}}return Ka.createRoot=function(t,n){if(!o(t))throw Error(a(299));var l=!1,r="",f=um,d=fm,y=hm,_=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(r=n.identifierPrefix),n.onUncaughtError!==void 0&&(f=n.onUncaughtError),n.onCaughtError!==void 0&&(d=n.onCaughtError),n.onRecoverableError!==void 0&&(y=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(_=n.unstable_transitionCallbacks)),n=Hg(t,1,!1,null,null,l,r,f,d,y,_,null),t[Ji]=n.current,Eu(t),new Ku(n)},Ka.hydrateRoot=function(t,n,l){if(!o(t))throw Error(a(299));var r=!1,f="",d=um,y=fm,_=hm,A=null,z=null;return l!=null&&(l.unstable_strictMode===!0&&(r=!0),l.identifierPrefix!==void 0&&(f=l.identifierPrefix),l.onUncaughtError!==void 0&&(d=l.onUncaughtError),l.onCaughtError!==void 0&&(y=l.onCaughtError),l.onRecoverableError!==void 0&&(_=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(A=l.unstable_transitionCallbacks),l.formState!==void 0&&(z=l.formState)),n=Hg(t,1,!0,n,l??null,r,f,d,y,_,A,z),n.context=Ug(null),l=n.current,r=Ne(),r=Ur(r),f=Zn(r),f.callback=null,Kn(l,f,r),l=r,n.current.lanes=l,Is(n,l),mn(n),t[Ji]=n.current,Eu(t),new Ko(n)},Ka.version="19.1.1",Ka}var lp;function l1(){if(lp)return Wu.exports;lp=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(e){console.error(e)}}return i(),Wu.exports=a1(),Wu.exports}var o1=l1();const r1=$v(o1);function Pb(i,e){return function(){return i.apply(e,arguments)}}const{toString:c1}=Object.prototype,{getPrototypeOf:Hf}=Object,{iterator:Ar,toStringTag:Jb}=Symbol,jr=(i=>e=>{const s=c1.call(e);return i[s]||(i[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),rn=i=>(i=i.toLowerCase(),e=>jr(e)===i),Dr=i=>e=>typeof e===i,{isArray:Zs}=Array,Ys=Dr("undefined");function Tl(i){return i!==null&&!Ys(i)&&i.constructor!==null&&!Ys(i.constructor)&&je(i.constructor.isBuffer)&&i.constructor.isBuffer(i)}const Ib=rn("ArrayBuffer");function u1(i){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(i):e=i&&i.buffer&&Ib(i.buffer),e}const f1=Dr("string"),je=Dr("function"),$b=Dr("number"),Al=i=>i!==null&&typeof i=="object",h1=i=>i===!0||i===!1,hr=i=>{if(jr(i)!=="object")return!1;const e=Hf(i);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Jb in i)&&!(Ar in i)},d1=i=>{if(!Al(i)||Tl(i))return!1;try{return Object.keys(i).length===0&&Object.getPrototypeOf(i)===Object.prototype}catch{return!1}},m1=rn("Date"),g1=rn("File"),p1=rn("Blob"),b1=rn("FileList"),x1=i=>Al(i)&&je(i.pipe),y1=i=>{let e;return i&&(typeof FormData=="function"&&i instanceof FormData||je(i.append)&&((e=jr(i))==="formdata"||e==="object"&&je(i.toString)&&i.toString()==="[object FormData]"))},v1=rn("URLSearchParams"),[_1,S1,M1,w1]=["ReadableStream","Request","Response","Headers"].map(rn),T1=i=>i.trim?i.trim():i.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function jl(i,e,{allOwnKeys:s=!1}={}){if(i===null||typeof i>"u")return;let a,o;if(typeof i!="object"&&(i=[i]),Zs(i))for(a=0,o=i.length;a<o;a++)e.call(null,i[a],a,i);else{if(Tl(i))return;const c=s?Object.getOwnPropertyNames(i):Object.keys(i),u=c.length;let h;for(a=0;a<u;a++)h=c[a],e.call(null,i[h],h,i)}}function t0(i,e){if(Tl(i))return null;e=e.toLowerCase();const s=Object.keys(i);let a=s.length,o;for(;a-- >0;)if(o=s[a],e===o.toLowerCase())return o;return null}const Vi=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,e0=i=>!Ys(i)&&i!==Vi;function vf(){const{caseless:i,skipUndefined:e}=e0(this)&&this||{},s={},a=(o,c)=>{const u=i&&t0(s,c)||c;hr(s[u])&&hr(o)?s[u]=vf(s[u],o):hr(o)?s[u]=vf({},o):Zs(o)?s[u]=o.slice():(!e||!Ys(o))&&(s[u]=o)};for(let o=0,c=arguments.length;o<c;o++)arguments[o]&&jl(arguments[o],a);return s}const A1=(i,e,s,{allOwnKeys:a}={})=>(jl(e,(o,c)=>{s&&je(o)?i[c]=Pb(o,s):i[c]=o},{allOwnKeys:a}),i),j1=i=>(i.charCodeAt(0)===65279&&(i=i.slice(1)),i),D1=(i,e,s,a)=>{i.prototype=Object.create(e.prototype,a),i.prototype.constructor=i,Object.defineProperty(i,"super",{value:e.prototype}),s&&Object.assign(i.prototype,s)},O1=(i,e,s,a)=>{let o,c,u;const h={};if(e=e||{},i==null)return e;do{for(o=Object.getOwnPropertyNames(i),c=o.length;c-- >0;)u=o[c],(!a||a(u,i,e))&&!h[u]&&(e[u]=i[u],h[u]=!0);i=s!==!1&&Hf(i)}while(i&&(!s||s(i,e))&&i!==Object.prototype);return e},E1=(i,e,s)=>{i=String(i),(s===void 0||s>i.length)&&(s=i.length),s-=e.length;const a=i.indexOf(e,s);return a!==-1&&a===s},z1=i=>{if(!i)return null;if(Zs(i))return i;let e=i.length;if(!$b(e))return null;const s=new Array(e);for(;e-- >0;)s[e]=i[e];return s},C1=(i=>e=>i&&e instanceof i)(typeof Uint8Array<"u"&&Hf(Uint8Array)),R1=(i,e)=>{const a=(i&&i[Ar]).call(i);let o;for(;(o=a.next())&&!o.done;){const c=o.value;e.call(i,c[0],c[1])}},k1=(i,e)=>{let s;const a=[];for(;(s=i.exec(e))!==null;)a.push(s);return a},B1=rn("HTMLFormElement"),N1=i=>i.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,a,o){return a.toUpperCase()+o}),op=(({hasOwnProperty:i})=>(e,s)=>i.call(e,s))(Object.prototype),L1=rn("RegExp"),n0=(i,e)=>{const s=Object.getOwnPropertyDescriptors(i),a={};jl(s,(o,c)=>{let u;(u=e(o,c,i))!==!1&&(a[c]=u||o)}),Object.defineProperties(i,a)},H1=i=>{n0(i,(e,s)=>{if(je(i)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const a=i[s];if(je(a)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},U1=(i,e)=>{const s={},a=o=>{o.forEach(c=>{s[c]=!0})};return Zs(i)?a(i):a(String(i).split(e)),s},q1=()=>{},V1=(i,e)=>i!=null&&Number.isFinite(i=+i)?i:e;function F1(i){return!!(i&&je(i.append)&&i[Jb]==="FormData"&&i[Ar])}const Y1=i=>{const e=new Array(10),s=(a,o)=>{if(Al(a)){if(e.indexOf(a)>=0)return;if(Tl(a))return a;if(!("toJSON"in a)){e[o]=a;const c=Zs(a)?[]:{};return jl(a,(u,h)=>{const m=s(u,o+1);!Ys(m)&&(c[h]=m)}),e[o]=void 0,c}}return a};return s(i,0)},X1=rn("AsyncFunction"),G1=i=>i&&(Al(i)||je(i))&&je(i.then)&&je(i.catch),i0=((i,e)=>i?setImmediate:e?((s,a)=>(Vi.addEventListener("message",({source:o,data:c})=>{o===Vi&&c===s&&a.length&&a.shift()()},!1),o=>{a.push(o),Vi.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",je(Vi.postMessage)),Q1=typeof queueMicrotask<"u"?queueMicrotask.bind(Vi):typeof process<"u"&&process.nextTick||i0,Z1=i=>i!=null&&je(i[Ar]),B={isArray:Zs,isArrayBuffer:Ib,isBuffer:Tl,isFormData:y1,isArrayBufferView:u1,isString:f1,isNumber:$b,isBoolean:h1,isObject:Al,isPlainObject:hr,isEmptyObject:d1,isReadableStream:_1,isRequest:S1,isResponse:M1,isHeaders:w1,isUndefined:Ys,isDate:m1,isFile:g1,isBlob:p1,isRegExp:L1,isFunction:je,isStream:x1,isURLSearchParams:v1,isTypedArray:C1,isFileList:b1,forEach:jl,merge:vf,extend:A1,trim:T1,stripBOM:j1,inherits:D1,toFlatObject:O1,kindOf:jr,kindOfTest:rn,endsWith:E1,toArray:z1,forEachEntry:R1,matchAll:k1,isHTMLForm:B1,hasOwnProperty:op,hasOwnProp:op,reduceDescriptors:n0,freezeMethods:H1,toObjectSet:U1,toCamelCase:N1,noop:q1,toFiniteNumber:V1,findKey:t0,global:Vi,isContextDefined:e0,isSpecCompliantForm:F1,toJSONObject:Y1,isAsyncFn:X1,isThenable:G1,setImmediate:i0,asap:Q1,isIterable:Z1};function lt(i,e,s,a,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=i,this.name="AxiosError",e&&(this.code=e),s&&(this.config=s),a&&(this.request=a),o&&(this.response=o,this.status=o.status?o.status:null)}B.inherits(lt,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:B.toJSONObject(this.config),code:this.code,status:this.status}}});const s0=lt.prototype,a0={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(i=>{a0[i]={value:i}});Object.defineProperties(lt,a0);Object.defineProperty(s0,"isAxiosError",{value:!0});lt.from=(i,e,s,a,o,c)=>{const u=Object.create(s0);B.toFlatObject(i,u,function(p){return p!==Error.prototype},g=>g!=="isAxiosError");const h=i&&i.message?i.message:"Error",m=e==null&&i?i.code:e;return lt.call(u,h,m,s,a,o),i&&u.cause==null&&Object.defineProperty(u,"cause",{value:i,configurable:!0}),u.name=i&&i.name||"Error",c&&Object.assign(u,c),u};const K1=null;function _f(i){return B.isPlainObject(i)||B.isArray(i)}function l0(i){return B.endsWith(i,"[]")?i.slice(0,-2):i}function rp(i,e,s){return i?i.concat(e).map(function(o,c){return o=l0(o),!s&&c?"["+o+"]":o}).join(s?".":""):e}function W1(i){return B.isArray(i)&&!i.some(_f)}const P1=B.toFlatObject(B,{},null,function(e){return/^is[A-Z]/.test(e)});function Or(i,e,s){if(!B.isObject(i))throw new TypeError("target must be an object");e=e||new FormData,s=B.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(T,w){return!B.isUndefined(w[T])});const a=s.metaTokens,o=s.visitor||p,c=s.dots,u=s.indexes,m=(s.Blob||typeof Blob<"u"&&Blob)&&B.isSpecCompliantForm(e);if(!B.isFunction(o))throw new TypeError("visitor must be a function");function g(M){if(M===null)return"";if(B.isDate(M))return M.toISOString();if(B.isBoolean(M))return M.toString();if(!m&&B.isBlob(M))throw new lt("Blob is not supported. Use a Buffer instead.");return B.isArrayBuffer(M)||B.isTypedArray(M)?m&&typeof Blob=="function"?new Blob([M]):Buffer.from(M):M}function p(M,T,w){let O=M;if(M&&!w&&typeof M=="object"){if(B.endsWith(T,"{}"))T=a?T:T.slice(0,-2),M=JSON.stringify(M);else if(B.isArray(M)&&W1(M)||(B.isFileList(M)||B.endsWith(T,"[]"))&&(O=B.toArray(M)))return T=l0(T),O.forEach(function(q,G){!(B.isUndefined(q)||q===null)&&e.append(u===!0?rp([T],G,c):u===null?T:T+"[]",g(q))}),!1}return _f(M)?!0:(e.append(rp(w,T,c),g(M)),!1)}const x=[],v=Object.assign(P1,{defaultVisitor:p,convertValue:g,isVisitable:_f});function S(M,T){if(!B.isUndefined(M)){if(x.indexOf(M)!==-1)throw Error("Circular reference detected in "+T.join("."));x.push(M),B.forEach(M,function(O,H){(!(B.isUndefined(O)||O===null)&&o.call(e,O,B.isString(H)?H.trim():H,T,v))===!0&&S(O,T?T.concat(H):[H])}),x.pop()}}if(!B.isObject(i))throw new TypeError("data must be an object");return S(i),e}function cp(i){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(i).replace(/[!'()~]|%20|%00/g,function(a){return e[a]})}function Uf(i,e){this._pairs=[],i&&Or(i,this,e)}const o0=Uf.prototype;o0.append=function(e,s){this._pairs.push([e,s])};o0.toString=function(e){const s=e?function(a){return e.call(this,a,cp)}:cp;return this._pairs.map(function(o){return s(o[0])+"="+s(o[1])},"").join("&")};function J1(i){return encodeURIComponent(i).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+")}function r0(i,e,s){if(!e)return i;const a=s&&s.encode||J1;B.isFunction(s)&&(s={serialize:s});const o=s&&s.serialize;let c;if(o?c=o(e,s):c=B.isURLSearchParams(e)?e.toString():new Uf(e,s).toString(a),c){const u=i.indexOf("#");u!==-1&&(i=i.slice(0,u)),i+=(i.indexOf("?")===-1?"?":"&")+c}return i}class up{constructor(){this.handlers=[]}use(e,s,a){return this.handlers.push({fulfilled:e,rejected:s,synchronous:a?a.synchronous:!1,runWhen:a?a.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){B.forEach(this.handlers,function(a){a!==null&&e(a)})}}const c0={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},I1=typeof URLSearchParams<"u"?URLSearchParams:Uf,$1=typeof FormData<"u"?FormData:null,t_=typeof Blob<"u"?Blob:null,e_={isBrowser:!0,classes:{URLSearchParams:I1,FormData:$1,Blob:t_},protocols:["http","https","file","blob","url","data"]},qf=typeof window<"u"&&typeof document<"u",Sf=typeof navigator=="object"&&navigator||void 0,n_=qf&&(!Sf||["ReactNative","NativeScript","NS"].indexOf(Sf.product)<0),i_=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",s_=qf&&window.location.href||"http://localhost",a_=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:qf,hasStandardBrowserEnv:n_,hasStandardBrowserWebWorkerEnv:i_,navigator:Sf,origin:s_},Symbol.toStringTag,{value:"Module"})),pe={...a_,...e_};function l_(i,e){return Or(i,new pe.classes.URLSearchParams,{visitor:function(s,a,o,c){return pe.isNode&&B.isBuffer(s)?(this.append(a,s.toString("base64")),!1):c.defaultVisitor.apply(this,arguments)},...e})}function o_(i){return B.matchAll(/\w+|\[(\w*)]/g,i).map(e=>e[0]==="[]"?"":e[1]||e[0])}function r_(i){const e={},s=Object.keys(i);let a;const o=s.length;let c;for(a=0;a<o;a++)c=s[a],e[c]=i[c];return e}function u0(i){function e(s,a,o,c){let u=s[c++];if(u==="__proto__")return!0;const h=Number.isFinite(+u),m=c>=s.length;return u=!u&&B.isArray(o)?o.length:u,m?(B.hasOwnProp(o,u)?o[u]=[o[u],a]:o[u]=a,!h):((!o[u]||!B.isObject(o[u]))&&(o[u]=[]),e(s,a,o[u],c)&&B.isArray(o[u])&&(o[u]=r_(o[u])),!h)}if(B.isFormData(i)&&B.isFunction(i.entries)){const s={};return B.forEachEntry(i,(a,o)=>{e(o_(a),o,s,0)}),s}return null}function c_(i,e,s){if(B.isString(i))try{return(e||JSON.parse)(i),B.trim(i)}catch(a){if(a.name!=="SyntaxError")throw a}return(s||JSON.stringify)(i)}const Dl={transitional:c0,adapter:["xhr","http","fetch"],transformRequest:[function(e,s){const a=s.getContentType()||"",o=a.indexOf("application/json")>-1,c=B.isObject(e);if(c&&B.isHTMLForm(e)&&(e=new FormData(e)),B.isFormData(e))return o?JSON.stringify(u0(e)):e;if(B.isArrayBuffer(e)||B.isBuffer(e)||B.isStream(e)||B.isFile(e)||B.isBlob(e)||B.isReadableStream(e))return e;if(B.isArrayBufferView(e))return e.buffer;if(B.isURLSearchParams(e))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let h;if(c){if(a.indexOf("application/x-www-form-urlencoded")>-1)return l_(e,this.formSerializer).toString();if((h=B.isFileList(e))||a.indexOf("multipart/form-data")>-1){const m=this.env&&this.env.FormData;return Or(h?{"files[]":e}:e,m&&new m,this.formSerializer)}}return c||o?(s.setContentType("application/json",!1),c_(e)):e}],transformResponse:[function(e){const s=this.transitional||Dl.transitional,a=s&&s.forcedJSONParsing,o=this.responseType==="json";if(B.isResponse(e)||B.isReadableStream(e))return e;if(e&&B.isString(e)&&(a&&!this.responseType||o)){const u=!(s&&s.silentJSONParsing)&&o;try{return JSON.parse(e,this.parseReviver)}catch(h){if(u)throw h.name==="SyntaxError"?lt.from(h,lt.ERR_BAD_RESPONSE,this,null,this.response):h}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:pe.classes.FormData,Blob:pe.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};B.forEach(["delete","get","head","post","put","patch"],i=>{Dl.headers[i]={}});const u_=B.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),f_=i=>{const e={};let s,a,o;return i&&i.split(`
`).forEach(function(u){o=u.indexOf(":"),s=u.substring(0,o).trim().toLowerCase(),a=u.substring(o+1).trim(),!(!s||e[s]&&u_[s])&&(s==="set-cookie"?e[s]?e[s].push(a):e[s]=[a]:e[s]=e[s]?e[s]+", "+a:a)}),e},fp=Symbol("internals");function Wa(i){return i&&String(i).trim().toLowerCase()}function dr(i){return i===!1||i==null?i:B.isArray(i)?i.map(dr):String(i)}function h_(i){const e=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let a;for(;a=s.exec(i);)e[a[1]]=a[2];return e}const d_=i=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(i.trim());function Iu(i,e,s,a,o){if(B.isFunction(a))return a.call(this,e,s);if(o&&(e=s),!!B.isString(e)){if(B.isString(a))return e.indexOf(a)!==-1;if(B.isRegExp(a))return a.test(e)}}function m_(i){return i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,s,a)=>s.toUpperCase()+a)}function g_(i,e){const s=B.toCamelCase(" "+e);["get","set","has"].forEach(a=>{Object.defineProperty(i,a+s,{value:function(o,c,u){return this[a].call(this,e,o,c,u)},configurable:!0})})}let De=class{constructor(e){e&&this.set(e)}set(e,s,a){const o=this;function c(h,m,g){const p=Wa(m);if(!p)throw new Error("header name must be a non-empty string");const x=B.findKey(o,p);(!x||o[x]===void 0||g===!0||g===void 0&&o[x]!==!1)&&(o[x||m]=dr(h))}const u=(h,m)=>B.forEach(h,(g,p)=>c(g,p,m));if(B.isPlainObject(e)||e instanceof this.constructor)u(e,s);else if(B.isString(e)&&(e=e.trim())&&!d_(e))u(f_(e),s);else if(B.isObject(e)&&B.isIterable(e)){let h={},m,g;for(const p of e){if(!B.isArray(p))throw TypeError("Object iterator must return a key-value pair");h[g=p[0]]=(m=h[g])?B.isArray(m)?[...m,p[1]]:[m,p[1]]:p[1]}u(h,s)}else e!=null&&c(s,e,a);return this}get(e,s){if(e=Wa(e),e){const a=B.findKey(this,e);if(a){const o=this[a];if(!s)return o;if(s===!0)return h_(o);if(B.isFunction(s))return s.call(this,o,a);if(B.isRegExp(s))return s.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,s){if(e=Wa(e),e){const a=B.findKey(this,e);return!!(a&&this[a]!==void 0&&(!s||Iu(this,this[a],a,s)))}return!1}delete(e,s){const a=this;let o=!1;function c(u){if(u=Wa(u),u){const h=B.findKey(a,u);h&&(!s||Iu(a,a[h],h,s))&&(delete a[h],o=!0)}}return B.isArray(e)?e.forEach(c):c(e),o}clear(e){const s=Object.keys(this);let a=s.length,o=!1;for(;a--;){const c=s[a];(!e||Iu(this,this[c],c,e,!0))&&(delete this[c],o=!0)}return o}normalize(e){const s=this,a={};return B.forEach(this,(o,c)=>{const u=B.findKey(a,c);if(u){s[u]=dr(o),delete s[c];return}const h=e?m_(c):String(c).trim();h!==c&&delete s[c],s[h]=dr(o),a[h]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const s=Object.create(null);return B.forEach(this,(a,o)=>{a!=null&&a!==!1&&(s[o]=e&&B.isArray(a)?a.join(", "):a)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,s])=>e+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...s){const a=new this(e);return s.forEach(o=>a.set(o)),a}static accessor(e){const a=(this[fp]=this[fp]={accessors:{}}).accessors,o=this.prototype;function c(u){const h=Wa(u);a[h]||(g_(o,u),a[h]=!0)}return B.isArray(e)?e.forEach(c):c(e),this}};De.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);B.reduceDescriptors(De.prototype,({value:i},e)=>{let s=e[0].toUpperCase()+e.slice(1);return{get:()=>i,set(a){this[s]=a}}});B.freezeMethods(De);function $u(i,e){const s=this||Dl,a=e||s,o=De.from(a.headers);let c=a.data;return B.forEach(i,function(h){c=h.call(s,c,o.normalize(),e?e.status:void 0)}),o.normalize(),c}function f0(i){return!!(i&&i.__CANCEL__)}function Ks(i,e,s){lt.call(this,i??"canceled",lt.ERR_CANCELED,e,s),this.name="CanceledError"}B.inherits(Ks,lt,{__CANCEL__:!0});function h0(i,e,s){const a=s.config.validateStatus;!s.status||!a||a(s.status)?i(s):e(new lt("Request failed with status code "+s.status,[lt.ERR_BAD_REQUEST,lt.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function p_(i){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(i);return e&&e[1]||""}function b_(i,e){i=i||10;const s=new Array(i),a=new Array(i);let o=0,c=0,u;return e=e!==void 0?e:1e3,function(m){const g=Date.now(),p=a[c];u||(u=g),s[o]=m,a[o]=g;let x=c,v=0;for(;x!==o;)v+=s[x++],x=x%i;if(o=(o+1)%i,o===c&&(c=(c+1)%i),g-u<e)return;const S=p&&g-p;return S?Math.round(v*1e3/S):void 0}}function x_(i,e){let s=0,a=1e3/e,o,c;const u=(g,p=Date.now())=>{s=p,o=null,c&&(clearTimeout(c),c=null),i(...g)};return[(...g)=>{const p=Date.now(),x=p-s;x>=a?u(g,p):(o=g,c||(c=setTimeout(()=>{c=null,u(o)},a-x)))},()=>o&&u(o)]}const xr=(i,e,s=3)=>{let a=0;const o=b_(50,250);return x_(c=>{const u=c.loaded,h=c.lengthComputable?c.total:void 0,m=u-a,g=o(m),p=u<=h;a=u;const x={loaded:u,total:h,progress:h?u/h:void 0,bytes:m,rate:g||void 0,estimated:g&&h&&p?(h-u)/g:void 0,event:c,lengthComputable:h!=null,[e?"download":"upload"]:!0};i(x)},s)},hp=(i,e)=>{const s=i!=null;return[a=>e[0]({lengthComputable:s,total:i,loaded:a}),e[1]]},dp=i=>(...e)=>B.asap(()=>i(...e)),y_=pe.hasStandardBrowserEnv?((i,e)=>s=>(s=new URL(s,pe.origin),i.protocol===s.protocol&&i.host===s.host&&(e||i.port===s.port)))(new URL(pe.origin),pe.navigator&&/(msie|trident)/i.test(pe.navigator.userAgent)):()=>!0,v_=pe.hasStandardBrowserEnv?{write(i,e,s,a,o,c){const u=[i+"="+encodeURIComponent(e)];B.isNumber(s)&&u.push("expires="+new Date(s).toGMTString()),B.isString(a)&&u.push("path="+a),B.isString(o)&&u.push("domain="+o),c===!0&&u.push("secure"),document.cookie=u.join("; ")},read(i){const e=document.cookie.match(new RegExp("(^|;\\s*)("+i+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(i){this.write(i,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function __(i){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(i)}function S_(i,e){return e?i.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):i}function d0(i,e,s){let a=!__(e);return i&&(a||s==!1)?S_(i,e):e}const mp=i=>i instanceof De?{...i}:i;function Zi(i,e){e=e||{};const s={};function a(g,p,x,v){return B.isPlainObject(g)&&B.isPlainObject(p)?B.merge.call({caseless:v},g,p):B.isPlainObject(p)?B.merge({},p):B.isArray(p)?p.slice():p}function o(g,p,x,v){if(B.isUndefined(p)){if(!B.isUndefined(g))return a(void 0,g,x,v)}else return a(g,p,x,v)}function c(g,p){if(!B.isUndefined(p))return a(void 0,p)}function u(g,p){if(B.isUndefined(p)){if(!B.isUndefined(g))return a(void 0,g)}else return a(void 0,p)}function h(g,p,x){if(x in e)return a(g,p);if(x in i)return a(void 0,g)}const m={url:c,method:c,data:c,baseURL:u,transformRequest:u,transformResponse:u,paramsSerializer:u,timeout:u,timeoutMessage:u,withCredentials:u,withXSRFToken:u,adapter:u,responseType:u,xsrfCookieName:u,xsrfHeaderName:u,onUploadProgress:u,onDownloadProgress:u,decompress:u,maxContentLength:u,maxBodyLength:u,beforeRedirect:u,transport:u,httpAgent:u,httpsAgent:u,cancelToken:u,socketPath:u,responseEncoding:u,validateStatus:h,headers:(g,p,x)=>o(mp(g),mp(p),x,!0)};return B.forEach(Object.keys({...i,...e}),function(p){const x=m[p]||o,v=x(i[p],e[p],p);B.isUndefined(v)&&x!==h||(s[p]=v)}),s}const m0=i=>{const e=Zi({},i);let{data:s,withXSRFToken:a,xsrfHeaderName:o,xsrfCookieName:c,headers:u,auth:h}=e;if(e.headers=u=De.from(u),e.url=r0(d0(e.baseURL,e.url,e.allowAbsoluteUrls),i.params,i.paramsSerializer),h&&u.set("Authorization","Basic "+btoa((h.username||"")+":"+(h.password?unescape(encodeURIComponent(h.password)):""))),B.isFormData(s)){if(pe.hasStandardBrowserEnv||pe.hasStandardBrowserWebWorkerEnv)u.setContentType(void 0);else if(B.isFunction(s.getHeaders)){const m=s.getHeaders(),g=["content-type","content-length"];Object.entries(m).forEach(([p,x])=>{g.includes(p.toLowerCase())&&u.set(p,x)})}}if(pe.hasStandardBrowserEnv&&(a&&B.isFunction(a)&&(a=a(e)),a||a!==!1&&y_(e.url))){const m=o&&c&&v_.read(c);m&&u.set(o,m)}return e},M_=typeof XMLHttpRequest<"u",w_=M_&&function(i){return new Promise(function(s,a){const o=m0(i);let c=o.data;const u=De.from(o.headers).normalize();let{responseType:h,onUploadProgress:m,onDownloadProgress:g}=o,p,x,v,S,M;function T(){S&&S(),M&&M(),o.cancelToken&&o.cancelToken.unsubscribe(p),o.signal&&o.signal.removeEventListener("abort",p)}let w=new XMLHttpRequest;w.open(o.method.toUpperCase(),o.url,!0),w.timeout=o.timeout;function O(){if(!w)return;const q=De.from("getAllResponseHeaders"in w&&w.getAllResponseHeaders()),V={data:!h||h==="text"||h==="json"?w.responseText:w.response,status:w.status,statusText:w.statusText,headers:q,config:i,request:w};h0(function(X){s(X),T()},function(X){a(X),T()},V),w=null}"onloadend"in w?w.onloadend=O:w.onreadystatechange=function(){!w||w.readyState!==4||w.status===0&&!(w.responseURL&&w.responseURL.indexOf("file:")===0)||setTimeout(O)},w.onabort=function(){w&&(a(new lt("Request aborted",lt.ECONNABORTED,i,w)),w=null)},w.onerror=function(G){const V=G&&G.message?G.message:"Network Error",F=new lt(V,lt.ERR_NETWORK,i,w);F.event=G||null,a(F),w=null},w.ontimeout=function(){let G=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const V=o.transitional||c0;o.timeoutErrorMessage&&(G=o.timeoutErrorMessage),a(new lt(G,V.clarifyTimeoutError?lt.ETIMEDOUT:lt.ECONNABORTED,i,w)),w=null},c===void 0&&u.setContentType(null),"setRequestHeader"in w&&B.forEach(u.toJSON(),function(G,V){w.setRequestHeader(V,G)}),B.isUndefined(o.withCredentials)||(w.withCredentials=!!o.withCredentials),h&&h!=="json"&&(w.responseType=o.responseType),g&&([v,M]=xr(g,!0),w.addEventListener("progress",v)),m&&w.upload&&([x,S]=xr(m),w.upload.addEventListener("progress",x),w.upload.addEventListener("loadend",S)),(o.cancelToken||o.signal)&&(p=q=>{w&&(a(!q||q.type?new Ks(null,i,w):q),w.abort(),w=null)},o.cancelToken&&o.cancelToken.subscribe(p),o.signal&&(o.signal.aborted?p():o.signal.addEventListener("abort",p)));const H=p_(o.url);if(H&&pe.protocols.indexOf(H)===-1){a(new lt("Unsupported protocol "+H+":",lt.ERR_BAD_REQUEST,i));return}w.send(c||null)})},T_=(i,e)=>{const{length:s}=i=i?i.filter(Boolean):[];if(e||s){let a=new AbortController,o;const c=function(g){if(!o){o=!0,h();const p=g instanceof Error?g:this.reason;a.abort(p instanceof lt?p:new Ks(p instanceof Error?p.message:p))}};let u=e&&setTimeout(()=>{u=null,c(new lt(`timeout ${e} of ms exceeded`,lt.ETIMEDOUT))},e);const h=()=>{i&&(u&&clearTimeout(u),u=null,i.forEach(g=>{g.unsubscribe?g.unsubscribe(c):g.removeEventListener("abort",c)}),i=null)};i.forEach(g=>g.addEventListener("abort",c));const{signal:m}=a;return m.unsubscribe=()=>B.asap(h),m}},A_=function*(i,e){let s=i.byteLength;if(s<e){yield i;return}let a=0,o;for(;a<s;)o=a+e,yield i.slice(a,o),a=o},j_=async function*(i,e){for await(const s of D_(i))yield*A_(s,e)},D_=async function*(i){if(i[Symbol.asyncIterator]){yield*i;return}const e=i.getReader();try{for(;;){const{done:s,value:a}=await e.read();if(s)break;yield a}}finally{await e.cancel()}},gp=(i,e,s,a)=>{const o=j_(i,e);let c=0,u,h=m=>{u||(u=!0,a&&a(m))};return new ReadableStream({async pull(m){try{const{done:g,value:p}=await o.next();if(g){h(),m.close();return}let x=p.byteLength;if(s){let v=c+=x;s(v)}m.enqueue(new Uint8Array(p))}catch(g){throw h(g),g}},cancel(m){return h(m),o.return()}},{highWaterMark:2})},pp=64*1024,{isFunction:Jo}=B,O_=(({Request:i,Response:e})=>({Request:i,Response:e}))(B.global),{ReadableStream:bp,TextEncoder:xp}=B.global,yp=(i,...e)=>{try{return!!i(...e)}catch{return!1}},E_=i=>{i=B.merge.call({skipUndefined:!0},O_,i);const{fetch:e,Request:s,Response:a}=i,o=e?Jo(e):typeof fetch=="function",c=Jo(s),u=Jo(a);if(!o)return!1;const h=o&&Jo(bp),m=o&&(typeof xp=="function"?(M=>T=>M.encode(T))(new xp):async M=>new Uint8Array(await new s(M).arrayBuffer())),g=c&&h&&yp(()=>{let M=!1;const T=new s(pe.origin,{body:new bp,method:"POST",get duplex(){return M=!0,"half"}}).headers.has("Content-Type");return M&&!T}),p=u&&h&&yp(()=>B.isReadableStream(new a("").body)),x={stream:p&&(M=>M.body)};o&&["text","arrayBuffer","blob","formData","stream"].forEach(M=>{!x[M]&&(x[M]=(T,w)=>{let O=T&&T[M];if(O)return O.call(T);throw new lt(`Response type '${M}' is not supported`,lt.ERR_NOT_SUPPORT,w)})});const v=async M=>{if(M==null)return 0;if(B.isBlob(M))return M.size;if(B.isSpecCompliantForm(M))return(await new s(pe.origin,{method:"POST",body:M}).arrayBuffer()).byteLength;if(B.isArrayBufferView(M)||B.isArrayBuffer(M))return M.byteLength;if(B.isURLSearchParams(M)&&(M=M+""),B.isString(M))return(await m(M)).byteLength},S=async(M,T)=>{const w=B.toFiniteNumber(M.getContentLength());return w??v(T)};return async M=>{let{url:T,method:w,data:O,signal:H,cancelToken:q,timeout:G,onDownloadProgress:V,onUploadProgress:F,responseType:X,headers:Z,withCredentials:I="same-origin",fetchOptions:rt}=m0(M),st=e||fetch;X=X?(X+"").toLowerCase():"text";let nt=T_([H,q&&q.toAbortSignal()],G),Nt=null;const Ht=nt&&nt.unsubscribe&&(()=>{nt.unsubscribe()});let gt;try{if(F&&g&&w!=="get"&&w!=="head"&&(gt=await S(Z,O))!==0){let Tt=new s(T,{method:"POST",body:O,duplex:"half"}),ft;if(B.isFormData(O)&&(ft=Tt.headers.get("content-type"))&&Z.setContentType(ft),Tt.body){const[ct,_t]=hp(gt,xr(dp(F)));O=gp(Tt.body,pp,ct,_t)}}B.isString(I)||(I=I?"include":"omit");const k=c&&"credentials"in s.prototype,Q={...rt,signal:nt,method:w.toUpperCase(),headers:Z.normalize().toJSON(),body:O,duplex:"half",credentials:k?I:void 0};Nt=c&&new s(T,Q);let P=await(c?st(Nt,rt):st(T,Q));const xt=p&&(X==="stream"||X==="response");if(p&&(V||xt&&Ht)){const Tt={};["status","statusText","headers"].forEach(Jt=>{Tt[Jt]=P[Jt]});const ft=B.toFiniteNumber(P.headers.get("content-length")),[ct,_t]=V&&hp(ft,xr(dp(V),!0))||[];P=new a(gp(P.body,pp,ct,()=>{_t&&_t(),Ht&&Ht()}),Tt)}X=X||"text";let pt=await x[B.findKey(x,X)||"text"](P,M);return!xt&&Ht&&Ht(),await new Promise((Tt,ft)=>{h0(Tt,ft,{data:pt,headers:De.from(P.headers),status:P.status,statusText:P.statusText,config:M,request:Nt})})}catch(k){throw Ht&&Ht(),k&&k.name==="TypeError"&&/Load failed|fetch/i.test(k.message)?Object.assign(new lt("Network Error",lt.ERR_NETWORK,M,Nt),{cause:k.cause||k}):lt.from(k,k&&k.code,M,Nt)}}},z_=new Map,g0=i=>{let e=i?i.env:{};const{fetch:s,Request:a,Response:o}=e,c=[a,o,s];let u=c.length,h=u,m,g,p=z_;for(;h--;)m=c[h],g=p.get(m),g===void 0&&p.set(m,g=h?new Map:E_(e)),p=g;return g};g0();const Mf={http:K1,xhr:w_,fetch:{get:g0}};B.forEach(Mf,(i,e)=>{if(i){try{Object.defineProperty(i,"name",{value:e})}catch{}Object.defineProperty(i,"adapterName",{value:e})}});const vp=i=>`- ${i}`,C_=i=>B.isFunction(i)||i===null||i===!1,p0={getAdapter:(i,e)=>{i=B.isArray(i)?i:[i];const{length:s}=i;let a,o;const c={};for(let u=0;u<s;u++){a=i[u];let h;if(o=a,!C_(a)&&(o=Mf[(h=String(a)).toLowerCase()],o===void 0))throw new lt(`Unknown adapter '${h}'`);if(o&&(B.isFunction(o)||(o=o.get(e))))break;c[h||"#"+u]=o}if(!o){const u=Object.entries(c).map(([m,g])=>`adapter ${m} `+(g===!1?"is not supported by the environment":"is not available in the build"));let h=s?u.length>1?`since :
`+u.map(vp).join(`
`):" "+vp(u[0]):"as no adapter specified";throw new lt("There is no suitable adapter to dispatch the request "+h,"ERR_NOT_SUPPORT")}return o},adapters:Mf};function tf(i){if(i.cancelToken&&i.cancelToken.throwIfRequested(),i.signal&&i.signal.aborted)throw new Ks(null,i)}function _p(i){return tf(i),i.headers=De.from(i.headers),i.data=$u.call(i,i.transformRequest),["post","put","patch"].indexOf(i.method)!==-1&&i.headers.setContentType("application/x-www-form-urlencoded",!1),p0.getAdapter(i.adapter||Dl.adapter,i)(i).then(function(a){return tf(i),a.data=$u.call(i,i.transformResponse,a),a.headers=De.from(a.headers),a},function(a){return f0(a)||(tf(i),a&&a.response&&(a.response.data=$u.call(i,i.transformResponse,a.response),a.response.headers=De.from(a.response.headers))),Promise.reject(a)})}const b0="1.12.2",Er={};["object","boolean","number","function","string","symbol"].forEach((i,e)=>{Er[i]=function(a){return typeof a===i||"a"+(e<1?"n ":" ")+i}});const Sp={};Er.transitional=function(e,s,a){function o(c,u){return"[Axios v"+b0+"] Transitional option '"+c+"'"+u+(a?". "+a:"")}return(c,u,h)=>{if(e===!1)throw new lt(o(u," has been removed"+(s?" in "+s:"")),lt.ERR_DEPRECATED);return s&&!Sp[u]&&(Sp[u]=!0,console.warn(o(u," has been deprecated since v"+s+" and will be removed in the near future"))),e?e(c,u,h):!0}};Er.spelling=function(e){return(s,a)=>(console.warn(`${a} is likely a misspelling of ${e}`),!0)};function R_(i,e,s){if(typeof i!="object")throw new lt("options must be an object",lt.ERR_BAD_OPTION_VALUE);const a=Object.keys(i);let o=a.length;for(;o-- >0;){const c=a[o],u=e[c];if(u){const h=i[c],m=h===void 0||u(h,c,i);if(m!==!0)throw new lt("option "+c+" must be "+m,lt.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new lt("Unknown option "+c,lt.ERR_BAD_OPTION)}}const mr={assertOptions:R_,validators:Er},gn=mr.validators;let Gi=class{constructor(e){this.defaults=e||{},this.interceptors={request:new up,response:new up}}async request(e,s){try{return await this._request(e,s)}catch(a){if(a instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const c=o.stack?o.stack.replace(/^.+\n/,""):"";try{a.stack?c&&!String(a.stack).endsWith(c.replace(/^.+\n.+\n/,""))&&(a.stack+=`
`+c):a.stack=c}catch{}}throw a}}_request(e,s){typeof e=="string"?(s=s||{},s.url=e):s=e||{},s=Zi(this.defaults,s);const{transitional:a,paramsSerializer:o,headers:c}=s;a!==void 0&&mr.assertOptions(a,{silentJSONParsing:gn.transitional(gn.boolean),forcedJSONParsing:gn.transitional(gn.boolean),clarifyTimeoutError:gn.transitional(gn.boolean)},!1),o!=null&&(B.isFunction(o)?s.paramsSerializer={serialize:o}:mr.assertOptions(o,{encode:gn.function,serialize:gn.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),mr.assertOptions(s,{baseUrl:gn.spelling("baseURL"),withXsrfToken:gn.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let u=c&&B.merge(c.common,c[s.method]);c&&B.forEach(["delete","get","head","post","put","patch","common"],M=>{delete c[M]}),s.headers=De.concat(u,c);const h=[];let m=!0;this.interceptors.request.forEach(function(T){typeof T.runWhen=="function"&&T.runWhen(s)===!1||(m=m&&T.synchronous,h.unshift(T.fulfilled,T.rejected))});const g=[];this.interceptors.response.forEach(function(T){g.push(T.fulfilled,T.rejected)});let p,x=0,v;if(!m){const M=[_p.bind(this),void 0];for(M.unshift(...h),M.push(...g),v=M.length,p=Promise.resolve(s);x<v;)p=p.then(M[x++],M[x++]);return p}v=h.length;let S=s;for(;x<v;){const M=h[x++],T=h[x++];try{S=M(S)}catch(w){T.call(this,w);break}}try{p=_p.call(this,S)}catch(M){return Promise.reject(M)}for(x=0,v=g.length;x<v;)p=p.then(g[x++],g[x++]);return p}getUri(e){e=Zi(this.defaults,e);const s=d0(e.baseURL,e.url,e.allowAbsoluteUrls);return r0(s,e.params,e.paramsSerializer)}};B.forEach(["delete","get","head","options"],function(e){Gi.prototype[e]=function(s,a){return this.request(Zi(a||{},{method:e,url:s,data:(a||{}).data}))}});B.forEach(["post","put","patch"],function(e){function s(a){return function(c,u,h){return this.request(Zi(h||{},{method:e,headers:a?{"Content-Type":"multipart/form-data"}:{},url:c,data:u}))}}Gi.prototype[e]=s(),Gi.prototype[e+"Form"]=s(!0)});let k_=class x0{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(c){s=c});const a=this;this.promise.then(o=>{if(!a._listeners)return;let c=a._listeners.length;for(;c-- >0;)a._listeners[c](o);a._listeners=null}),this.promise.then=o=>{let c;const u=new Promise(h=>{a.subscribe(h),c=h}).then(o);return u.cancel=function(){a.unsubscribe(c)},u},e(function(c,u,h){a.reason||(a.reason=new Ks(c,u,h),s(a.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const s=this._listeners.indexOf(e);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const e=new AbortController,s=a=>{e.abort(a)};return this.subscribe(s),e.signal.unsubscribe=()=>this.unsubscribe(s),e.signal}static source(){let e;return{token:new x0(function(o){e=o}),cancel:e}}};function B_(i){return function(s){return i.apply(null,s)}}function N_(i){return B.isObject(i)&&i.isAxiosError===!0}const wf={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(wf).forEach(([i,e])=>{wf[e]=i});function y0(i){const e=new Gi(i),s=Pb(Gi.prototype.request,e);return B.extend(s,Gi.prototype,e,{allOwnKeys:!0}),B.extend(s,e,null,{allOwnKeys:!0}),s.create=function(o){return y0(Zi(i,o))},s}const Kt=y0(Dl);Kt.Axios=Gi;Kt.CanceledError=Ks;Kt.CancelToken=k_;Kt.isCancel=f0;Kt.VERSION=b0;Kt.toFormData=Or;Kt.AxiosError=lt;Kt.Cancel=Kt.CanceledError;Kt.all=function(e){return Promise.all(e)};Kt.spread=B_;Kt.isAxiosError=N_;Kt.mergeConfig=Zi;Kt.AxiosHeaders=De;Kt.formToJSON=i=>u0(B.isHTMLForm(i)?new FormData(i):i);Kt.getAdapter=p0.getAdapter;Kt.HttpStatusCode=wf;Kt.default=Kt;const{Axios:tA,AxiosError:eA,CanceledError:nA,isCancel:iA,CancelToken:sA,VERSION:aA,all:lA,Cancel:oA,isAxiosError:rA,spread:cA,toFormData:uA,AxiosHeaders:fA,HttpStatusCode:hA,formToJSON:dA,getAdapter:mA,mergeConfig:gA}=Kt,Vt=Kt.create({baseURL:"/api",withCredentials:!0});Vt.interceptors.response.use(i=>i,i=>{if(i.response&&i.response.data){if(i.response.data.error)return Promise.reject(new Error(i.response.data.error));if(i.response.data.detail)return Promise.reject(new Error(i.response.data.detail));if(typeof i.response.data=="object"){const e=Object.values(i.response.data).flat();if(e.length>0)return Promise.reject(new Error(e.join(" ")))}}return Promise.reject(new Error(i.message||"An unexpected error occurred."))});const L_=async i=>(await Vt.post("/login/",i)).data,H_=async()=>{await Vt.post("/api/logout/")},U_=async()=>(await Vt.get("/api/auth/validate/")).data,q_=async i=>(await Vt.post("/password-reset/request/",{email:i})).data,V_=async(i,e,s)=>(await Vt.patch(`/password-reset/confirm/${i}/${e}/`,{password:s})).data,v0=Y.createContext(void 0),F_=({children:i})=>{const[e,s]=Y.useState(null),[a,o]=Y.useState(!0);Y.useEffect(()=>{(async()=>{try{const m=await U_();s(m)}catch{s(null)}o(!1)})()},[]);const c=async h=>{const m=await L_(h);s(m)},u=async()=>{await H_(),s(null),window.location.href="/login"};return b.jsx(v0.Provider,{value:{user:e,loading:a,login:c,logout:u},children:i})},Ol=()=>{const i=Y.useContext(v0);if(i===void 0)throw new Error("useAuth must be used within an AuthProvider");return i},Ue=i=>{if(i.response?.data){const e=i.response.data;if(e.detail)return e.detail;if(typeof e=="object"){let s="";for(const a in e)Array.isArray(e[a])&&(s+=`${a}: ${e[a].join(", ")}
`);if(s)return s}}return i.message&&typeof i.message=="string"?i.message:"An unexpected error occurred."},Y_=()=>{const[i,e]=Y.useState(""),[s,a]=Y.useState(""),[o,c]=Y.useState(!1),[u,h]=Y.useState(null),{login:m,user:g,loading:p}=Ol(),x=Wb();Y.useEffect(()=>{if(g)switch(g.role){case"admin":x("/admin/dashboard");break;case"staff":x("/staff/dashboard");break;case"client":x("/client/dashboard");break;default:x("/")}},[g,x]);const v=async S=>{S.preventDefault(),h(null);try{await m({username:i,password:s})}catch(M){h(Ue(M))}};return b.jsxs(K,{sx:{minHeight:"100vh",width:"100%",display:"flex",overflow:"hidden",position:"relative"},children:[b.jsx(K,{sx:{width:{md:"60%"},background:"linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)",display:{xs:"none",md:"flex"},flexDirection:"column",justifyContent:"center",alignItems:"center",color:"white",position:"relative",padding:4,"&::before":{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,background:`
              radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%)
            `,opacity:.8}},children:b.jsxs(K,{sx:{textAlign:"center",zIndex:1,maxWidth:600},children:[b.jsx(K,{sx:{width:120,height:120,borderRadius:"50%",background:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 3rem",boxShadow:"0 25px 50px rgba(59, 130, 246, 0.4)"},children:b.jsx(Wg,{sx:{fontSize:60,color:"white"}})}),b.jsx(et,{variant:"h1",sx:{fontWeight:700,mb:3,fontSize:"4rem"},children:"Ampersand Capital"}),b.jsx(et,{variant:"h4",sx:{opacity:.9,mb:4,fontWeight:300},children:"Professional Investment Management"}),b.jsx(et,{variant:"h6",sx:{opacity:.8,lineHeight:1.8,maxWidth:500,margin:"0 auto"},children:"Secure access to your investment portfolio, performance analytics, and comprehensive financial reporting. Your trusted partner in wealth management."})]})}),b.jsx(K,{sx:{width:{md:"40%"},display:{xs:"none",md:"flex"},alignItems:"center",justifyContent:"center",backgroundColor:"#ffffff",padding:6},children:b.jsx(K,{sx:{width:"100%",maxWidth:450},children:b.jsxs(K,{sx:{display:"flex",flexDirection:"column",alignItems:"center",width:"100%"},children:[b.jsx(et,{variant:"h3",component:"h1",sx:{fontWeight:700,color:"#0f172a",mb:2,textAlign:"center"},children:"Welcome Back"}),b.jsx(et,{variant:"h6",color:"text.secondary",sx:{mb:6,textAlign:"center",fontWeight:400},children:"Please sign in to access your account"}),b.jsxs(K,{component:"form",onSubmit:v,noValidate:!0,sx:{width:"100%"},children:[b.jsx(Mt,{margin:"normal",required:!0,fullWidth:!0,id:"username",label:"Username",name:"username",autoComplete:"username",autoFocus:!0,value:i,onChange:S=>e(S.target.value),slotProps:{input:{startAdornment:b.jsx(Ns,{position:"start",children:b.jsx(Pg,{sx:{color:"#64748b",fontSize:24}})})}},sx:{mb:3,"& .MuiOutlinedInput-root":{borderRadius:3,height:60,fontSize:"1.1rem",backgroundColor:"#f8fafc",border:"2px solid transparent","& fieldset":{borderColor:"#e2e8f0"},"&:hover":{backgroundColor:"#f1f5f9","& fieldset":{borderColor:"#3b82f6"}},"&.Mui-focused":{backgroundColor:"white","& fieldset":{borderColor:"#3b82f6",borderWidth:"2px"}}},"& .MuiInputLabel-root":{fontSize:"1.1rem","&.Mui-focused":{color:"#3b82f6"}}}}),b.jsx(Mt,{margin:"normal",required:!0,fullWidth:!0,name:"password",label:"Password",type:o?"text":"password",id:"password",autoComplete:"current-password",value:s,onChange:S=>a(S.target.value),slotProps:{input:{startAdornment:b.jsx(Ns,{position:"start",children:b.jsx($g,{sx:{color:"#64748b",fontSize:24}})}),endAdornment:b.jsx(Ns,{position:"end",children:b.jsx(hl,{"aria-label":"toggle password visibility",onClick:()=>c(!o),edge:"end",sx:{color:"#64748b"},children:o?b.jsx(Jg,{}):b.jsx(Ig,{})})})}},sx:{mb:4,"& .MuiOutlinedInput-root":{borderRadius:3,height:60,fontSize:"1.1rem",backgroundColor:"#f8fafc",border:"2px solid transparent","& fieldset":{borderColor:"#e2e8f0"},"&:hover":{backgroundColor:"#f1f5f9","& fieldset":{borderColor:"#3b82f6"}},"&.Mui-focused":{backgroundColor:"white","& fieldset":{borderColor:"#3b82f6",borderWidth:"2px"}}},"& .MuiInputLabel-root":{fontSize:"1.1rem","&.Mui-focused":{color:"#3b82f6"}}}}),u&&b.jsx(le,{severity:"error",sx:{width:"100%",mb:3,borderRadius:2,backgroundColor:"#fef2f2",border:"1px solid #fecaca",color:"#dc2626"},children:u}),b.jsx(Ct,{type:"submit",fullWidth:!0,variant:"contained",disabled:p,sx:{height:60,borderRadius:3,background:"linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%)",fontSize:"1.2rem",fontWeight:600,textTransform:"none",boxShadow:"0 8px 32px rgba(30, 64, 175, 0.3)",transition:"all 0.3s ease","&:hover":{background:"linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%)",boxShadow:"0 12px 40px rgba(30, 64, 175, 0.4)",transform:"translateY(-2px)"},"&:disabled":{background:"#e2e8f0",color:"#64748b",boxShadow:"none",transform:"none"}},children:p?b.jsx($e,{size:24,sx:{color:"white"}}):"Sign In"}),b.jsx(K,{sx:{textAlign:"center",mt:4},children:b.jsx(yf,{component:ye,to:"/password-reset",variant:"body2",sx:{color:"#64748b",textDecoration:"none",fontSize:"1rem","&:hover":{color:"#3b82f6",textDecoration:"underline"}},children:"Forgot password?"})})]})]})})}),b.jsxs(K,{sx:{width:"100%",minHeight:"100vh",display:{xs:"flex",md:"none"},flexDirection:"column",background:"linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)",position:"relative","&::before":{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,background:`
              radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%)
            `,opacity:.8}},children:[b.jsxs(K,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",color:"white",textAlign:"center",padding:{xs:2,sm:3},paddingTop:{xs:4,sm:6},paddingBottom:{xs:2,sm:3},zIndex:1},children:[b.jsx(K,{sx:{width:{xs:60,sm:70},height:{xs:60,sm:70},borderRadius:"50%",background:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 1.5rem",boxShadow:"0 15px 30px rgba(59, 130, 246, 0.3)"},children:b.jsx(Wg,{sx:{fontSize:{xs:28,sm:32},color:"white"}})}),b.jsx(et,{variant:"h5",sx:{fontWeight:700,mb:.5,fontSize:{xs:"1.25rem",sm:"1.5rem"},lineHeight:1.2},children:"Ampersand Capital"}),b.jsx(et,{variant:"body2",sx:{opacity:.9,mb:2,fontSize:{xs:"0.875rem",sm:"1rem"}},children:"Professional Investment Management"})]}),b.jsxs(K,{sx:{backgroundColor:"white",borderTopLeftRadius:{xs:24,sm:32},borderTopRightRadius:{xs:24,sm:32},padding:{xs:3,sm:4},paddingTop:{xs:4,sm:5},flex:1,width:"100%",zIndex:1,marginTop:"auto",maxWidth:"100%",overflow:"hidden"},children:[b.jsx(et,{variant:"h6",component:"h1",sx:{fontWeight:700,color:"#0f172a",mb:.5,textAlign:"center",fontSize:{xs:"1.125rem",sm:"1.25rem"}},children:"Welcome Back"}),b.jsx(et,{variant:"body2",color:"text.secondary",sx:{mb:{xs:3,sm:4},textAlign:"center",fontSize:{xs:"0.875rem",sm:"1rem"}},children:"Please sign in to access your account"}),b.jsxs(K,{component:"form",onSubmit:v,sx:{width:"100%"},children:[b.jsx(Mt,{margin:"normal",required:!0,fullWidth:!0,id:"username-mobile",label:"Username",name:"username",autoComplete:"username",value:i,onChange:S=>e(S.target.value),sx:{mb:{xs:1.5,sm:2},"& .MuiOutlinedInput-root":{borderRadius:2,height:{xs:44,sm:48},backgroundColor:"#f8fafc",fontSize:{xs:"0.875rem",sm:"1rem"},"& fieldset":{borderColor:"#e2e8f0"},"&:hover fieldset":{borderColor:"#3b82f6"},"&.Mui-focused fieldset":{borderColor:"#3b82f6"}},"& .MuiInputLabel-root":{fontSize:{xs:"0.875rem",sm:"1rem"},"&.Mui-focused":{color:"#3b82f6"}}},slotProps:{input:{startAdornment:b.jsx(Ns,{position:"start",children:b.jsx(Pg,{sx:{color:"#64748b",fontSize:{xs:18,sm:20}}})})}}}),b.jsx(Mt,{margin:"normal",required:!0,fullWidth:!0,name:"password",label:"Password",type:o?"text":"password",id:"password-mobile",autoComplete:"current-password",value:s,onChange:S=>a(S.target.value),sx:{mb:{xs:2,sm:3},"& .MuiOutlinedInput-root":{borderRadius:2,height:{xs:44,sm:48},backgroundColor:"#f8fafc",fontSize:{xs:"0.875rem",sm:"1rem"},"& fieldset":{borderColor:"#e2e8f0"},"&:hover fieldset":{borderColor:"#3b82f6"},"&.Mui-focused fieldset":{borderColor:"#3b82f6"}},"& .MuiInputLabel-root":{fontSize:{xs:"0.875rem",sm:"1rem"},"&.Mui-focused":{color:"#3b82f6"}}},slotProps:{input:{startAdornment:b.jsx(Ns,{position:"start",children:b.jsx($g,{sx:{color:"#64748b",fontSize:{xs:18,sm:20}}})}),endAdornment:b.jsx(Ns,{position:"end",children:b.jsx(hl,{"aria-label":"toggle password visibility",onClick:()=>c(!o),edge:"end",sx:{color:"#64748b",padding:{xs:.5,sm:1}},children:o?b.jsx(Jg,{sx:{fontSize:{xs:18,sm:20}}}):b.jsx(Ig,{sx:{fontSize:{xs:18,sm:20}}})})})}}}),u&&b.jsx(le,{severity:"error",sx:{width:"100%",mb:{xs:2,sm:3},borderRadius:2,backgroundColor:"#fef2f2",border:"1px solid #fecaca",color:"#dc2626",fontSize:{xs:"0.875rem",sm:"1rem"}},children:u}),b.jsx(Ct,{type:"submit",fullWidth:!0,variant:"contained",disabled:p,sx:{height:{xs:44,sm:48},borderRadius:2,background:"linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%)",fontSize:{xs:"1rem",sm:"1.1rem"},fontWeight:600,textTransform:"none",boxShadow:"0 4px 16px rgba(30, 64, 175, 0.3)","&:hover":{background:"linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%)",boxShadow:"0 6px 20px rgba(30, 64, 175, 0.4)"},"&:disabled":{background:"#e2e8f0",color:"#64748b",boxShadow:"none"}},children:p?b.jsx($e,{size:18,sx:{color:"white"}}):"Sign In"}),b.jsx(K,{sx:{textAlign:"center",mt:{xs:2,sm:3}},children:b.jsx(yf,{component:ye,to:"/password-reset",variant:"body2",sx:{color:"#64748b",textDecoration:"none",fontSize:{xs:"0.875rem",sm:"1rem"},"&:hover":{color:"#3b82f6",textDecoration:"underline"}},children:"Forgot password?"})})]})]})]})]})},X_=()=>b.jsxs("div",{children:[b.jsx("h1",{children:"Unauthorized"}),b.jsx("p",{children:"You do not have permission to view this page."})]}),ef=({allowedRoles:i})=>{const{user:e,loading:s}=Ol();return s?b.jsx("div",{children:"Loading..."}):e?i.includes(e.role)?b.jsx(Tr,{}):b.jsx(Hs,{to:"/unauthorized",replace:!0}):b.jsx(Hs,{to:"/login",replace:!0})},nf=240,G_=({mobileOpen:i,handleDrawerToggle:e})=>{const s=b.jsxs(K,{children:[b.jsx(Vn,{}),b.jsx(K,{sx:{overflow:"auto"},children:b.jsxs(Fs,{children:[b.jsx(me,{disablePadding:!0,component:ye,to:"/admin/dashboard",children:b.jsxs(Pe,{children:[b.jsx(Je,{children:b.jsx(Cf,{})}),b.jsx(ge,{primary:"Dashboard"})]})}),b.jsx(me,{disablePadding:!0,component:ye,to:"/admin/staff",children:b.jsxs(Pe,{children:[b.jsx(Je,{children:b.jsx(dl,{})}),b.jsx(ge,{primary:"Staff"})]})}),b.jsx(me,{disablePadding:!0,component:ye,to:"/admin/clients",children:b.jsxs(Pe,{children:[b.jsx(Je,{children:b.jsx(dl,{})}),b.jsx(ge,{primary:"Clients"})]})}),b.jsx(me,{disablePadding:!0,component:ye,to:"/admin/register-staff",children:b.jsxs(Pe,{children:[b.jsx(Je,{children:b.jsx(Fb,{})}),b.jsx(ge,{primary:"Register Staff"})]})})]})})]});return b.jsxs(K,{component:"nav",sx:{width:{sm:nf},flexShrink:{sm:0}},"aria-label":"mailbox folders",children:[b.jsx(Vs,{variant:"temporary",open:i,onClose:e,keepMounted:!0,sx:{display:{xs:"block",sm:"none"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:nf}},children:s}),b.jsx(Vs,{variant:"permanent",sx:{display:{xs:"none",sm:"block"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:nf}},children:s})]})},Q_=({children:i})=>{const[e,s]=Y.useState(!1),a=Rf(),o=kf(a.breakpoints.down("sm")),{user:c,logout:u}=Ol(),h=()=>{s(!e)};return b.jsxs(K,{sx:{display:"flex"},children:[b.jsx(Bf,{position:"fixed",sx:{zIndex:m=>m.zIndex.drawer+1},children:b.jsxs(Vn,{children:[o&&b.jsx(hl,{color:"inherit","aria-label":"open drawer",edge:"start",onClick:h,sx:{mr:2,display:{sm:"none"}},children:b.jsx(Nf,{})}),b.jsx(et,{variant:"h6",component:"div",sx:{flexGrow:1},children:"Ampersand Capital"}),c&&b.jsx(Ct,{color:"inherit",onClick:u,children:"Logout"})]})}),b.jsx(G_,{mobileOpen:e,handleDrawerToggle:h}),b.jsxs(K,{component:"main",sx:{flexGrow:1,p:3},children:[b.jsx(Vn,{}),i]})]})},Z_=()=>b.jsx(Q_,{children:b.jsx(Tr,{})}),K_=async()=>(await Vt.get("/api/admin/dashboard-stats/")).data,W_=async()=>(await Vt.get("/api/admin/staff-list/")).data.staff,P_=async()=>(await Vt.get("/api/admin/client-list/")).data.clients,J_=async i=>(await Vt.post("/api/admin/register-staff/",i)).data,Mp=async i=>(await Vt.get("/api/admin/search-staff/",{params:{query:i}})).data,wp=async i=>(await Vt.get("/api/admin/search-clients/",{params:{name_query:i}})).data,sf=({title:i,value:e,icon:s})=>b.jsxs(Xv,{sx:{display:"flex",alignItems:"center",p:2},children:[b.jsxs(K,{sx:{flexGrow:1},children:[b.jsx(et,{color:"text.secondary",gutterBottom:!0,children:i}),b.jsx(et,{variant:"h4",component:"div",children:e})]}),s]}),I_=()=>{const[i,e]=Y.useState(null),[s,a]=Y.useState(!0);return Y.useEffect(()=>{(async()=>{try{const c=await K_();e(c)}catch(c){console.error("Failed to fetch dashboard stats",c)}a(!1)})()},[]),s?b.jsx("div",{children:"Loading..."}):b.jsxs(K,{children:[b.jsxs(et,{variant:"h4",gutterBottom:!0,children:["Welcome, ",i?.adminName||"Admin"]}),b.jsxs(Qt,{container:!0,spacing:3,children:[b.jsx(Qt,{size:{xs:12,sm:6,md:4},children:b.jsx(sf,{title:"Total Staff",value:i?.staffCount,icon:b.jsx(dl,{sx:{fontSize:40,color:"primary.main"}})})}),b.jsx(Qt,{size:{xs:12,sm:6,md:4},children:b.jsx(sf,{title:"Total Clients",value:i?.clientCount,icon:b.jsx(dl,{sx:{fontSize:40,color:"secondary.main"}})})}),b.jsx(Qt,{size:{xs:12,sm:6,md:4},children:b.jsx(sf,{title:"Total Folios",value:i?.folioCount,icon:b.jsx(Yv,{sx:{fontSize:40,color:"error.main"}})})})]})]})},$_=()=>{const[i,e]=Y.useState([]),[s,a]=Y.useState(!0),[o,c]=Y.useState(""),[u,h]=Y.useState(null),m=async()=>{a(!0);try{const x=await W_();e(x)}catch(x){console.error("Failed to fetch staff list",x)}a(!1)};Y.useEffect(()=>{m()},[]);const g=async x=>{x.preventDefault(),a(!0),h(null);try{const v=await Mp(o);e(v)}catch(v){h(Ue(v)),e([])}a(!1)},p=async()=>{c(""),a(!0),h(null);try{const x=await Mp("");e(x)}catch(x){h(Ue(x)),e([])}a(!1)};return b.jsxs(K,{children:[b.jsx(et,{variant:"h4",gutterBottom:!0,children:"Staff List"}),b.jsxs(K,{component:"form",onSubmit:g,sx:{mb:4,display:"flex",gap:2,alignItems:"center"},children:[b.jsx(Mt,{label:"Search by Name or Staff ID",value:o,onChange:x=>c(x.target.value),variant:"outlined",sx:{flexGrow:1},placeholder:"Leave empty and search to show all staff"}),b.jsx(Ct,{type:"submit",variant:"contained",disabled:s,children:s?b.jsx($e,{size:24}):"Search"}),b.jsx(Ct,{variant:"outlined",onClick:p,disabled:s,sx:{whiteSpace:"nowrap"},children:"Show All"})]}),u&&b.jsx(le,{severity:"error",sx:{mb:2},children:u}),b.jsx(K,{sx:{overflowX:"auto"},children:b.jsx(ml,{component:on,children:b.jsxs(gl,{sx:{minWidth:650},"aria-label":"simple table",children:[b.jsx(pl,{children:b.jsxs(yn,{children:[b.jsx(at,{children:"Staff ID"}),b.jsx(at,{children:"First Name"}),b.jsx(at,{children:"Last Name"}),b.jsx(at,{children:"Email"})]})}),b.jsx(bl,{children:i.map(x=>b.jsxs(yn,{children:[b.jsx(at,{children:x.user}),b.jsx(at,{children:x.first_name}),b.jsx(at,{children:x.last_name}),b.jsx(at,{children:x.email})]},x.id))})]})})})]})},tS=()=>{const[i,e]=Y.useState([]),[s,a]=Y.useState(!0),[o,c]=Y.useState(""),[u,h]=Y.useState(null),m=async()=>{a(!0);try{const x=await P_();e(x)}catch(x){console.error("Failed to fetch client list",x)}a(!1)};Y.useEffect(()=>{m()},[]);const g=async x=>{x.preventDefault(),a(!0),h(null);try{const v=await wp(o);e(v)}catch(v){h(Ue(v)),e([])}a(!1)},p=async()=>{c(""),a(!0),h(null);try{const x=await wp("");e(x)}catch(x){h(Ue(x)),e([])}a(!1)};return b.jsxs(K,{children:[b.jsx(et,{variant:"h4",gutterBottom:!0,children:"Client List"}),b.jsxs(K,{component:"form",onSubmit:g,sx:{mb:4,display:"flex",gap:2,alignItems:"center"},children:[b.jsx(Mt,{label:"Search by Name or Folio Number",value:o,onChange:x=>c(x.target.value),variant:"outlined",sx:{flexGrow:1},placeholder:"Leave empty and search to show all clients"}),b.jsx(Ct,{type:"submit",variant:"contained",disabled:s,children:s?b.jsx($e,{size:24}):"Search"}),b.jsx(Ct,{variant:"outlined",onClick:p,disabled:s,sx:{whiteSpace:"nowrap"},children:"Show All"})]}),u&&b.jsx(le,{severity:"error",sx:{mb:2},children:u}),b.jsx(K,{sx:{overflowX:"auto"},children:b.jsx(ml,{component:on,children:b.jsxs(gl,{sx:{minWidth:650},"aria-label":"simple table",children:[b.jsx(pl,{children:b.jsxs(yn,{children:[b.jsx(at,{children:"First Name"}),b.jsx(at,{children:"Last Name"}),b.jsx(at,{children:"Email"}),b.jsx(at,{children:"PAN"})]})}),b.jsx(bl,{children:i.map(x=>b.jsxs(yn,{children:[b.jsx(at,{children:x.first_name}),b.jsx(at,{children:x.last_name}),b.jsx(at,{children:x.email}),b.jsx(at,{children:x.pan})]},x.id))})]})})})]})},eS=()=>{const[i,e]=Y.useState(""),[s,a]=Y.useState(""),[o,c]=Y.useState(""),[u,h]=Y.useState(!1),[m,g]=Y.useState(null),[p,x]=Y.useState(null),v=async S=>{S.preventDefault(),h(!0),g(null),x(null);try{await J_({email:i,first_name:s,last_name:o}),x("Staff member registered successfully!"),e(""),a(""),c("")}catch(M){g(Ue(M))}h(!1)};return b.jsxs(K,{sx:{maxWidth:500},children:[b.jsx(et,{variant:"h4",gutterBottom:!0,children:"Register New Staff Member"}),b.jsxs(K,{component:"form",onSubmit:v,noValidate:!0,sx:{mt:1},children:[b.jsx(Mt,{margin:"normal",required:!0,fullWidth:!0,id:"email",label:"Email Address",name:"email",autoComplete:"email",value:i,onChange:S=>e(S.target.value)}),b.jsx(Mt,{margin:"normal",required:!0,fullWidth:!0,id:"firstName",label:"First Name",name:"firstName",autoComplete:"given-name",value:s,onChange:S=>a(S.target.value)}),b.jsx(Mt,{margin:"normal",required:!0,fullWidth:!0,id:"lastName",label:"Last Name",name:"lastName",autoComplete:"family-name",value:o,onChange:S=>c(S.target.value)}),m&&b.jsx(le,{severity:"error",sx:{width:"100%",mt:2},children:m}),p&&b.jsx(le,{severity:"success",sx:{width:"100%",mt:2},children:p}),b.jsx(Ct,{type:"submit",fullWidth:!0,variant:"contained",sx:{mt:3,mb:2},disabled:u,children:u?b.jsx($e,{size:24}):"Register Staff"})]})]})},af=240,nS=({mobileOpen:i,handleDrawerToggle:e})=>{const s=b.jsxs(K,{children:[b.jsx(Vn,{}),b.jsx(K,{sx:{overflow:"auto"},children:b.jsxs(Fs,{children:[b.jsx(me,{disablePadding:!0,component:ye,to:"/staff/dashboard",children:b.jsxs(Pe,{children:[b.jsx(Je,{children:b.jsx(Cf,{})}),b.jsx(ge,{primary:"Dashboard"})]})}),b.jsx(me,{disablePadding:!0,component:ye,to:"/staff/register-client",children:b.jsxs(Pe,{children:[b.jsx(Je,{children:b.jsx(Fb,{})}),b.jsx(ge,{primary:"Register Client"})]})}),b.jsx(me,{disablePadding:!0,component:ye,to:"/staff/upload-document",children:b.jsxs(Pe,{children:[b.jsx(Je,{children:b.jsx(Gv,{})}),b.jsx(ge,{primary:"Upload Document"})]})}),b.jsx(me,{disablePadding:!0,component:ye,to:"/staff/clients",children:b.jsxs(Pe,{children:[b.jsx(Je,{children:b.jsx(dl,{})}),b.jsx(ge,{primary:"Client Directory"})]})}),b.jsx(me,{disablePadding:!0,component:ye,to:"/staff/documents",children:b.jsxs(Pe,{children:[b.jsx(Je,{children:b.jsx(Yb,{})}),b.jsx(ge,{primary:"Document Directory"})]})}),b.jsx(me,{disablePadding:!0,component:ye,to:"/staff/terminate-folio",children:b.jsxs(Pe,{children:[b.jsx(Je,{children:b.jsx(Qv,{})}),b.jsx(ge,{primary:"Terminate Folio"})]})})]})})]});return b.jsxs(K,{component:"nav",sx:{width:{sm:af},flexShrink:{sm:0}},"aria-label":"mailbox folders",children:[b.jsx(Vs,{variant:"temporary",open:i,onClose:e,ModalProps:{keepMounted:!0},sx:{display:{xs:"block",sm:"none"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:af}},children:s}),b.jsx(Vs,{variant:"permanent",sx:{display:{xs:"none",sm:"block"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:af}},open:!0,children:s})]})},iS=({children:i})=>{const[e,s]=Y.useState(!1),a=Rf(),o=kf(a.breakpoints.down("sm")),{user:c,logout:u}=Ol(),h=()=>{s(!e)};return b.jsxs(K,{sx:{display:"flex"},children:[b.jsx(Bf,{position:"fixed",sx:{zIndex:m=>m.zIndex.drawer+1},children:b.jsxs(Vn,{children:[o&&b.jsx(hl,{color:"inherit","aria-label":"open drawer",edge:"start",onClick:h,sx:{mr:2,display:{sm:"none"}},children:b.jsx(Nf,{})}),b.jsx(et,{variant:"h6",component:"div",sx:{flexGrow:1},children:"Ampersand Capital"}),c&&b.jsx(Ct,{color:"inherit",onClick:u,children:"Logout"})]})}),b.jsx(nS,{mobileOpen:e,handleDrawerToggle:h}),b.jsxs(K,{component:"main",sx:{flexGrow:1,p:3},children:[b.jsx(Vn,{}),i]})]})},sS=()=>b.jsx(iS,{children:b.jsx(Tr,{})}),Tp=async i=>(await Vt.get("/api/staff/search-clients/",{params:i})).data,aS=async i=>(await Vt.post("/api/staff/register-client/",i)).data,lS=async i=>(await Vt.post("/api/staff/upload/",i,{headers:{"Content-Type":"multipart/form-data"}})).data,_0=async i=>(await Vt.get("/api/staff/documents/",{params:i})).data,oS=async i=>(await Vt.get(`/api/staff/download-document/${i}/`,{responseType:"blob"})).data,rS=async(i,e)=>(await Vt.post("/api/staff/create-folio/",{client_username:i,folio_number:e})).data,cS=async()=>(await Vt.get("/api/staff/dashboard-stats/")).data,uS=async i=>(await Vt.delete("/api/staff/create-folio/",{data:{folio_number:i}})).data;/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function El(i){return i+.5|0}const di=(i,e,s)=>Math.max(Math.min(i,s),e);function al(i){return di(El(i*2.55),0,255)}function gi(i){return di(El(i*255),0,255)}function qn(i){return di(El(i/2.55)/100,0,1)}function Ap(i){return di(El(i*100),0,100)}const We={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Tf=[..."0123456789ABCDEF"],fS=i=>Tf[i&15],hS=i=>Tf[(i&240)>>4]+Tf[i&15],Io=i=>(i&240)>>4===(i&15),dS=i=>Io(i.r)&&Io(i.g)&&Io(i.b)&&Io(i.a);function mS(i){var e=i.length,s;return i[0]==="#"&&(e===4||e===5?s={r:255&We[i[1]]*17,g:255&We[i[2]]*17,b:255&We[i[3]]*17,a:e===5?We[i[4]]*17:255}:(e===7||e===9)&&(s={r:We[i[1]]<<4|We[i[2]],g:We[i[3]]<<4|We[i[4]],b:We[i[5]]<<4|We[i[6]],a:e===9?We[i[7]]<<4|We[i[8]]:255})),s}const gS=(i,e)=>i<255?e(i):"";function pS(i){var e=dS(i)?fS:hS;return i?"#"+e(i.r)+e(i.g)+e(i.b)+gS(i.a,e):void 0}const bS=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function S0(i,e,s){const a=e*Math.min(s,1-s),o=(c,u=(c+i/30)%12)=>s-a*Math.max(Math.min(u-3,9-u,1),-1);return[o(0),o(8),o(4)]}function xS(i,e,s){const a=(o,c=(o+i/60)%6)=>s-s*e*Math.max(Math.min(c,4-c,1),0);return[a(5),a(3),a(1)]}function yS(i,e,s){const a=S0(i,1,.5);let o;for(e+s>1&&(o=1/(e+s),e*=o,s*=o),o=0;o<3;o++)a[o]*=1-e-s,a[o]+=e;return a}function vS(i,e,s,a,o){return i===o?(e-s)/a+(e<s?6:0):e===o?(s-i)/a+2:(i-e)/a+4}function Vf(i){const s=i.r/255,a=i.g/255,o=i.b/255,c=Math.max(s,a,o),u=Math.min(s,a,o),h=(c+u)/2;let m,g,p;return c!==u&&(p=c-u,g=h>.5?p/(2-c-u):p/(c+u),m=vS(s,a,o,p,c),m=m*60+.5),[m|0,g||0,h]}function Ff(i,e,s,a){return(Array.isArray(e)?i(e[0],e[1],e[2]):i(e,s,a)).map(gi)}function Yf(i,e,s){return Ff(S0,i,e,s)}function _S(i,e,s){return Ff(yS,i,e,s)}function SS(i,e,s){return Ff(xS,i,e,s)}function M0(i){return(i%360+360)%360}function MS(i){const e=bS.exec(i);let s=255,a;if(!e)return;e[5]!==a&&(s=e[6]?al(+e[5]):gi(+e[5]));const o=M0(+e[2]),c=+e[3]/100,u=+e[4]/100;return e[1]==="hwb"?a=_S(o,c,u):e[1]==="hsv"?a=SS(o,c,u):a=Yf(o,c,u),{r:a[0],g:a[1],b:a[2],a:s}}function wS(i,e){var s=Vf(i);s[0]=M0(s[0]+e),s=Yf(s),i.r=s[0],i.g=s[1],i.b=s[2]}function TS(i){if(!i)return;const e=Vf(i),s=e[0],a=Ap(e[1]),o=Ap(e[2]);return i.a<255?`hsla(${s}, ${a}%, ${o}%, ${qn(i.a)})`:`hsl(${s}, ${a}%, ${o}%)`}const jp={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Dp={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function AS(){const i={},e=Object.keys(Dp),s=Object.keys(jp);let a,o,c,u,h;for(a=0;a<e.length;a++){for(u=h=e[a],o=0;o<s.length;o++)c=s[o],h=h.replace(c,jp[c]);c=parseInt(Dp[u],16),i[h]=[c>>16&255,c>>8&255,c&255]}return i}let $o;function jS(i){$o||($o=AS(),$o.transparent=[0,0,0,0]);const e=$o[i.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:e.length===4?e[3]:255}}const DS=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function OS(i){const e=DS.exec(i);let s=255,a,o,c;if(e){if(e[7]!==a){const u=+e[7];s=e[8]?al(u):di(u*255,0,255)}return a=+e[1],o=+e[3],c=+e[5],a=255&(e[2]?al(a):di(a,0,255)),o=255&(e[4]?al(o):di(o,0,255)),c=255&(e[6]?al(c):di(c,0,255)),{r:a,g:o,b:c,a:s}}}function ES(i){return i&&(i.a<255?`rgba(${i.r}, ${i.g}, ${i.b}, ${qn(i.a)})`:`rgb(${i.r}, ${i.g}, ${i.b})`)}const lf=i=>i<=.0031308?i*12.92:Math.pow(i,1/2.4)*1.055-.055,Ls=i=>i<=.04045?i/12.92:Math.pow((i+.055)/1.055,2.4);function zS(i,e,s){const a=Ls(qn(i.r)),o=Ls(qn(i.g)),c=Ls(qn(i.b));return{r:gi(lf(a+s*(Ls(qn(e.r))-a))),g:gi(lf(o+s*(Ls(qn(e.g))-o))),b:gi(lf(c+s*(Ls(qn(e.b))-c))),a:i.a+s*(e.a-i.a)}}function tr(i,e,s){if(i){let a=Vf(i);a[e]=Math.max(0,Math.min(a[e]+a[e]*s,e===0?360:1)),a=Yf(a),i.r=a[0],i.g=a[1],i.b=a[2]}}function w0(i,e){return i&&Object.assign(e||{},i)}function Op(i){var e={r:0,g:0,b:0,a:255};return Array.isArray(i)?i.length>=3&&(e={r:i[0],g:i[1],b:i[2],a:255},i.length>3&&(e.a=gi(i[3]))):(e=w0(i,{r:0,g:0,b:0,a:1}),e.a=gi(e.a)),e}function CS(i){return i.charAt(0)==="r"?OS(i):MS(i)}class xl{constructor(e){if(e instanceof xl)return e;const s=typeof e;let a;s==="object"?a=Op(e):s==="string"&&(a=mS(e)||jS(e)||CS(e)),this._rgb=a,this._valid=!!a}get valid(){return this._valid}get rgb(){var e=w0(this._rgb);return e&&(e.a=qn(e.a)),e}set rgb(e){this._rgb=Op(e)}rgbString(){return this._valid?ES(this._rgb):void 0}hexString(){return this._valid?pS(this._rgb):void 0}hslString(){return this._valid?TS(this._rgb):void 0}mix(e,s){if(e){const a=this.rgb,o=e.rgb;let c;const u=s===c?.5:s,h=2*u-1,m=a.a-o.a,g=((h*m===-1?h:(h+m)/(1+h*m))+1)/2;c=1-g,a.r=255&g*a.r+c*o.r+.5,a.g=255&g*a.g+c*o.g+.5,a.b=255&g*a.b+c*o.b+.5,a.a=u*a.a+(1-u)*o.a,this.rgb=a}return this}interpolate(e,s){return e&&(this._rgb=zS(this._rgb,e._rgb,s)),this}clone(){return new xl(this.rgb)}alpha(e){return this._rgb.a=gi(e),this}clearer(e){const s=this._rgb;return s.a*=1-e,this}greyscale(){const e=this._rgb,s=El(e.r*.3+e.g*.59+e.b*.11);return e.r=e.g=e.b=s,this}opaquer(e){const s=this._rgb;return s.a*=1+e,this}negate(){const e=this._rgb;return e.r=255-e.r,e.g=255-e.g,e.b=255-e.b,this}lighten(e){return tr(this._rgb,2,e),this}darken(e){return tr(this._rgb,2,-e),this}saturate(e){return tr(this._rgb,1,e),this}desaturate(e){return tr(this._rgb,1,-e),this}rotate(e){return wS(this._rgb,e),this}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function Ln(){}const RS=(()=>{let i=0;return()=>i++})();function wt(i){return i==null}function te(i){if(Array.isArray&&Array.isArray(i))return!0;const e=Object.prototype.toString.call(i);return e.slice(0,7)==="[object"&&e.slice(-6)==="Array]"}function vt(i){return i!==null&&Object.prototype.toString.call(i)==="[object Object]"}function tn(i){return(typeof i=="number"||i instanceof Number)&&isFinite(+i)}function pn(i,e){return tn(i)?i:e}function mt(i,e){return typeof i>"u"?e:i}const kS=(i,e)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100*e:+i;function Lt(i,e,s){if(i&&typeof i.call=="function")return i.apply(s,e)}function Dt(i,e,s,a){let o,c,u;if(te(i))for(c=i.length,o=0;o<c;o++)e.call(s,i[o],o);else if(vt(i))for(u=Object.keys(i),c=u.length,o=0;o<c;o++)e.call(s,i[u[o]],u[o])}function yr(i,e){let s,a,o,c;if(!i||!e||i.length!==e.length)return!1;for(s=0,a=i.length;s<a;++s)if(o=i[s],c=e[s],o.datasetIndex!==c.datasetIndex||o.index!==c.index)return!1;return!0}function vr(i){if(te(i))return i.map(vr);if(vt(i)){const e=Object.create(null),s=Object.keys(i),a=s.length;let o=0;for(;o<a;++o)e[s[o]]=vr(i[s[o]]);return e}return i}function T0(i){return["__proto__","prototype","constructor"].indexOf(i)===-1}function BS(i,e,s,a){if(!T0(i))return;const o=e[i],c=s[i];vt(o)&&vt(c)?yl(o,c,a):e[i]=vr(c)}function yl(i,e,s){const a=te(e)?e:[e],o=a.length;if(!vt(i))return i;s=s||{};const c=s.merger||BS;let u;for(let h=0;h<o;++h){if(u=a[h],!vt(u))continue;const m=Object.keys(u);for(let g=0,p=m.length;g<p;++g)c(m[g],i,u,s)}return i}function rl(i,e){return yl(i,e,{merger:NS})}function NS(i,e,s){if(!T0(i))return;const a=e[i],o=s[i];vt(a)&&vt(o)?rl(a,o):Object.prototype.hasOwnProperty.call(e,i)||(e[i]=vr(o))}const Ep={"":i=>i,x:i=>i.x,y:i=>i.y};function LS(i){const e=i.split("."),s=[];let a="";for(const o of e)a+=o,a.endsWith("\\")?a=a.slice(0,-1)+".":(s.push(a),a="");return s}function HS(i){const e=LS(i);return s=>{for(const a of e){if(a==="")break;s=s&&s[a]}return s}}function Xs(i,e){return(Ep[e]||(Ep[e]=HS(e)))(i)}function Xf(i){return i.charAt(0).toUpperCase()+i.slice(1)}const vl=i=>typeof i<"u",pi=i=>typeof i=="function",zp=(i,e)=>{if(i.size!==e.size)return!1;for(const s of i)if(!e.has(s))return!1;return!0};function US(i){return i.type==="mouseup"||i.type==="click"||i.type==="contextmenu"}const ae=Math.PI,vn=2*ae,qS=vn+ae,_r=Number.POSITIVE_INFINITY,VS=ae/180,ln=ae/2,Ni=ae/4,Cp=ae*2/3,A0=Math.log10,_n=Math.sign;function cl(i,e,s){return Math.abs(i-e)<s}function Rp(i){const e=Math.round(i);i=cl(i,e,i/1e3)?e:i;const s=Math.pow(10,Math.floor(A0(i))),a=i/s;return(a<=1?1:a<=2?2:a<=5?5:10)*s}function FS(i){const e=[],s=Math.sqrt(i);let a;for(a=1;a<s;a++)i%a===0&&(e.push(a),e.push(i/a));return s===(s|0)&&e.push(s),e.sort((o,c)=>o-c).pop(),e}function YS(i){return typeof i=="symbol"||typeof i=="object"&&i!==null&&!(Symbol.toPrimitive in i||"toString"in i||"valueOf"in i)}function _l(i){return!YS(i)&&!isNaN(parseFloat(i))&&isFinite(i)}function XS(i,e){const s=Math.round(i);return s-e<=i&&s+e>=i}function GS(i,e,s){let a,o,c;for(a=0,o=i.length;a<o;a++)c=i[a][s],isNaN(c)||(e.min=Math.min(e.min,c),e.max=Math.max(e.max,c))}function Fi(i){return i*(ae/180)}function QS(i){return i*(180/ae)}function kp(i){if(!tn(i))return;let e=1,s=0;for(;Math.round(i*e)/e!==i;)e*=10,s++;return s}function ZS(i,e){const s=e.x-i.x,a=e.y-i.y,o=Math.sqrt(s*s+a*a);let c=Math.atan2(a,s);return c<-.5*ae&&(c+=vn),{angle:c,distance:o}}function Af(i,e){return Math.sqrt(Math.pow(e.x-i.x,2)+Math.pow(e.y-i.y,2))}function KS(i,e){return(i-e+qS)%vn-ae}function hi(i){return(i%vn+vn)%vn}function j0(i,e,s,a){const o=hi(i),c=hi(e),u=hi(s),h=hi(c-o),m=hi(u-o),g=hi(o-c),p=hi(o-u);return o===c||o===u||a&&c===u||h>m&&g<p}function He(i,e,s){return Math.max(e,Math.min(s,i))}function WS(i){return He(i,-32768,32767)}function Yi(i,e,s,a=1e-6){return i>=Math.min(e,s)-a&&i<=Math.max(e,s)+a}function Gf(i,e,s){s=s||(u=>i[u]<e);let a=i.length-1,o=0,c;for(;a-o>1;)c=o+a>>1,s(c)?o=c:a=c;return{lo:o,hi:a}}const Xi=(i,e,s,a)=>Gf(i,s,a?o=>{const c=i[o][e];return c<s||c===s&&i[o+1][e]===s}:o=>i[o][e]<s),PS=(i,e,s)=>Gf(i,s,a=>i[a][e]>=s);function JS(i,e,s){let a=0,o=i.length;for(;a<o&&i[a]<e;)a++;for(;o>a&&i[o-1]>s;)o--;return a>0||o<i.length?i.slice(a,o):i}const D0=["push","pop","shift","splice","unshift"];function IS(i,e){if(i._chartjs){i._chartjs.listeners.push(e);return}Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),D0.forEach(s=>{const a="_onData"+Xf(s),o=i[s];Object.defineProperty(i,s,{configurable:!0,enumerable:!1,value(...c){const u=o.apply(this,c);return i._chartjs.listeners.forEach(h=>{typeof h[a]=="function"&&h[a](...c)}),u}})})}function Bp(i,e){const s=i._chartjs;if(!s)return;const a=s.listeners,o=a.indexOf(e);o!==-1&&a.splice(o,1),!(a.length>0)&&(D0.forEach(c=>{delete i[c]}),delete i._chartjs)}function O0(i){const e=new Set(i);return e.size===i.length?i:Array.from(e)}const E0=(function(){return typeof window>"u"?function(i){return i()}:window.requestAnimationFrame})();function z0(i,e){let s=[],a=!1;return function(...o){s=o,a||(a=!0,E0.call(window,()=>{a=!1,i.apply(e,s)}))}}function $S(i,e){let s;return function(...a){return e?(clearTimeout(s),s=setTimeout(i,e,a)):i.apply(this,a),e}}const Qf=i=>i==="start"?"left":i==="end"?"right":"center",de=(i,e,s)=>i==="start"?e:i==="end"?s:(e+s)/2,t2=(i,e,s,a)=>i===(a?"left":"right")?s:i==="center"?(e+s)/2:e;function e2(i,e,s){const a=e.length;let o=0,c=a;if(i._sorted){const{iScale:u,vScale:h,_parsed:m}=i,g=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null,p=u.axis,{min:x,max:v,minDefined:S,maxDefined:M}=u.getUserBounds();if(S){if(o=Math.min(Xi(m,p,x).lo,s?a:Xi(e,p,u.getPixelForValue(x)).lo),g){const T=m.slice(0,o+1).reverse().findIndex(w=>!wt(w[h.axis]));o-=Math.max(0,T)}o=He(o,0,a-1)}if(M){let T=Math.max(Xi(m,u.axis,v,!0).hi+1,s?0:Xi(e,p,u.getPixelForValue(v),!0).hi+1);if(g){const w=m.slice(T-1).findIndex(O=>!wt(O[h.axis]));T+=Math.max(0,w)}c=He(T,o,a)-o}else c=a-o}return{start:o,count:c}}function n2(i){const{xScale:e,yScale:s,_scaleRanges:a}=i,o={xmin:e.min,xmax:e.max,ymin:s.min,ymax:s.max};if(!a)return i._scaleRanges=o,!0;const c=a.xmin!==e.min||a.xmax!==e.max||a.ymin!==s.min||a.ymax!==s.max;return Object.assign(a,o),c}const er=i=>i===0||i===1,Np=(i,e,s)=>-(Math.pow(2,10*(i-=1))*Math.sin((i-e)*vn/s)),Lp=(i,e,s)=>Math.pow(2,-10*i)*Math.sin((i-e)*vn/s)+1,ul={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>-Math.cos(i*ln)+1,easeOutSine:i=>Math.sin(i*ln),easeInOutSine:i=>-.5*(Math.cos(ae*i)-1),easeInExpo:i=>i===0?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>i===1?1:-Math.pow(2,-10*i)+1,easeInOutExpo:i=>er(i)?i:i<.5?.5*Math.pow(2,10*(i*2-1)):.5*(-Math.pow(2,-10*(i*2-1))+2),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>er(i)?i:Np(i,.075,.3),easeOutElastic:i=>er(i)?i:Lp(i,.075,.3),easeInOutElastic(i){return er(i)?i:i<.5?.5*Np(i*2,.1125,.45):.5+.5*Lp(i*2-1,.1125,.45)},easeInBack(i){return i*i*((1.70158+1)*i-1.70158)},easeOutBack(i){return(i-=1)*i*((1.70158+1)*i********)+1},easeInOutBack(i){let e=1.70158;return(i/=.5)<1?.5*(i*i*(((e*=1.525)+1)*i-e)):.5*((i-=2)*i*(((e*=1.525)+1)*i+e)+2)},easeInBounce:i=>1-ul.easeOutBounce(1-i),easeOutBounce(i){return i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375},easeInOutBounce:i=>i<.5?ul.easeInBounce(i*2)*.5:ul.easeOutBounce(i*2-1)*.5+.5};function Zf(i){if(i&&typeof i=="object"){const e=i.toString();return e==="[object CanvasPattern]"||e==="[object CanvasGradient]"}return!1}function Hp(i){return Zf(i)?i:new xl(i)}function of(i){return Zf(i)?i:new xl(i).saturate(.5).darken(.1).hexString()}const i2=["x","y","borderWidth","radius","tension"],s2=["color","borderColor","backgroundColor"];function a2(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:e=>e!=="onProgress"&&e!=="onComplete"&&e!=="fn"}),i.set("animations",{colors:{type:"color",properties:s2},numbers:{type:"number",properties:i2}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:e=>e|0}}}})}function l2(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const Up=new Map;function o2(i,e){e=e||{};const s=i+JSON.stringify(e);let a=Up.get(s);return a||(a=new Intl.NumberFormat(i,e),Up.set(s,a)),a}function C0(i,e,s){return o2(e,s).format(i)}const r2={values(i){return te(i)?i:""+i},numeric(i,e,s){if(i===0)return"0";const a=this.chart.options.locale;let o,c=i;if(s.length>1){const g=Math.max(Math.abs(s[0].value),Math.abs(s[s.length-1].value));(g<1e-4||g>1e15)&&(o="scientific"),c=c2(i,s)}const u=A0(Math.abs(c)),h=isNaN(u)?1:Math.max(Math.min(-1*Math.floor(u),20),0),m={notation:o,minimumFractionDigits:h,maximumFractionDigits:h};return Object.assign(m,this.options.ticks.format),C0(i,a,m)}};function c2(i,e){let s=e.length>3?e[2].value-e[1].value:e[1].value-e[0].value;return Math.abs(s)>=1&&i!==Math.floor(i)&&(s=i-Math.floor(i)),s}var R0={formatters:r2};function u2(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(e,s)=>s.lineWidth,tickColor:(e,s)=>s.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:R0.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:e=>!e.startsWith("before")&&!e.startsWith("after")&&e!=="callback"&&e!=="parser",_indexable:e=>e!=="borderDash"&&e!=="tickBorderDash"&&e!=="dash"}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:e=>e!=="backdropPadding"&&e!=="callback",_indexable:e=>e!=="backdropPadding"})}const Ki=Object.create(null),jf=Object.create(null);function fl(i,e){if(!e)return i;const s=e.split(".");for(let a=0,o=s.length;a<o;++a){const c=s[a];i=i[c]||(i[c]=Object.create(null))}return i}function rf(i,e,s){return typeof e=="string"?yl(fl(i,e),s):yl(fl(i,""),e)}class f2{constructor(e,s){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=a=>a.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(a,o)=>of(o.backgroundColor),this.hoverBorderColor=(a,o)=>of(o.borderColor),this.hoverColor=(a,o)=>of(o.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(e),this.apply(s)}set(e,s){return rf(this,e,s)}get(e){return fl(this,e)}describe(e,s){return rf(jf,e,s)}override(e,s){return rf(Ki,e,s)}route(e,s,a,o){const c=fl(this,e),u=fl(this,a),h="_"+s;Object.defineProperties(c,{[h]:{value:c[s],writable:!0},[s]:{enumerable:!0,get(){const m=this[h],g=u[o];return vt(m)?Object.assign({},g,m):mt(m,g)},set(m){this[h]=m}}})}apply(e){e.forEach(s=>s(this))}}var Zt=new f2({_scriptable:i=>!i.startsWith("on"),_indexable:i=>i!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[a2,l2,u2]);function h2(i){return!i||wt(i.size)||wt(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}function qp(i,e,s,a,o){let c=e[o];return c||(c=e[o]=i.measureText(o).width,s.push(o)),c>a&&(a=c),a}function Li(i,e,s){const a=i.currentDevicePixelRatio,o=s!==0?Math.max(s/2,.5):0;return Math.round((e-o)*a)/a+o}function Vp(i,e){!e&&!i||(e=e||i.getContext("2d"),e.save(),e.resetTransform(),e.clearRect(0,0,i.width,i.height),e.restore())}function Df(i,e,s,a){k0(i,e,s,a,null)}function k0(i,e,s,a,o){let c,u,h,m,g,p,x,v;const S=e.pointStyle,M=e.rotation,T=e.radius;let w=(M||0)*VS;if(S&&typeof S=="object"&&(c=S.toString(),c==="[object HTMLImageElement]"||c==="[object HTMLCanvasElement]")){i.save(),i.translate(s,a),i.rotate(w),i.drawImage(S,-S.width/2,-S.height/2,S.width,S.height),i.restore();return}if(!(isNaN(T)||T<=0)){switch(i.beginPath(),S){default:o?i.ellipse(s,a,o/2,T,0,0,vn):i.arc(s,a,T,0,vn),i.closePath();break;case"triangle":p=o?o/2:T,i.moveTo(s+Math.sin(w)*p,a-Math.cos(w)*T),w+=Cp,i.lineTo(s+Math.sin(w)*p,a-Math.cos(w)*T),w+=Cp,i.lineTo(s+Math.sin(w)*p,a-Math.cos(w)*T),i.closePath();break;case"rectRounded":g=T*.516,m=T-g,u=Math.cos(w+Ni)*m,x=Math.cos(w+Ni)*(o?o/2-g:m),h=Math.sin(w+Ni)*m,v=Math.sin(w+Ni)*(o?o/2-g:m),i.arc(s-x,a-h,g,w-ae,w-ln),i.arc(s+v,a-u,g,w-ln,w),i.arc(s+x,a+h,g,w,w+ln),i.arc(s-v,a+u,g,w+ln,w+ae),i.closePath();break;case"rect":if(!M){m=Math.SQRT1_2*T,p=o?o/2:m,i.rect(s-p,a-m,2*p,2*m);break}w+=Ni;case"rectRot":x=Math.cos(w)*(o?o/2:T),u=Math.cos(w)*T,h=Math.sin(w)*T,v=Math.sin(w)*(o?o/2:T),i.moveTo(s-x,a-h),i.lineTo(s+v,a-u),i.lineTo(s+x,a+h),i.lineTo(s-v,a+u),i.closePath();break;case"crossRot":w+=Ni;case"cross":x=Math.cos(w)*(o?o/2:T),u=Math.cos(w)*T,h=Math.sin(w)*T,v=Math.sin(w)*(o?o/2:T),i.moveTo(s-x,a-h),i.lineTo(s+x,a+h),i.moveTo(s+v,a-u),i.lineTo(s-v,a+u);break;case"star":x=Math.cos(w)*(o?o/2:T),u=Math.cos(w)*T,h=Math.sin(w)*T,v=Math.sin(w)*(o?o/2:T),i.moveTo(s-x,a-h),i.lineTo(s+x,a+h),i.moveTo(s+v,a-u),i.lineTo(s-v,a+u),w+=Ni,x=Math.cos(w)*(o?o/2:T),u=Math.cos(w)*T,h=Math.sin(w)*T,v=Math.sin(w)*(o?o/2:T),i.moveTo(s-x,a-h),i.lineTo(s+x,a+h),i.moveTo(s+v,a-u),i.lineTo(s-v,a+u);break;case"line":u=o?o/2:Math.cos(w)*T,h=Math.sin(w)*T,i.moveTo(s-u,a-h),i.lineTo(s+u,a+h);break;case"dash":i.moveTo(s,a),i.lineTo(s+Math.cos(w)*(o?o/2:T),a+Math.sin(w)*T);break;case!1:i.closePath();break}i.fill(),e.borderWidth>0&&i.stroke()}}function Sl(i,e,s){return s=s||.5,!e||i&&i.x>e.left-s&&i.x<e.right+s&&i.y>e.top-s&&i.y<e.bottom+s}function Kf(i,e){i.save(),i.beginPath(),i.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),i.clip()}function Wf(i){i.restore()}function d2(i,e,s,a,o){if(!e)return i.lineTo(s.x,s.y);if(o==="middle"){const c=(e.x+s.x)/2;i.lineTo(c,e.y),i.lineTo(c,s.y)}else o==="after"!=!!a?i.lineTo(e.x,s.y):i.lineTo(s.x,e.y);i.lineTo(s.x,s.y)}function m2(i,e,s,a){if(!e)return i.lineTo(s.x,s.y);i.bezierCurveTo(a?e.cp1x:e.cp2x,a?e.cp1y:e.cp2y,a?s.cp2x:s.cp1x,a?s.cp2y:s.cp1y,s.x,s.y)}function g2(i,e){e.translation&&i.translate(e.translation[0],e.translation[1]),wt(e.rotation)||i.rotate(e.rotation),e.color&&(i.fillStyle=e.color),e.textAlign&&(i.textAlign=e.textAlign),e.textBaseline&&(i.textBaseline=e.textBaseline)}function p2(i,e,s,a,o){if(o.strikethrough||o.underline){const c=i.measureText(a),u=e-c.actualBoundingBoxLeft,h=e+c.actualBoundingBoxRight,m=s-c.actualBoundingBoxAscent,g=s+c.actualBoundingBoxDescent,p=o.strikethrough?(m+g)/2:g;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=o.decorationWidth||2,i.moveTo(u,p),i.lineTo(h,p),i.stroke()}}function b2(i,e){const s=i.fillStyle;i.fillStyle=e.color,i.fillRect(e.left,e.top,e.width,e.height),i.fillStyle=s}function Ml(i,e,s,a,o,c={}){const u=te(e)?e:[e],h=c.strokeWidth>0&&c.strokeColor!=="";let m,g;for(i.save(),i.font=o.string,g2(i,c),m=0;m<u.length;++m)g=u[m],c.backdrop&&b2(i,c.backdrop),h&&(c.strokeColor&&(i.strokeStyle=c.strokeColor),wt(c.strokeWidth)||(i.lineWidth=c.strokeWidth),i.strokeText(g,s,a,c.maxWidth)),i.fillText(g,s,a,c.maxWidth),p2(i,s,a,g,c),a+=Number(o.lineHeight);i.restore()}function Sr(i,e){const{x:s,y:a,w:o,h:c,radius:u}=e;i.arc(s+u.topLeft,a+u.topLeft,u.topLeft,1.5*ae,ae,!0),i.lineTo(s,a+c-u.bottomLeft),i.arc(s+u.bottomLeft,a+c-u.bottomLeft,u.bottomLeft,ae,ln,!0),i.lineTo(s+o-u.bottomRight,a+c),i.arc(s+o-u.bottomRight,a+c-u.bottomRight,u.bottomRight,ln,0,!0),i.lineTo(s+o,a+u.topRight),i.arc(s+o-u.topRight,a+u.topRight,u.topRight,0,-ln,!0),i.lineTo(s+u.topLeft,a)}const x2=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,y2=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function v2(i,e){const s=(""+i).match(x2);if(!s||s[1]==="normal")return e*1.2;switch(i=+s[2],s[3]){case"px":return i;case"%":i/=100;break}return e*i}const _2=i=>+i||0;function B0(i,e){const s={},a=vt(e),o=a?Object.keys(e):e,c=vt(i)?a?u=>mt(i[u],i[e[u]]):u=>i[u]:()=>i;for(const u of o)s[u]=_2(c(u));return s}function N0(i){return B0(i,{top:"y",right:"x",bottom:"y",left:"x"})}function Us(i){return B0(i,["topLeft","topRight","bottomLeft","bottomRight"])}function en(i){const e=N0(i);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function be(i,e){i=i||{},e=e||Zt.font;let s=mt(i.size,e.size);typeof s=="string"&&(s=parseInt(s,10));let a=mt(i.style,e.style);a&&!(""+a).match(y2)&&(console.warn('Invalid font style specified: "'+a+'"'),a=void 0);const o={family:mt(i.family,e.family),lineHeight:v2(mt(i.lineHeight,e.lineHeight),s),size:s,style:a,weight:mt(i.weight,e.weight),string:""};return o.string=h2(o),o}function nr(i,e,s,a){let o,c,u;for(o=0,c=i.length;o<c;++o)if(u=i[o],u!==void 0&&u!==void 0)return u}function S2(i,e,s){const{min:a,max:o}=i,c=kS(e,(o-a)/2),u=(h,m)=>s&&h===0?0:h+m;return{min:u(a,-Math.abs(c)),max:u(o,c)}}function Wi(i,e){return Object.assign(Object.create(i),e)}function Pf(i,e=[""],s,a,o=()=>i[0]){const c=s||i;typeof a>"u"&&(a=q0("_fallback",i));const u={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:c,_fallback:a,_getTarget:o,override:h=>Pf([h,...i],e,c,a)};return new Proxy(u,{deleteProperty(h,m){return delete h[m],delete h._keys,delete i[0][m],!0},get(h,m){return H0(h,m,()=>E2(m,e,i,h))},getOwnPropertyDescriptor(h,m){return Reflect.getOwnPropertyDescriptor(h._scopes[0],m)},getPrototypeOf(){return Reflect.getPrototypeOf(i[0])},has(h,m){return Yp(h).includes(m)},ownKeys(h){return Yp(h)},set(h,m,g){const p=h._storage||(h._storage=o());return h[m]=p[m]=g,delete h._keys,!0}})}function Gs(i,e,s,a){const o={_cacheable:!1,_proxy:i,_context:e,_subProxy:s,_stack:new Set,_descriptors:L0(i,a),setContext:c=>Gs(i,c,s,a),override:c=>Gs(i.override(c),e,s,a)};return new Proxy(o,{deleteProperty(c,u){return delete c[u],delete i[u],!0},get(c,u,h){return H0(c,u,()=>w2(c,u,h))},getOwnPropertyDescriptor(c,u){return c._descriptors.allKeys?Reflect.has(i,u)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,u)},getPrototypeOf(){return Reflect.getPrototypeOf(i)},has(c,u){return Reflect.has(i,u)},ownKeys(){return Reflect.ownKeys(i)},set(c,u,h){return i[u]=h,delete c[u],!0}})}function L0(i,e={scriptable:!0,indexable:!0}){const{_scriptable:s=e.scriptable,_indexable:a=e.indexable,_allKeys:o=e.allKeys}=i;return{allKeys:o,scriptable:s,indexable:a,isScriptable:pi(s)?s:()=>s,isIndexable:pi(a)?a:()=>a}}const M2=(i,e)=>i?i+Xf(e):e,Jf=(i,e)=>vt(e)&&i!=="adapters"&&(Object.getPrototypeOf(e)===null||e.constructor===Object);function H0(i,e,s){if(Object.prototype.hasOwnProperty.call(i,e)||e==="constructor")return i[e];const a=s();return i[e]=a,a}function w2(i,e,s){const{_proxy:a,_context:o,_subProxy:c,_descriptors:u}=i;let h=a[e];return pi(h)&&u.isScriptable(e)&&(h=T2(e,h,i,s)),te(h)&&h.length&&(h=A2(e,h,i,u.isIndexable)),Jf(e,h)&&(h=Gs(h,o,c&&c[e],u)),h}function T2(i,e,s,a){const{_proxy:o,_context:c,_subProxy:u,_stack:h}=s;if(h.has(i))throw new Error("Recursion detected: "+Array.from(h).join("->")+"->"+i);h.add(i);let m=e(c,u||a);return h.delete(i),Jf(i,m)&&(m=If(o._scopes,o,i,m)),m}function A2(i,e,s,a){const{_proxy:o,_context:c,_subProxy:u,_descriptors:h}=s;if(typeof c.index<"u"&&a(i))return e[c.index%e.length];if(vt(e[0])){const m=e,g=o._scopes.filter(p=>p!==m);e=[];for(const p of m){const x=If(g,o,i,p);e.push(Gs(x,c,u&&u[i],h))}}return e}function U0(i,e,s){return pi(i)?i(e,s):i}const j2=(i,e)=>i===!0?e:typeof i=="string"?Xs(e,i):void 0;function D2(i,e,s,a,o){for(const c of e){const u=j2(s,c);if(u){i.add(u);const h=U0(u._fallback,s,o);if(typeof h<"u"&&h!==s&&h!==a)return h}else if(u===!1&&typeof a<"u"&&s!==a)return null}return!1}function If(i,e,s,a){const o=e._rootScopes,c=U0(e._fallback,s,a),u=[...i,...o],h=new Set;h.add(a);let m=Fp(h,u,s,c||s,a);return m===null||typeof c<"u"&&c!==s&&(m=Fp(h,u,c,m,a),m===null)?!1:Pf(Array.from(h),[""],o,c,()=>O2(e,s,a))}function Fp(i,e,s,a,o){for(;s;)s=D2(i,e,s,a,o);return s}function O2(i,e,s){const a=i._getTarget();e in a||(a[e]={});const o=a[e];return te(o)&&vt(s)?s:o||{}}function E2(i,e,s,a){let o;for(const c of e)if(o=q0(M2(c,i),s),typeof o<"u")return Jf(i,o)?If(s,a,i,o):o}function q0(i,e){for(const s of e){if(!s)continue;const a=s[i];if(typeof a<"u")return a}}function Yp(i){let e=i._keys;return e||(e=i._keys=z2(i._scopes)),e}function z2(i){const e=new Set;for(const s of i)for(const a of Object.keys(s).filter(o=>!o.startsWith("_")))e.add(a);return Array.from(e)}const C2=Number.EPSILON||1e-14,Qs=(i,e)=>e<i.length&&!i[e].skip&&i[e],V0=i=>i==="x"?"y":"x";function R2(i,e,s,a){const o=i.skip?e:i,c=e,u=s.skip?e:s,h=Af(c,o),m=Af(u,c);let g=h/(h+m),p=m/(h+m);g=isNaN(g)?0:g,p=isNaN(p)?0:p;const x=a*g,v=a*p;return{previous:{x:c.x-x*(u.x-o.x),y:c.y-x*(u.y-o.y)},next:{x:c.x+v*(u.x-o.x),y:c.y+v*(u.y-o.y)}}}function k2(i,e,s){const a=i.length;let o,c,u,h,m,g=Qs(i,0);for(let p=0;p<a-1;++p)if(m=g,g=Qs(i,p+1),!(!m||!g)){if(cl(e[p],0,C2)){s[p]=s[p+1]=0;continue}o=s[p]/e[p],c=s[p+1]/e[p],h=Math.pow(o,2)+Math.pow(c,2),!(h<=9)&&(u=3/Math.sqrt(h),s[p]=o*u*e[p],s[p+1]=c*u*e[p])}}function B2(i,e,s="x"){const a=V0(s),o=i.length;let c,u,h,m=Qs(i,0);for(let g=0;g<o;++g){if(u=h,h=m,m=Qs(i,g+1),!h)continue;const p=h[s],x=h[a];u&&(c=(p-u[s])/3,h[`cp1${s}`]=p-c,h[`cp1${a}`]=x-c*e[g]),m&&(c=(m[s]-p)/3,h[`cp2${s}`]=p+c,h[`cp2${a}`]=x+c*e[g])}}function N2(i,e="x"){const s=V0(e),a=i.length,o=Array(a).fill(0),c=Array(a);let u,h,m,g=Qs(i,0);for(u=0;u<a;++u)if(h=m,m=g,g=Qs(i,u+1),!!m){if(g){const p=g[e]-m[e];o[u]=p!==0?(g[s]-m[s])/p:0}c[u]=h?g?_n(o[u-1])!==_n(o[u])?0:(o[u-1]+o[u])/2:o[u-1]:o[u]}k2(i,o,c),B2(i,c,e)}function ir(i,e,s){return Math.max(Math.min(i,s),e)}function L2(i,e){let s,a,o,c,u,h=Sl(i[0],e);for(s=0,a=i.length;s<a;++s)u=c,c=h,h=s<a-1&&Sl(i[s+1],e),c&&(o=i[s],u&&(o.cp1x=ir(o.cp1x,e.left,e.right),o.cp1y=ir(o.cp1y,e.top,e.bottom)),h&&(o.cp2x=ir(o.cp2x,e.left,e.right),o.cp2y=ir(o.cp2y,e.top,e.bottom)))}function H2(i,e,s,a,o){let c,u,h,m;if(e.spanGaps&&(i=i.filter(g=>!g.skip)),e.cubicInterpolationMode==="monotone")N2(i,o);else{let g=a?i[i.length-1]:i[0];for(c=0,u=i.length;c<u;++c)h=i[c],m=R2(g,h,i[Math.min(c+1,u-(a?0:1))%u],e.tension),h.cp1x=m.previous.x,h.cp1y=m.previous.y,h.cp2x=m.next.x,h.cp2y=m.next.y,g=h}e.capBezierPoints&&L2(i,s)}function $f(){return typeof window<"u"&&typeof document<"u"}function th(i){let e=i.parentNode;return e&&e.toString()==="[object ShadowRoot]"&&(e=e.host),e}function Mr(i,e,s){let a;return typeof i=="string"?(a=parseInt(i,10),i.indexOf("%")!==-1&&(a=a/100*e.parentNode[s])):a=i,a}const zr=i=>i.ownerDocument.defaultView.getComputedStyle(i,null);function U2(i,e){return zr(i).getPropertyValue(e)}const q2=["top","right","bottom","left"];function Qi(i,e,s){const a={};s=s?"-"+s:"";for(let o=0;o<4;o++){const c=q2[o];a[c]=parseFloat(i[e+"-"+c+s])||0}return a.width=a.left+a.right,a.height=a.top+a.bottom,a}const V2=(i,e,s)=>(i>0||e>0)&&(!s||!s.shadowRoot);function F2(i,e){const s=i.touches,a=s&&s.length?s[0]:i,{offsetX:o,offsetY:c}=a;let u=!1,h,m;if(V2(o,c,i.target))h=o,m=c;else{const g=e.getBoundingClientRect();h=a.clientX-g.left,m=a.clientY-g.top,u=!0}return{x:h,y:m,box:u}}function Ui(i,e){if("native"in i)return i;const{canvas:s,currentDevicePixelRatio:a}=e,o=zr(s),c=o.boxSizing==="border-box",u=Qi(o,"padding"),h=Qi(o,"border","width"),{x:m,y:g,box:p}=F2(i,s),x=u.left+(p&&h.left),v=u.top+(p&&h.top);let{width:S,height:M}=e;return c&&(S-=u.width+h.width,M-=u.height+h.height),{x:Math.round((m-x)/S*s.width/a),y:Math.round((g-v)/M*s.height/a)}}function Y2(i,e,s){let a,o;if(e===void 0||s===void 0){const c=i&&th(i);if(!c)e=i.clientWidth,s=i.clientHeight;else{const u=c.getBoundingClientRect(),h=zr(c),m=Qi(h,"border","width"),g=Qi(h,"padding");e=u.width-g.width-m.width,s=u.height-g.height-m.height,a=Mr(h.maxWidth,c,"clientWidth"),o=Mr(h.maxHeight,c,"clientHeight")}}return{width:e,height:s,maxWidth:a||_r,maxHeight:o||_r}}const sr=i=>Math.round(i*10)/10;function X2(i,e,s,a){const o=zr(i),c=Qi(o,"margin"),u=Mr(o.maxWidth,i,"clientWidth")||_r,h=Mr(o.maxHeight,i,"clientHeight")||_r,m=Y2(i,e,s);let{width:g,height:p}=m;if(o.boxSizing==="content-box"){const v=Qi(o,"border","width"),S=Qi(o,"padding");g-=S.width+v.width,p-=S.height+v.height}return g=Math.max(0,g-c.width),p=Math.max(0,a?g/a:p-c.height),g=sr(Math.min(g,u,m.maxWidth)),p=sr(Math.min(p,h,m.maxHeight)),g&&!p&&(p=sr(g/2)),(e!==void 0||s!==void 0)&&a&&m.height&&p>m.height&&(p=m.height,g=sr(Math.floor(p*a))),{width:g,height:p}}function Xp(i,e,s){const a=e||1,o=Math.floor(i.height*a),c=Math.floor(i.width*a);i.height=Math.floor(i.height),i.width=Math.floor(i.width);const u=i.canvas;return u.style&&(s||!u.style.height&&!u.style.width)&&(u.style.height=`${i.height}px`,u.style.width=`${i.width}px`),i.currentDevicePixelRatio!==a||u.height!==o||u.width!==c?(i.currentDevicePixelRatio=a,u.height=o,u.width=c,i.ctx.setTransform(a,0,0,a,0,0),!0):!1}const G2=(function(){let i=!1;try{const e={get passive(){return i=!0,!1}};$f()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch{}return i})();function Gp(i,e){const s=U2(i,e),a=s&&s.match(/^(\d+)(\.\d+)?px$/);return a?+a[1]:void 0}function qi(i,e,s,a){return{x:i.x+s*(e.x-i.x),y:i.y+s*(e.y-i.y)}}function Q2(i,e,s,a){return{x:i.x+s*(e.x-i.x),y:a==="middle"?s<.5?i.y:e.y:a==="after"?s<1?i.y:e.y:s>0?e.y:i.y}}function Z2(i,e,s,a){const o={x:i.cp2x,y:i.cp2y},c={x:e.cp1x,y:e.cp1y},u=qi(i,o,s),h=qi(o,c,s),m=qi(c,e,s),g=qi(u,h,s),p=qi(h,m,s);return qi(g,p,s)}const K2=function(i,e){return{x(s){return i+i+e-s},setWidth(s){e=s},textAlign(s){return s==="center"?s:s==="right"?"left":"right"},xPlus(s,a){return s-a},leftForLtr(s,a){return s-a}}},W2=function(){return{x(i){return i},setWidth(i){},textAlign(i){return i},xPlus(i,e){return i+e},leftForLtr(i,e){return i}}};function qs(i,e,s){return i?K2(e,s):W2()}function F0(i,e){let s,a;(e==="ltr"||e==="rtl")&&(s=i.canvas.style,a=[s.getPropertyValue("direction"),s.getPropertyPriority("direction")],s.setProperty("direction",e,"important"),i.prevTextDirection=a)}function Y0(i,e){e!==void 0&&(delete i.prevTextDirection,i.canvas.style.setProperty("direction",e[0],e[1]))}function X0(i){return i==="angle"?{between:j0,compare:KS,normalize:hi}:{between:Yi,compare:(e,s)=>e-s,normalize:e=>e}}function Qp({start:i,end:e,count:s,loop:a,style:o}){return{start:i%s,end:e%s,loop:a&&(e-i+1)%s===0,style:o}}function P2(i,e,s){const{property:a,start:o,end:c}=s,{between:u,normalize:h}=X0(a),m=e.length;let{start:g,end:p,loop:x}=i,v,S;if(x){for(g+=m,p+=m,v=0,S=m;v<S&&u(h(e[g%m][a]),o,c);++v)g--,p--;g%=m,p%=m}return p<g&&(p+=m),{start:g,end:p,loop:x,style:i.style}}function J2(i,e,s){if(!s)return[i];const{property:a,start:o,end:c}=s,u=e.length,{compare:h,between:m,normalize:g}=X0(a),{start:p,end:x,loop:v,style:S}=P2(i,e,s),M=[];let T=!1,w=null,O,H,q;const G=()=>m(o,q,O)&&h(o,q)!==0,V=()=>h(c,O)===0||m(c,q,O),F=()=>T||G(),X=()=>!T||V();for(let Z=p,I=p;Z<=x;++Z)H=e[Z%u],!H.skip&&(O=g(H[a]),O!==q&&(T=m(O,o,c),w===null&&F()&&(w=h(O,o)===0?Z:I),w!==null&&X()&&(M.push(Qp({start:w,end:Z,loop:v,count:u,style:S})),w=null),I=Z,q=O));return w!==null&&M.push(Qp({start:w,end:x,loop:v,count:u,style:S})),M}function I2(i,e){const s=[],a=i.segments;for(let o=0;o<a.length;o++){const c=J2(a[o],i.points,e);c.length&&s.push(...c)}return s}function $2(i,e,s,a){let o=0,c=e-1;if(s&&!a)for(;o<e&&!i[o].skip;)o++;for(;o<e&&i[o].skip;)o++;for(o%=e,s&&(c+=o);c>o&&i[c%e].skip;)c--;return c%=e,{start:o,end:c}}function tM(i,e,s,a){const o=i.length,c=[];let u=e,h=i[e],m;for(m=e+1;m<=s;++m){const g=i[m%o];g.skip||g.stop?h.skip||(a=!1,c.push({start:e%o,end:(m-1)%o,loop:a}),e=u=g.stop?m:null):(u=m,h.skip&&(e=m)),h=g}return u!==null&&c.push({start:e%o,end:u%o,loop:a}),c}function eM(i,e){const s=i.points,a=i.options.spanGaps,o=s.length;if(!o)return[];const c=!!i._loop,{start:u,end:h}=$2(s,o,c,a);if(a===!0)return Zp(i,[{start:u,end:h,loop:c}],s,e);const m=h<u?h+o:h,g=!!i._fullLoop&&u===0&&h===o-1;return Zp(i,tM(s,u,m,g),s,e)}function Zp(i,e,s,a){return!a||!a.setContext||!s?e:nM(i,e,s,a)}function nM(i,e,s,a){const o=i._chart.getContext(),c=Kp(i.options),{_datasetIndex:u,options:{spanGaps:h}}=i,m=s.length,g=[];let p=c,x=e[0].start,v=x;function S(M,T,w,O){const H=h?-1:1;if(M!==T){for(M+=m;s[M%m].skip;)M-=H;for(;s[T%m].skip;)T+=H;M%m!==T%m&&(g.push({start:M%m,end:T%m,loop:w,style:O}),p=O,x=T%m)}}for(const M of e){x=h?x:M.start;let T=s[x%m],w;for(v=x+1;v<=M.end;v++){const O=s[v%m];w=Kp(a.setContext(Wi(o,{type:"segment",p0:T,p1:O,p0DataIndex:(v-1)%m,p1DataIndex:v%m,datasetIndex:u}))),iM(w,p)&&S(x,v-1,M.loop,p),T=O,p=w}x<v-1&&S(x,v-1,M.loop,p)}return g}function Kp(i){return{backgroundColor:i.backgroundColor,borderCapStyle:i.borderCapStyle,borderDash:i.borderDash,borderDashOffset:i.borderDashOffset,borderJoinStyle:i.borderJoinStyle,borderWidth:i.borderWidth,borderColor:i.borderColor}}function iM(i,e){if(!e)return!1;const s=[],a=function(o,c){return Zf(c)?(s.includes(c)||s.push(c),s.indexOf(c)):c};return JSON.stringify(i,a)!==JSON.stringify(e,a)}function ar(i,e,s){return i.options.clip?i[s]:e[s]}function sM(i,e){const{xScale:s,yScale:a}=i;return s&&a?{left:ar(s,e,"left"),right:ar(s,e,"right"),top:ar(a,e,"top"),bottom:ar(a,e,"bottom")}:e}function aM(i,e){const s=e._clip;if(s.disabled)return!1;const a=sM(e,i.chartArea);return{left:s.left===!1?0:a.left-(s.left===!0?0:s.left),right:s.right===!1?i.width:a.right+(s.right===!0?0:s.right),top:s.top===!1?0:a.top-(s.top===!0?0:s.top),bottom:s.bottom===!1?i.height:a.bottom+(s.bottom===!0?0:s.bottom)}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class lM{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(e,s,a,o){const c=s.listeners[o],u=s.duration;c.forEach(h=>h({chart:e,initial:s.initial,numSteps:u,currentStep:Math.min(a-s.start,u)}))}_refresh(){this._request||(this._running=!0,this._request=E0.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(e=Date.now()){let s=0;this._charts.forEach((a,o)=>{if(!a.running||!a.items.length)return;const c=a.items;let u=c.length-1,h=!1,m;for(;u>=0;--u)m=c[u],m._active?(m._total>a.duration&&(a.duration=m._total),m.tick(e),h=!0):(c[u]=c[c.length-1],c.pop());h&&(o.draw(),this._notify(o,a,e,"progress")),c.length||(a.running=!1,this._notify(o,a,e,"complete"),a.initial=!1),s+=c.length}),this._lastDate=e,s===0&&(this._running=!1)}_getAnims(e){const s=this._charts;let a=s.get(e);return a||(a={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},s.set(e,a)),a}listen(e,s,a){this._getAnims(e).listeners[s].push(a)}add(e,s){!s||!s.length||this._getAnims(e).items.push(...s)}has(e){return this._getAnims(e).items.length>0}start(e){const s=this._charts.get(e);s&&(s.running=!0,s.start=Date.now(),s.duration=s.items.reduce((a,o)=>Math.max(a,o._duration),0),this._refresh())}running(e){if(!this._running)return!1;const s=this._charts.get(e);return!(!s||!s.running||!s.items.length)}stop(e){const s=this._charts.get(e);if(!s||!s.items.length)return;const a=s.items;let o=a.length-1;for(;o>=0;--o)a[o].cancel();s.items=[],this._notify(e,s,Date.now(),"complete")}remove(e){return this._charts.delete(e)}}var Hn=new lM;const Wp="transparent",oM={boolean(i,e,s){return s>.5?e:i},color(i,e,s){const a=Hp(i||Wp),o=a.valid&&Hp(e||Wp);return o&&o.valid?o.mix(a,s).hexString():e},number(i,e,s){return i+(e-i)*s}};class rM{constructor(e,s,a,o){const c=s[a];o=nr([e.to,o,c,e.from]);const u=nr([e.from,c,o]);this._active=!0,this._fn=e.fn||oM[e.type||typeof u],this._easing=ul[e.easing]||ul.linear,this._start=Math.floor(Date.now()+(e.delay||0)),this._duration=this._total=Math.floor(e.duration),this._loop=!!e.loop,this._target=s,this._prop=a,this._from=u,this._to=o,this._promises=void 0}active(){return this._active}update(e,s,a){if(this._active){this._notify(!1);const o=this._target[this._prop],c=a-this._start,u=this._duration-c;this._start=a,this._duration=Math.floor(Math.max(u,e.duration)),this._total+=c,this._loop=!!e.loop,this._to=nr([e.to,s,o,e.from]),this._from=nr([e.from,o,s])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(e){const s=e-this._start,a=this._duration,o=this._prop,c=this._from,u=this._loop,h=this._to;let m;if(this._active=c!==h&&(u||s<a),!this._active){this._target[o]=h,this._notify(!0);return}if(s<0){this._target[o]=c;return}m=s/a%2,m=u&&m>1?2-m:m,m=this._easing(Math.min(1,Math.max(0,m))),this._target[o]=this._fn(c,h,m)}wait(){const e=this._promises||(this._promises=[]);return new Promise((s,a)=>{e.push({res:s,rej:a})})}_notify(e){const s=e?"res":"rej",a=this._promises||[];for(let o=0;o<a.length;o++)a[o][s]()}}class G0{constructor(e,s){this._chart=e,this._properties=new Map,this.configure(s)}configure(e){if(!vt(e))return;const s=Object.keys(Zt.animation),a=this._properties;Object.getOwnPropertyNames(e).forEach(o=>{const c=e[o];if(!vt(c))return;const u={};for(const h of s)u[h]=c[h];(te(c.properties)&&c.properties||[o]).forEach(h=>{(h===o||!a.has(h))&&a.set(h,u)})})}_animateOptions(e,s){const a=s.options,o=uM(e,a);if(!o)return[];const c=this._createAnimations(o,a);return a.$shared&&cM(e.options.$animations,a).then(()=>{e.options=a},()=>{}),c}_createAnimations(e,s){const a=this._properties,o=[],c=e.$animations||(e.$animations={}),u=Object.keys(s),h=Date.now();let m;for(m=u.length-1;m>=0;--m){const g=u[m];if(g.charAt(0)==="$")continue;if(g==="options"){o.push(...this._animateOptions(e,s));continue}const p=s[g];let x=c[g];const v=a.get(g);if(x)if(v&&x.active()){x.update(v,p,h);continue}else x.cancel();if(!v||!v.duration){e[g]=p;continue}c[g]=x=new rM(v,e,g,p),o.push(x)}return o}update(e,s){if(this._properties.size===0){Object.assign(e,s);return}const a=this._createAnimations(e,s);if(a.length)return Hn.add(this._chart,a),!0}}function cM(i,e){const s=[],a=Object.keys(e);for(let o=0;o<a.length;o++){const c=i[a[o]];c&&c.active()&&s.push(c.wait())}return Promise.all(s)}function uM(i,e){if(!e)return;let s=i.options;if(!s){i.options=e;return}return s.$shared&&(i.options=s=Object.assign({},s,{$shared:!1,$animations:{}})),s}function Pp(i,e){const s=i&&i.options||{},a=s.reverse,o=s.min===void 0?e:0,c=s.max===void 0?e:0;return{start:a?c:o,end:a?o:c}}function fM(i,e,s){if(s===!1)return!1;const a=Pp(i,s),o=Pp(e,s);return{top:o.end,right:a.end,bottom:o.start,left:a.start}}function hM(i){let e,s,a,o;return vt(i)?(e=i.top,s=i.right,a=i.bottom,o=i.left):e=s=a=o=i,{top:e,right:s,bottom:a,left:o,disabled:i===!1}}function Q0(i,e){const s=[],a=i._getSortedDatasetMetas(e);let o,c;for(o=0,c=a.length;o<c;++o)s.push(a[o].index);return s}function Jp(i,e,s,a={}){const o=i.keys,c=a.mode==="single";let u,h,m,g;if(e===null)return;let p=!1;for(u=0,h=o.length;u<h;++u){if(m=+o[u],m===s){if(p=!0,a.all)continue;break}g=i.values[m],tn(g)&&(c||e===0||_n(e)===_n(g))&&(e+=g)}return!p&&!a.all?0:e}function dM(i,e){const{iScale:s,vScale:a}=e,o=s.axis==="x"?"x":"y",c=a.axis==="x"?"x":"y",u=Object.keys(i),h=new Array(u.length);let m,g,p;for(m=0,g=u.length;m<g;++m)p=u[m],h[m]={[o]:p,[c]:i[p]};return h}function cf(i,e){const s=i&&i.options.stacked;return s||s===void 0&&e.stack!==void 0}function mM(i,e,s){return`${i.id}.${e.id}.${s.stack||s.type}`}function gM(i){const{min:e,max:s,minDefined:a,maxDefined:o}=i.getUserBounds();return{min:a?e:Number.NEGATIVE_INFINITY,max:o?s:Number.POSITIVE_INFINITY}}function pM(i,e,s){const a=i[e]||(i[e]={});return a[s]||(a[s]={})}function Ip(i,e,s,a){for(const o of e.getMatchingVisibleMetas(a).reverse()){const c=i[o.index];if(s&&c>0||!s&&c<0)return o.index}return null}function $p(i,e){const{chart:s,_cachedMeta:a}=i,o=s._stacks||(s._stacks={}),{iScale:c,vScale:u,index:h}=a,m=c.axis,g=u.axis,p=mM(c,u,a),x=e.length;let v;for(let S=0;S<x;++S){const M=e[S],{[m]:T,[g]:w}=M,O=M._stacks||(M._stacks={});v=O[g]=pM(o,p,T),v[h]=w,v._top=Ip(v,u,!0,a.type),v._bottom=Ip(v,u,!1,a.type);const H=v._visualValues||(v._visualValues={});H[h]=w}}function uf(i,e){const s=i.scales;return Object.keys(s).filter(a=>s[a].axis===e).shift()}function bM(i,e){return Wi(i,{active:!1,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}function xM(i,e,s){return Wi(i,{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:s,index:e,mode:"default",type:"data"})}function Pa(i,e){const s=i.controller.index,a=i.vScale&&i.vScale.axis;if(a){e=e||i._parsed;for(const o of e){const c=o._stacks;if(!c||c[a]===void 0||c[a][s]===void 0)return;delete c[a][s],c[a]._visualValues!==void 0&&c[a]._visualValues[s]!==void 0&&delete c[a]._visualValues[s]}}}const ff=i=>i==="reset"||i==="none",tb=(i,e)=>e?i:Object.assign({},i),yM=(i,e,s)=>i&&!e.hidden&&e._stacked&&{keys:Q0(s,!0),values:null};class eh{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(e,s){this.chart=e,this._ctx=e.ctx,this.index=s,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const e=this._cachedMeta;this.configure(),this.linkScales(),e._stacked=cf(e.vScale,e),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(e){this.index!==e&&Pa(this._cachedMeta),this.index=e}linkScales(){const e=this.chart,s=this._cachedMeta,a=this.getDataset(),o=(x,v,S,M)=>x==="x"?v:x==="r"?M:S,c=s.xAxisID=mt(a.xAxisID,uf(e,"x")),u=s.yAxisID=mt(a.yAxisID,uf(e,"y")),h=s.rAxisID=mt(a.rAxisID,uf(e,"r")),m=s.indexAxis,g=s.iAxisID=o(m,c,u,h),p=s.vAxisID=o(m,u,c,h);s.xScale=this.getScaleForId(c),s.yScale=this.getScaleForId(u),s.rScale=this.getScaleForId(h),s.iScale=this.getScaleForId(g),s.vScale=this.getScaleForId(p)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(e){return this.chart.scales[e]}_getOtherScale(e){const s=this._cachedMeta;return e===s.iScale?s.vScale:s.iScale}reset(){this._update("reset")}_destroy(){const e=this._cachedMeta;this._data&&Bp(this._data,this),e._stacked&&Pa(e)}_dataCheck(){const e=this.getDataset(),s=e.data||(e.data=[]),a=this._data;if(vt(s)){const o=this._cachedMeta;this._data=dM(s,o)}else if(a!==s){if(a){Bp(a,this);const o=this._cachedMeta;Pa(o),o._parsed=[]}s&&Object.isExtensible(s)&&IS(s,this),this._syncList=[],this._data=s}}addElements(){const e=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(e.dataset=new this.datasetElementType)}buildOrUpdateElements(e){const s=this._cachedMeta,a=this.getDataset();let o=!1;this._dataCheck();const c=s._stacked;s._stacked=cf(s.vScale,s),s.stack!==a.stack&&(o=!0,Pa(s),s.stack=a.stack),this._resyncElements(e),(o||c!==s._stacked)&&($p(this,s._parsed),s._stacked=cf(s.vScale,s))}configure(){const e=this.chart.config,s=e.datasetScopeKeys(this._type),a=e.getOptionScopes(this.getDataset(),s,!0);this.options=e.createResolver(a,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(e,s){const{_cachedMeta:a,_data:o}=this,{iScale:c,_stacked:u}=a,h=c.axis;let m=e===0&&s===o.length?!0:a._sorted,g=e>0&&a._parsed[e-1],p,x,v;if(this._parsing===!1)a._parsed=o,a._sorted=!0,v=o;else{te(o[e])?v=this.parseArrayData(a,o,e,s):vt(o[e])?v=this.parseObjectData(a,o,e,s):v=this.parsePrimitiveData(a,o,e,s);const S=()=>x[h]===null||g&&x[h]<g[h];for(p=0;p<s;++p)a._parsed[p+e]=x=v[p],m&&(S()&&(m=!1),g=x);a._sorted=m}u&&$p(this,v)}parsePrimitiveData(e,s,a,o){const{iScale:c,vScale:u}=e,h=c.axis,m=u.axis,g=c.getLabels(),p=c===u,x=new Array(o);let v,S,M;for(v=0,S=o;v<S;++v)M=v+a,x[v]={[h]:p||c.parse(g[M],M),[m]:u.parse(s[M],M)};return x}parseArrayData(e,s,a,o){const{xScale:c,yScale:u}=e,h=new Array(o);let m,g,p,x;for(m=0,g=o;m<g;++m)p=m+a,x=s[p],h[m]={x:c.parse(x[0],p),y:u.parse(x[1],p)};return h}parseObjectData(e,s,a,o){const{xScale:c,yScale:u}=e,{xAxisKey:h="x",yAxisKey:m="y"}=this._parsing,g=new Array(o);let p,x,v,S;for(p=0,x=o;p<x;++p)v=p+a,S=s[v],g[p]={x:c.parse(Xs(S,h),v),y:u.parse(Xs(S,m),v)};return g}getParsed(e){return this._cachedMeta._parsed[e]}getDataElement(e){return this._cachedMeta.data[e]}applyStack(e,s,a){const o=this.chart,c=this._cachedMeta,u=s[e.axis],h={keys:Q0(o,!0),values:s._stacks[e.axis]._visualValues};return Jp(h,u,c.index,{mode:a})}updateRangeFromParsed(e,s,a,o){const c=a[s.axis];let u=c===null?NaN:c;const h=o&&a._stacks[s.axis];o&&h&&(o.values=h,u=Jp(o,c,this._cachedMeta.index)),e.min=Math.min(e.min,u),e.max=Math.max(e.max,u)}getMinMax(e,s){const a=this._cachedMeta,o=a._parsed,c=a._sorted&&e===a.iScale,u=o.length,h=this._getOtherScale(e),m=yM(s,a,this.chart),g={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:p,max:x}=gM(h);let v,S;function M(){S=o[v];const T=S[h.axis];return!tn(S[e.axis])||p>T||x<T}for(v=0;v<u&&!(!M()&&(this.updateRangeFromParsed(g,e,S,m),c));++v);if(c){for(v=u-1;v>=0;--v)if(!M()){this.updateRangeFromParsed(g,e,S,m);break}}return g}getAllParsedValues(e){const s=this._cachedMeta._parsed,a=[];let o,c,u;for(o=0,c=s.length;o<c;++o)u=s[o][e.axis],tn(u)&&a.push(u);return a}getMaxOverflow(){return!1}getLabelAndValue(e){const s=this._cachedMeta,a=s.iScale,o=s.vScale,c=this.getParsed(e);return{label:a?""+a.getLabelForValue(c[a.axis]):"",value:o?""+o.getLabelForValue(c[o.axis]):""}}_update(e){const s=this._cachedMeta;this.update(e||"default"),s._clip=hM(mt(this.options.clip,fM(s.xScale,s.yScale,this.getMaxOverflow())))}update(e){}draw(){const e=this._ctx,s=this.chart,a=this._cachedMeta,o=a.data||[],c=s.chartArea,u=[],h=this._drawStart||0,m=this._drawCount||o.length-h,g=this.options.drawActiveElementsOnTop;let p;for(a.dataset&&a.dataset.draw(e,c,h,m),p=h;p<h+m;++p){const x=o[p];x.hidden||(x.active&&g?u.push(x):x.draw(e,c))}for(p=0;p<u.length;++p)u[p].draw(e,c)}getStyle(e,s){const a=s?"active":"default";return e===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(a):this.resolveDataElementOptions(e||0,a)}getContext(e,s,a){const o=this.getDataset();let c;if(e>=0&&e<this._cachedMeta.data.length){const u=this._cachedMeta.data[e];c=u.$context||(u.$context=xM(this.getContext(),e,u)),c.parsed=this.getParsed(e),c.raw=o.data[e],c.index=c.dataIndex=e}else c=this.$context||(this.$context=bM(this.chart.getContext(),this.index)),c.dataset=o,c.index=c.datasetIndex=this.index;return c.active=!!s,c.mode=a,c}resolveDatasetElementOptions(e){return this._resolveElementOptions(this.datasetElementType.id,e)}resolveDataElementOptions(e,s){return this._resolveElementOptions(this.dataElementType.id,s,e)}_resolveElementOptions(e,s="default",a){const o=s==="active",c=this._cachedDataOpts,u=e+"-"+s,h=c[u],m=this.enableOptionSharing&&vl(a);if(h)return tb(h,m);const g=this.chart.config,p=g.datasetElementScopeKeys(this._type,e),x=o?[`${e}Hover`,"hover",e,""]:[e,""],v=g.getOptionScopes(this.getDataset(),p),S=Object.keys(Zt.elements[e]),M=()=>this.getContext(a,o,s),T=g.resolveNamedOptions(v,S,M,x);return T.$shared&&(T.$shared=m,c[u]=Object.freeze(tb(T,m))),T}_resolveAnimations(e,s,a){const o=this.chart,c=this._cachedDataOpts,u=`animation-${s}`,h=c[u];if(h)return h;let m;if(o.options.animation!==!1){const p=this.chart.config,x=p.datasetAnimationScopeKeys(this._type,s),v=p.getOptionScopes(this.getDataset(),x);m=p.createResolver(v,this.getContext(e,a,s))}const g=new G0(o,m&&m.animations);return m&&m._cacheable&&(c[u]=Object.freeze(g)),g}getSharedOptions(e){if(e.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},e))}includeOptions(e,s){return!s||ff(e)||this.chart._animationsDisabled}_getSharedOptions(e,s){const a=this.resolveDataElementOptions(e,s),o=this._sharedOptions,c=this.getSharedOptions(a),u=this.includeOptions(s,c)||c!==o;return this.updateSharedOptions(c,s,a),{sharedOptions:c,includeOptions:u}}updateElement(e,s,a,o){ff(o)?Object.assign(e,a):this._resolveAnimations(s,o).update(e,a)}updateSharedOptions(e,s,a){e&&!ff(s)&&this._resolveAnimations(void 0,s).update(e,a)}_setStyle(e,s,a,o){e.active=o;const c=this.getStyle(s,o);this._resolveAnimations(s,a,o).update(e,{options:!o&&this.getSharedOptions(c)||c})}removeHoverStyle(e,s,a){this._setStyle(e,a,"active",!1)}setHoverStyle(e,s,a){this._setStyle(e,a,"active",!0)}_removeDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!1)}_setDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!0)}_resyncElements(e){const s=this._data,a=this._cachedMeta.data;for(const[h,m,g]of this._syncList)this[h](m,g);this._syncList=[];const o=a.length,c=s.length,u=Math.min(c,o);u&&this.parse(0,u),c>o?this._insertElements(o,c-o,e):c<o&&this._removeElements(c,o-c)}_insertElements(e,s,a=!0){const o=this._cachedMeta,c=o.data,u=e+s;let h;const m=g=>{for(g.length+=s,h=g.length-1;h>=u;h--)g[h]=g[h-s]};for(m(c),h=e;h<u;++h)c[h]=new this.dataElementType;this._parsing&&m(o._parsed),this.parse(e,s),a&&this.updateElements(c,e,s,"reset")}updateElements(e,s,a,o){}_removeElements(e,s){const a=this._cachedMeta;if(this._parsing){const o=a._parsed.splice(e,s);a._stacked&&Pa(a,o)}a.data.splice(e,s)}_sync(e){if(this._parsing)this._syncList.push(e);else{const[s,a,o]=e;this[s](a,o)}this.chart._dataChanges.push([this.index,...e])}_onDataPush(){const e=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-e,e])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(e,s){s&&this._sync(["_removeElements",e,s]);const a=arguments.length-2;a&&this._sync(["_insertElements",e,a])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function vM(i,e){if(!i._cache.$bar){const s=i.getMatchingVisibleMetas(e);let a=[];for(let o=0,c=s.length;o<c;o++)a=a.concat(s[o].controller.getAllParsedValues(i));i._cache.$bar=O0(a.sort((o,c)=>o-c))}return i._cache.$bar}function _M(i){const e=i.iScale,s=vM(e,i.type);let a=e._length,o,c,u,h;const m=()=>{u===32767||u===-32768||(vl(h)&&(a=Math.min(a,Math.abs(u-h)||a)),h=u)};for(o=0,c=s.length;o<c;++o)u=e.getPixelForValue(s[o]),m();for(h=void 0,o=0,c=e.ticks.length;o<c;++o)u=e.getPixelForTick(o),m();return a}function SM(i,e,s,a){const o=s.barThickness;let c,u;return wt(o)?(c=e.min*s.categoryPercentage,u=s.barPercentage):(c=o*a,u=1),{chunk:c/a,ratio:u,start:e.pixels[i]-c/2}}function MM(i,e,s,a){const o=e.pixels,c=o[i];let u=i>0?o[i-1]:null,h=i<o.length-1?o[i+1]:null;const m=s.categoryPercentage;u===null&&(u=c-(h===null?e.end-e.start:h-c)),h===null&&(h=c+c-u);const g=c-(c-Math.min(u,h))/2*m;return{chunk:Math.abs(h-u)/2*m/a,ratio:s.barPercentage,start:g}}function wM(i,e,s,a){const o=s.parse(i[0],a),c=s.parse(i[1],a),u=Math.min(o,c),h=Math.max(o,c);let m=u,g=h;Math.abs(u)>Math.abs(h)&&(m=h,g=u),e[s.axis]=g,e._custom={barStart:m,barEnd:g,start:o,end:c,min:u,max:h}}function Z0(i,e,s,a){return te(i)?wM(i,e,s,a):e[s.axis]=s.parse(i,a),e}function eb(i,e,s,a){const o=i.iScale,c=i.vScale,u=o.getLabels(),h=o===c,m=[];let g,p,x,v;for(g=s,p=s+a;g<p;++g)v=e[g],x={},x[o.axis]=h||o.parse(u[g],g),m.push(Z0(v,x,c,g));return m}function hf(i){return i&&i.barStart!==void 0&&i.barEnd!==void 0}function TM(i,e,s){return i!==0?_n(i):(e.isHorizontal()?1:-1)*(e.min>=s?1:-1)}function AM(i){let e,s,a,o,c;return i.horizontal?(e=i.base>i.x,s="left",a="right"):(e=i.base<i.y,s="bottom",a="top"),e?(o="end",c="start"):(o="start",c="end"),{start:s,end:a,reverse:e,top:o,bottom:c}}function jM(i,e,s,a){let o=e.borderSkipped;const c={};if(!o){i.borderSkipped=c;return}if(o===!0){i.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:u,end:h,reverse:m,top:g,bottom:p}=AM(i);o==="middle"&&s&&(i.enableBorderRadius=!0,(s._top||0)===a?o=g:(s._bottom||0)===a?o=p:(c[nb(p,u,h,m)]=!0,o=g)),c[nb(o,u,h,m)]=!0,i.borderSkipped=c}function nb(i,e,s,a){return a?(i=DM(i,e,s),i=ib(i,s,e)):i=ib(i,e,s),i}function DM(i,e,s){return i===e?s:i===s?e:i}function ib(i,e,s){return i==="start"?e:i==="end"?s:i}function OM(i,{inflateAmount:e},s){i.inflateAmount=e==="auto"?s===1?.33:0:e}class EM extends eh{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(e,s,a,o){return eb(e,s,a,o)}parseArrayData(e,s,a,o){return eb(e,s,a,o)}parseObjectData(e,s,a,o){const{iScale:c,vScale:u}=e,{xAxisKey:h="x",yAxisKey:m="y"}=this._parsing,g=c.axis==="x"?h:m,p=u.axis==="x"?h:m,x=[];let v,S,M,T;for(v=a,S=a+o;v<S;++v)T=s[v],M={},M[c.axis]=c.parse(Xs(T,g),v),x.push(Z0(Xs(T,p),M,u,v));return x}updateRangeFromParsed(e,s,a,o){super.updateRangeFromParsed(e,s,a,o);const c=a._custom;c&&s===this._cachedMeta.vScale&&(e.min=Math.min(e.min,c.min),e.max=Math.max(e.max,c.max))}getMaxOverflow(){return 0}getLabelAndValue(e){const s=this._cachedMeta,{iScale:a,vScale:o}=s,c=this.getParsed(e),u=c._custom,h=hf(u)?"["+u.start+", "+u.end+"]":""+o.getLabelForValue(c[o.axis]);return{label:""+a.getLabelForValue(c[a.axis]),value:h}}initialize(){this.enableOptionSharing=!0,super.initialize();const e=this._cachedMeta;e.stack=this.getDataset().stack}update(e){const s=this._cachedMeta;this.updateElements(s.data,0,s.data.length,e)}updateElements(e,s,a,o){const c=o==="reset",{index:u,_cachedMeta:{vScale:h}}=this,m=h.getBasePixel(),g=h.isHorizontal(),p=this._getRuler(),{sharedOptions:x,includeOptions:v}=this._getSharedOptions(s,o);for(let S=s;S<s+a;S++){const M=this.getParsed(S),T=c||wt(M[h.axis])?{base:m,head:m}:this._calculateBarValuePixels(S),w=this._calculateBarIndexPixels(S,p),O=(M._stacks||{})[h.axis],H={horizontal:g,base:T.base,enableBorderRadius:!O||hf(M._custom)||u===O._top||u===O._bottom,x:g?T.head:w.center,y:g?w.center:T.head,height:g?w.size:Math.abs(T.size),width:g?Math.abs(T.size):w.size};v&&(H.options=x||this.resolveDataElementOptions(S,e[S].active?"active":o));const q=H.options||e[S].options;jM(H,q,O,u),OM(H,q,p.ratio),this.updateElement(e[S],S,H,o)}}_getStacks(e,s){const{iScale:a}=this._cachedMeta,o=a.getMatchingVisibleMetas(this._type).filter(p=>p.controller.options.grouped),c=a.options.stacked,u=[],h=this._cachedMeta.controller.getParsed(s),m=h&&h[a.axis],g=p=>{const x=p._parsed.find(S=>S[a.axis]===m),v=x&&x[p.vScale.axis];if(wt(v)||isNaN(v))return!0};for(const p of o)if(!(s!==void 0&&g(p))&&((c===!1||u.indexOf(p.stack)===-1||c===void 0&&p.stack===void 0)&&u.push(p.stack),p.index===e))break;return u.length||u.push(void 0),u}_getStackCount(e){return this._getStacks(void 0,e).length}_getAxisCount(){return this._getAxis().length}getFirstScaleIdForIndexAxis(){const e=this.chart.scales,s=this.chart.options.indexAxis;return Object.keys(e).filter(a=>e[a].axis===s).shift()}_getAxis(){const e={},s=this.getFirstScaleIdForIndexAxis();for(const a of this.chart.data.datasets)e[mt(this.chart.options.indexAxis==="x"?a.xAxisID:a.yAxisID,s)]=!0;return Object.keys(e)}_getStackIndex(e,s,a){const o=this._getStacks(e,a),c=s!==void 0?o.indexOf(s):-1;return c===-1?o.length-1:c}_getRuler(){const e=this.options,s=this._cachedMeta,a=s.iScale,o=[];let c,u;for(c=0,u=s.data.length;c<u;++c)o.push(a.getPixelForValue(this.getParsed(c)[a.axis],c));const h=e.barThickness;return{min:h||_M(s),pixels:o,start:a._startPixel,end:a._endPixel,stackCount:this._getStackCount(),scale:a,grouped:e.grouped,ratio:h?1:e.categoryPercentage*e.barPercentage}}_calculateBarValuePixels(e){const{_cachedMeta:{vScale:s,_stacked:a,index:o},options:{base:c,minBarLength:u}}=this,h=c||0,m=this.getParsed(e),g=m._custom,p=hf(g);let x=m[s.axis],v=0,S=a?this.applyStack(s,m,a):x,M,T;S!==x&&(v=S-x,S=x),p&&(x=g.barStart,S=g.barEnd-g.barStart,x!==0&&_n(x)!==_n(g.barEnd)&&(v=0),v+=x);const w=!wt(c)&&!p?c:v;let O=s.getPixelForValue(w);if(this.chart.getDataVisibility(e)?M=s.getPixelForValue(v+S):M=O,T=M-O,Math.abs(T)<u){T=TM(T,s,h)*u,x===h&&(O-=T/2);const H=s.getPixelForDecimal(0),q=s.getPixelForDecimal(1),G=Math.min(H,q),V=Math.max(H,q);O=Math.max(Math.min(O,V),G),M=O+T,a&&!p&&(m._stacks[s.axis]._visualValues[o]=s.getValueForPixel(M)-s.getValueForPixel(O))}if(O===s.getPixelForValue(h)){const H=_n(T)*s.getLineWidthForValue(h)/2;O+=H,T-=H}return{size:T,base:O,head:M,center:M+T/2}}_calculateBarIndexPixels(e,s){const a=s.scale,o=this.options,c=o.skipNull,u=mt(o.maxBarThickness,1/0);let h,m;const g=this._getAxisCount();if(s.grouped){const p=c?this._getStackCount(e):s.stackCount,x=o.barThickness==="flex"?MM(e,s,o,p*g):SM(e,s,o,p*g),v=this.chart.options.indexAxis==="x"?this.getDataset().xAxisID:this.getDataset().yAxisID,S=this._getAxis().indexOf(mt(v,this.getFirstScaleIdForIndexAxis())),M=this._getStackIndex(this.index,this._cachedMeta.stack,c?e:void 0)+S;h=x.start+x.chunk*M+x.chunk/2,m=Math.min(u,x.chunk*x.ratio)}else h=a.getPixelForValue(this.getParsed(e)[a.axis],e),m=Math.min(u,s.min*s.ratio);return{base:h-m/2,head:h+m/2,center:h,size:m}}draw(){const e=this._cachedMeta,s=e.vScale,a=e.data,o=a.length;let c=0;for(;c<o;++c)this.getParsed(c)[s.axis]!==null&&!a[c].hidden&&a[c].draw(this._ctx)}}class zM extends eh{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(e){const s=this._cachedMeta,{dataset:a,data:o=[],_dataset:c}=s,u=this.chart._animationsDisabled;let{start:h,count:m}=e2(s,o,u);this._drawStart=h,this._drawCount=m,n2(s)&&(h=0,m=o.length),a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!c._decimated,a.points=o;const g=this.resolveDatasetElementOptions(e);this.options.showLine||(g.borderWidth=0),g.segment=this.options.segment,this.updateElement(a,void 0,{animated:!u,options:g},e),this.updateElements(o,h,m,e)}updateElements(e,s,a,o){const c=o==="reset",{iScale:u,vScale:h,_stacked:m,_dataset:g}=this._cachedMeta,{sharedOptions:p,includeOptions:x}=this._getSharedOptions(s,o),v=u.axis,S=h.axis,{spanGaps:M,segment:T}=this.options,w=_l(M)?M:Number.POSITIVE_INFINITY,O=this.chart._animationsDisabled||c||o==="none",H=s+a,q=e.length;let G=s>0&&this.getParsed(s-1);for(let V=0;V<q;++V){const F=e[V],X=O?F:{};if(V<s||V>=H){X.skip=!0;continue}const Z=this.getParsed(V),I=wt(Z[S]),rt=X[v]=u.getPixelForValue(Z[v],V),st=X[S]=c||I?h.getBasePixel():h.getPixelForValue(m?this.applyStack(h,Z,m):Z[S],V);X.skip=isNaN(rt)||isNaN(st)||I,X.stop=V>0&&Math.abs(Z[v]-G[v])>w,T&&(X.parsed=Z,X.raw=g.data[V]),x&&(X.options=p||this.resolveDataElementOptions(V,F.active?"active":o)),O||this.updateElement(F,V,X,o),G=Z}}getMaxOverflow(){const e=this._cachedMeta,s=e.dataset,a=s.options&&s.options.borderWidth||0,o=e.data||[];if(!o.length)return a;const c=o[0].size(this.resolveDataElementOptions(0)),u=o[o.length-1].size(this.resolveDataElementOptions(o.length-1));return Math.max(a,c,u)/2}draw(){const e=this._cachedMeta;e.dataset.updateControlPoints(this.chart.chartArea,e.iScale.axis),super.draw()}}function Hi(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class nh{static override(e){Object.assign(nh.prototype,e)}options;constructor(e){this.options=e||{}}init(){}formats(){return Hi()}parse(){return Hi()}format(){return Hi()}add(){return Hi()}diff(){return Hi()}startOf(){return Hi()}endOf(){return Hi()}}var CM={_date:nh};function RM(i,e,s,a){const{controller:o,data:c,_sorted:u}=i,h=o._cachedMeta.iScale,m=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null;if(h&&e===h.axis&&e!=="r"&&u&&c.length){const g=h._reversePixels?PS:Xi;if(a){if(o._sharedOptions){const p=c[0],x=typeof p.getRange=="function"&&p.getRange(e);if(x){const v=g(c,e,s-x),S=g(c,e,s+x);return{lo:v.lo,hi:S.hi}}}}else{const p=g(c,e,s);if(m){const{vScale:x}=o._cachedMeta,{_parsed:v}=i,S=v.slice(0,p.lo+1).reverse().findIndex(T=>!wt(T[x.axis]));p.lo-=Math.max(0,S);const M=v.slice(p.hi).findIndex(T=>!wt(T[x.axis]));p.hi+=Math.max(0,M)}return p}}return{lo:0,hi:c.length-1}}function Cr(i,e,s,a,o){const c=i.getSortedVisibleDatasetMetas(),u=s[e];for(let h=0,m=c.length;h<m;++h){const{index:g,data:p}=c[h],{lo:x,hi:v}=RM(c[h],e,u,o);for(let S=x;S<=v;++S){const M=p[S];M.skip||a(M,g,S)}}}function kM(i){const e=i.indexOf("x")!==-1,s=i.indexOf("y")!==-1;return function(a,o){const c=e?Math.abs(a.x-o.x):0,u=s?Math.abs(a.y-o.y):0;return Math.sqrt(Math.pow(c,2)+Math.pow(u,2))}}function df(i,e,s,a,o){const c=[];return!o&&!i.isPointInArea(e)||Cr(i,s,e,function(h,m,g){!o&&!Sl(h,i.chartArea,0)||h.inRange(e.x,e.y,a)&&c.push({element:h,datasetIndex:m,index:g})},!0),c}function BM(i,e,s,a){let o=[];function c(u,h,m){const{startAngle:g,endAngle:p}=u.getProps(["startAngle","endAngle"],a),{angle:x}=ZS(u,{x:e.x,y:e.y});j0(x,g,p)&&o.push({element:u,datasetIndex:h,index:m})}return Cr(i,s,e,c),o}function NM(i,e,s,a,o,c){let u=[];const h=kM(s);let m=Number.POSITIVE_INFINITY;function g(p,x,v){const S=p.inRange(e.x,e.y,o);if(a&&!S)return;const M=p.getCenterPoint(o);if(!(!!c||i.isPointInArea(M))&&!S)return;const w=h(e,M);w<m?(u=[{element:p,datasetIndex:x,index:v}],m=w):w===m&&u.push({element:p,datasetIndex:x,index:v})}return Cr(i,s,e,g),u}function mf(i,e,s,a,o,c){return!c&&!i.isPointInArea(e)?[]:s==="r"&&!a?BM(i,e,s,o):NM(i,e,s,a,o,c)}function sb(i,e,s,a,o){const c=[],u=s==="x"?"inXRange":"inYRange";let h=!1;return Cr(i,s,e,(m,g,p)=>{m[u]&&m[u](e[s],o)&&(c.push({element:m,datasetIndex:g,index:p}),h=h||m.inRange(e.x,e.y,o))}),a&&!h?[]:c}var LM={modes:{index(i,e,s,a){const o=Ui(e,i),c=s.axis||"x",u=s.includeInvisible||!1,h=s.intersect?df(i,o,c,a,u):mf(i,o,c,!1,a,u),m=[];return h.length?(i.getSortedVisibleDatasetMetas().forEach(g=>{const p=h[0].index,x=g.data[p];x&&!x.skip&&m.push({element:x,datasetIndex:g.index,index:p})}),m):[]},dataset(i,e,s,a){const o=Ui(e,i),c=s.axis||"xy",u=s.includeInvisible||!1;let h=s.intersect?df(i,o,c,a,u):mf(i,o,c,!1,a,u);if(h.length>0){const m=h[0].datasetIndex,g=i.getDatasetMeta(m).data;h=[];for(let p=0;p<g.length;++p)h.push({element:g[p],datasetIndex:m,index:p})}return h},point(i,e,s,a){const o=Ui(e,i),c=s.axis||"xy",u=s.includeInvisible||!1;return df(i,o,c,a,u)},nearest(i,e,s,a){const o=Ui(e,i),c=s.axis||"xy",u=s.includeInvisible||!1;return mf(i,o,c,s.intersect,a,u)},x(i,e,s,a){const o=Ui(e,i);return sb(i,o,"x",s.intersect,a)},y(i,e,s,a){const o=Ui(e,i);return sb(i,o,"y",s.intersect,a)}}};const K0=["left","top","right","bottom"];function Ja(i,e){return i.filter(s=>s.pos===e)}function ab(i,e){return i.filter(s=>K0.indexOf(s.pos)===-1&&s.box.axis===e)}function Ia(i,e){return i.sort((s,a)=>{const o=e?a:s,c=e?s:a;return o.weight===c.weight?o.index-c.index:o.weight-c.weight})}function HM(i){const e=[];let s,a,o,c,u,h;for(s=0,a=(i||[]).length;s<a;++s)o=i[s],{position:c,options:{stack:u,stackWeight:h=1}}=o,e.push({index:s,box:o,pos:c,horizontal:o.isHorizontal(),weight:o.weight,stack:u&&c+u,stackWeight:h});return e}function UM(i){const e={};for(const s of i){const{stack:a,pos:o,stackWeight:c}=s;if(!a||!K0.includes(o))continue;const u=e[a]||(e[a]={count:0,placed:0,weight:0,size:0});u.count++,u.weight+=c}return e}function qM(i,e){const s=UM(i),{vBoxMaxWidth:a,hBoxMaxHeight:o}=e;let c,u,h;for(c=0,u=i.length;c<u;++c){h=i[c];const{fullSize:m}=h.box,g=s[h.stack],p=g&&h.stackWeight/g.weight;h.horizontal?(h.width=p?p*a:m&&e.availableWidth,h.height=o):(h.width=a,h.height=p?p*o:m&&e.availableHeight)}return s}function VM(i){const e=HM(i),s=Ia(e.filter(g=>g.box.fullSize),!0),a=Ia(Ja(e,"left"),!0),o=Ia(Ja(e,"right")),c=Ia(Ja(e,"top"),!0),u=Ia(Ja(e,"bottom")),h=ab(e,"x"),m=ab(e,"y");return{fullSize:s,leftAndTop:a.concat(c),rightAndBottom:o.concat(m).concat(u).concat(h),chartArea:Ja(e,"chartArea"),vertical:a.concat(o).concat(m),horizontal:c.concat(u).concat(h)}}function lb(i,e,s,a){return Math.max(i[s],e[s])+Math.max(i[a],e[a])}function W0(i,e){i.top=Math.max(i.top,e.top),i.left=Math.max(i.left,e.left),i.bottom=Math.max(i.bottom,e.bottom),i.right=Math.max(i.right,e.right)}function FM(i,e,s,a){const{pos:o,box:c}=s,u=i.maxPadding;if(!vt(o)){s.size&&(i[o]-=s.size);const x=a[s.stack]||{size:0,count:1};x.size=Math.max(x.size,s.horizontal?c.height:c.width),s.size=x.size/x.count,i[o]+=s.size}c.getPadding&&W0(u,c.getPadding());const h=Math.max(0,e.outerWidth-lb(u,i,"left","right")),m=Math.max(0,e.outerHeight-lb(u,i,"top","bottom")),g=h!==i.w,p=m!==i.h;return i.w=h,i.h=m,s.horizontal?{same:g,other:p}:{same:p,other:g}}function YM(i){const e=i.maxPadding;function s(a){const o=Math.max(e[a]-i[a],0);return i[a]+=o,o}i.y+=s("top"),i.x+=s("left"),s("right"),s("bottom")}function XM(i,e){const s=e.maxPadding;function a(o){const c={left:0,top:0,right:0,bottom:0};return o.forEach(u=>{c[u]=Math.max(e[u],s[u])}),c}return a(i?["left","right"]:["top","bottom"])}function ll(i,e,s,a){const o=[];let c,u,h,m,g,p;for(c=0,u=i.length,g=0;c<u;++c){h=i[c],m=h.box,m.update(h.width||e.w,h.height||e.h,XM(h.horizontal,e));const{same:x,other:v}=FM(e,s,h,a);g|=x&&o.length,p=p||v,m.fullSize||o.push(h)}return g&&ll(o,e,s,a)||p}function lr(i,e,s,a,o){i.top=s,i.left=e,i.right=e+a,i.bottom=s+o,i.width=a,i.height=o}function ob(i,e,s,a){const o=s.padding;let{x:c,y:u}=e;for(const h of i){const m=h.box,g=a[h.stack]||{placed:0,weight:1},p=h.stackWeight/g.weight||1;if(h.horizontal){const x=e.w*p,v=g.size||m.height;vl(g.start)&&(u=g.start),m.fullSize?lr(m,o.left,u,s.outerWidth-o.right-o.left,v):lr(m,e.left+g.placed,u,x,v),g.start=u,g.placed+=x,u=m.bottom}else{const x=e.h*p,v=g.size||m.width;vl(g.start)&&(c=g.start),m.fullSize?lr(m,c,o.top,v,s.outerHeight-o.bottom-o.top):lr(m,c,e.top+g.placed,v,x),g.start=c,g.placed+=x,c=m.right}}e.x=c,e.y=u}var Ie={addBox(i,e){i.boxes||(i.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(s){e.draw(s)}}]},i.boxes.push(e)},removeBox(i,e){const s=i.boxes?i.boxes.indexOf(e):-1;s!==-1&&i.boxes.splice(s,1)},configure(i,e,s){e.fullSize=s.fullSize,e.position=s.position,e.weight=s.weight},update(i,e,s,a){if(!i)return;const o=en(i.options.layout.padding),c=Math.max(e-o.width,0),u=Math.max(s-o.height,0),h=VM(i.boxes),m=h.vertical,g=h.horizontal;Dt(i.boxes,T=>{typeof T.beforeLayout=="function"&&T.beforeLayout()});const p=m.reduce((T,w)=>w.box.options&&w.box.options.display===!1?T:T+1,0)||1,x=Object.freeze({outerWidth:e,outerHeight:s,padding:o,availableWidth:c,availableHeight:u,vBoxMaxWidth:c/2/p,hBoxMaxHeight:u/2}),v=Object.assign({},o);W0(v,en(a));const S=Object.assign({maxPadding:v,w:c,h:u,x:o.left,y:o.top},o),M=qM(m.concat(g),x);ll(h.fullSize,S,x,M),ll(m,S,x,M),ll(g,S,x,M)&&ll(m,S,x,M),YM(S),ob(h.leftAndTop,S,x,M),S.x+=S.w,S.y+=S.h,ob(h.rightAndBottom,S,x,M),i.chartArea={left:S.left,top:S.top,right:S.left+S.w,bottom:S.top+S.h,height:S.h,width:S.w},Dt(h.chartArea,T=>{const w=T.box;Object.assign(w,i.chartArea),w.update(S.w,S.h,{left:0,top:0,right:0,bottom:0})})}};class P0{acquireContext(e,s){}releaseContext(e){return!1}addEventListener(e,s,a){}removeEventListener(e,s,a){}getDevicePixelRatio(){return 1}getMaximumSize(e,s,a,o){return s=Math.max(0,s||e.width),a=a||e.height,{width:s,height:Math.max(0,o?Math.floor(s/o):a)}}isAttached(e){return!0}updateConfig(e){}}class GM extends P0{acquireContext(e){return e&&e.getContext&&e.getContext("2d")||null}updateConfig(e){e.options.animation=!1}}const gr="$chartjs",QM={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},rb=i=>i===null||i==="";function ZM(i,e){const s=i.style,a=i.getAttribute("height"),o=i.getAttribute("width");if(i[gr]={initial:{height:a,width:o,style:{display:s.display,height:s.height,width:s.width}}},s.display=s.display||"block",s.boxSizing=s.boxSizing||"border-box",rb(o)){const c=Gp(i,"width");c!==void 0&&(i.width=c)}if(rb(a))if(i.style.height==="")i.height=i.width/(e||2);else{const c=Gp(i,"height");c!==void 0&&(i.height=c)}return i}const J0=G2?{passive:!0}:!1;function KM(i,e,s){i&&i.addEventListener(e,s,J0)}function WM(i,e,s){i&&i.canvas&&i.canvas.removeEventListener(e,s,J0)}function PM(i,e){const s=QM[i.type]||i.type,{x:a,y:o}=Ui(i,e);return{type:s,chart:e,native:i,x:a!==void 0?a:null,y:o!==void 0?o:null}}function wr(i,e){for(const s of i)if(s===e||s.contains(e))return!0}function JM(i,e,s){const a=i.canvas,o=new MutationObserver(c=>{let u=!1;for(const h of c)u=u||wr(h.addedNodes,a),u=u&&!wr(h.removedNodes,a);u&&s()});return o.observe(document,{childList:!0,subtree:!0}),o}function IM(i,e,s){const a=i.canvas,o=new MutationObserver(c=>{let u=!1;for(const h of c)u=u||wr(h.removedNodes,a),u=u&&!wr(h.addedNodes,a);u&&s()});return o.observe(document,{childList:!0,subtree:!0}),o}const wl=new Map;let cb=0;function I0(){const i=window.devicePixelRatio;i!==cb&&(cb=i,wl.forEach((e,s)=>{s.currentDevicePixelRatio!==i&&e()}))}function $M(i,e){wl.size||window.addEventListener("resize",I0),wl.set(i,e)}function tw(i){wl.delete(i),wl.size||window.removeEventListener("resize",I0)}function ew(i,e,s){const a=i.canvas,o=a&&th(a);if(!o)return;const c=z0((h,m)=>{const g=o.clientWidth;s(h,m),g<o.clientWidth&&s()},window),u=new ResizeObserver(h=>{const m=h[0],g=m.contentRect.width,p=m.contentRect.height;g===0&&p===0||c(g,p)});return u.observe(o),$M(i,c),u}function gf(i,e,s){s&&s.disconnect(),e==="resize"&&tw(i)}function nw(i,e,s){const a=i.canvas,o=z0(c=>{i.ctx!==null&&s(PM(c,i))},i);return KM(a,e,o),o}class iw extends P0{acquireContext(e,s){const a=e&&e.getContext&&e.getContext("2d");return a&&a.canvas===e?(ZM(e,s),a):null}releaseContext(e){const s=e.canvas;if(!s[gr])return!1;const a=s[gr].initial;["height","width"].forEach(c=>{const u=a[c];wt(u)?s.removeAttribute(c):s.setAttribute(c,u)});const o=a.style||{};return Object.keys(o).forEach(c=>{s.style[c]=o[c]}),s.width=s.width,delete s[gr],!0}addEventListener(e,s,a){this.removeEventListener(e,s);const o=e.$proxies||(e.$proxies={}),u={attach:JM,detach:IM,resize:ew}[s]||nw;o[s]=u(e,s,a)}removeEventListener(e,s){const a=e.$proxies||(e.$proxies={}),o=a[s];if(!o)return;({attach:gf,detach:gf,resize:gf}[s]||WM)(e,s,o),a[s]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(e,s,a,o){return X2(e,s,a,o)}isAttached(e){const s=e&&th(e);return!!(s&&s.isConnected)}}function sw(i){return!$f()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?GM:iw}class bi{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(e){const{x:s,y:a}=this.getProps(["x","y"],e);return{x:s,y:a}}hasValue(){return _l(this.x)&&_l(this.y)}getProps(e,s){const a=this.$animations;if(!s||!a)return this;const o={};return e.forEach(c=>{o[c]=a[c]&&a[c].active()?a[c]._to:this[c]}),o}}function aw(i,e){const s=i.options.ticks,a=lw(i),o=Math.min(s.maxTicksLimit||a,a),c=s.major.enabled?rw(e):[],u=c.length,h=c[0],m=c[u-1],g=[];if(u>o)return cw(e,g,c,u/o),g;const p=ow(c,e,o);if(u>0){let x,v;const S=u>1?Math.round((m-h)/(u-1)):null;for(or(e,g,p,wt(S)?0:h-S,h),x=0,v=u-1;x<v;x++)or(e,g,p,c[x],c[x+1]);return or(e,g,p,m,wt(S)?e.length:m+S),g}return or(e,g,p),g}function lw(i){const e=i.options.offset,s=i._tickSize(),a=i._length/s+(e?0:1),o=i._maxLength/s;return Math.floor(Math.min(a,o))}function ow(i,e,s){const a=uw(i),o=e.length/s;if(!a)return Math.max(o,1);const c=FS(a);for(let u=0,h=c.length-1;u<h;u++){const m=c[u];if(m>o)return m}return Math.max(o,1)}function rw(i){const e=[];let s,a;for(s=0,a=i.length;s<a;s++)i[s].major&&e.push(s);return e}function cw(i,e,s,a){let o=0,c=s[0],u;for(a=Math.ceil(a),u=0;u<i.length;u++)u===c&&(e.push(i[u]),o++,c=s[o*a])}function or(i,e,s,a,o){const c=mt(a,0),u=Math.min(mt(o,i.length),i.length);let h=0,m,g,p;for(s=Math.ceil(s),o&&(m=o-a,s=m/Math.floor(m/s)),p=c;p<0;)h++,p=Math.round(c+h*s);for(g=Math.max(c,0);g<u;g++)g===p&&(e.push(i[g]),h++,p=Math.round(c+h*s))}function uw(i){const e=i.length;let s,a;if(e<2)return!1;for(a=i[0],s=1;s<e;++s)if(i[s]-i[s-1]!==a)return!1;return a}const fw=i=>i==="left"?"right":i==="right"?"left":i,ub=(i,e,s)=>e==="top"||e==="left"?i[e]+s:i[e]-s,fb=(i,e)=>Math.min(e||i,i);function hb(i,e){const s=[],a=i.length/e,o=i.length;let c=0;for(;c<o;c+=a)s.push(i[Math.floor(c)]);return s}function hw(i,e,s){const a=i.ticks.length,o=Math.min(e,a-1),c=i._startPixel,u=i._endPixel,h=1e-6;let m=i.getPixelForTick(o),g;if(!(s&&(a===1?g=Math.max(m-c,u-m):e===0?g=(i.getPixelForTick(1)-m)/2:g=(m-i.getPixelForTick(o-1))/2,m+=o<e?g:-g,m<c-h||m>u+h)))return m}function dw(i,e){Dt(i,s=>{const a=s.gc,o=a.length/2;let c;if(o>e){for(c=0;c<o;++c)delete s.data[a[c]];a.splice(0,o)}})}function $a(i){return i.drawTicks?i.tickLength:0}function db(i,e){if(!i.display)return 0;const s=be(i.font,e),a=en(i.padding);return(te(i.text)?i.text.length:1)*s.lineHeight+a.height}function mw(i,e){return Wi(i,{scale:e,type:"scale"})}function gw(i,e,s){return Wi(i,{tick:s,index:e,type:"tick"})}function pw(i,e,s){let a=Qf(i);return(s&&e!=="right"||!s&&e==="right")&&(a=fw(a)),a}function bw(i,e,s,a){const{top:o,left:c,bottom:u,right:h,chart:m}=i,{chartArea:g,scales:p}=m;let x=0,v,S,M;const T=u-o,w=h-c;if(i.isHorizontal()){if(S=de(a,c,h),vt(s)){const O=Object.keys(s)[0],H=s[O];M=p[O].getPixelForValue(H)+T-e}else s==="center"?M=(g.bottom+g.top)/2+T-e:M=ub(i,s,e);v=h-c}else{if(vt(s)){const O=Object.keys(s)[0],H=s[O];S=p[O].getPixelForValue(H)-w+e}else s==="center"?S=(g.left+g.right)/2-w+e:S=ub(i,s,e);M=de(a,u,o),x=s==="left"?-ln:ln}return{titleX:S,titleY:M,maxWidth:v,rotation:x}}class Ws extends bi{constructor(e){super(),this.id=e.id,this.type=e.type,this.options=void 0,this.ctx=e.ctx,this.chart=e.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(e){this.options=e.setContext(this.getContext()),this.axis=e.axis,this._userMin=this.parse(e.min),this._userMax=this.parse(e.max),this._suggestedMin=this.parse(e.suggestedMin),this._suggestedMax=this.parse(e.suggestedMax)}parse(e,s){return e}getUserBounds(){let{_userMin:e,_userMax:s,_suggestedMin:a,_suggestedMax:o}=this;return e=pn(e,Number.POSITIVE_INFINITY),s=pn(s,Number.NEGATIVE_INFINITY),a=pn(a,Number.POSITIVE_INFINITY),o=pn(o,Number.NEGATIVE_INFINITY),{min:pn(e,a),max:pn(s,o),minDefined:tn(e),maxDefined:tn(s)}}getMinMax(e){let{min:s,max:a,minDefined:o,maxDefined:c}=this.getUserBounds(),u;if(o&&c)return{min:s,max:a};const h=this.getMatchingVisibleMetas();for(let m=0,g=h.length;m<g;++m)u=h[m].controller.getMinMax(this,e),o||(s=Math.min(s,u.min)),c||(a=Math.max(a,u.max));return s=c&&s>a?a:s,a=o&&s>a?s:a,{min:pn(s,pn(a,s)),max:pn(a,pn(s,a))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const e=this.chart.data;return this.options.labels||(this.isHorizontal()?e.xLabels:e.yLabels)||e.labels||[]}getLabelItems(e=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(e))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){Lt(this.options.beforeUpdate,[this])}update(e,s,a){const{beginAtZero:o,grace:c,ticks:u}=this.options,h=u.sampleSize;this.beforeUpdate(),this.maxWidth=e,this.maxHeight=s,this._margins=a=Object.assign({left:0,right:0,top:0,bottom:0},a),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+a.left+a.right:this.height+a.top+a.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=S2(this,c,o),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const m=h<this.ticks.length;this._convertTicksToLabels(m?hb(this.ticks,h):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),u.display&&(u.autoSkip||u.source==="auto")&&(this.ticks=aw(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),m&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let e=this.options.reverse,s,a;this.isHorizontal()?(s=this.left,a=this.right):(s=this.top,a=this.bottom,e=!e),this._startPixel=s,this._endPixel=a,this._reversePixels=e,this._length=a-s,this._alignToPixels=this.options.alignToPixels}afterUpdate(){Lt(this.options.afterUpdate,[this])}beforeSetDimensions(){Lt(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){Lt(this.options.afterSetDimensions,[this])}_callHooks(e){this.chart.notifyPlugins(e,this.getContext()),Lt(this.options[e],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){Lt(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(e){const s=this.options.ticks;let a,o,c;for(a=0,o=e.length;a<o;a++)c=e[a],c.label=Lt(s.callback,[c.value,a,e],this)}afterTickToLabelConversion(){Lt(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){Lt(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const e=this.options,s=e.ticks,a=fb(this.ticks.length,e.ticks.maxTicksLimit),o=s.minRotation||0,c=s.maxRotation;let u=o,h,m,g;if(!this._isVisible()||!s.display||o>=c||a<=1||!this.isHorizontal()){this.labelRotation=o;return}const p=this._getLabelSizes(),x=p.widest.width,v=p.highest.height,S=He(this.chart.width-x,0,this.maxWidth);h=e.offset?this.maxWidth/a:S/(a-1),x+6>h&&(h=S/(a-(e.offset?.5:1)),m=this.maxHeight-$a(e.grid)-s.padding-db(e.title,this.chart.options.font),g=Math.sqrt(x*x+v*v),u=QS(Math.min(Math.asin(He((p.highest.height+6)/h,-1,1)),Math.asin(He(m/g,-1,1))-Math.asin(He(v/g,-1,1)))),u=Math.max(o,Math.min(c,u))),this.labelRotation=u}afterCalculateLabelRotation(){Lt(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){Lt(this.options.beforeFit,[this])}fit(){const e={width:0,height:0},{chart:s,options:{ticks:a,title:o,grid:c}}=this,u=this._isVisible(),h=this.isHorizontal();if(u){const m=db(o,s.options.font);if(h?(e.width=this.maxWidth,e.height=$a(c)+m):(e.height=this.maxHeight,e.width=$a(c)+m),a.display&&this.ticks.length){const{first:g,last:p,widest:x,highest:v}=this._getLabelSizes(),S=a.padding*2,M=Fi(this.labelRotation),T=Math.cos(M),w=Math.sin(M);if(h){const O=a.mirror?0:w*x.width+T*v.height;e.height=Math.min(this.maxHeight,e.height+O+S)}else{const O=a.mirror?0:T*x.width+w*v.height;e.width=Math.min(this.maxWidth,e.width+O+S)}this._calculatePadding(g,p,w,T)}}this._handleMargins(),h?(this.width=this._length=s.width-this._margins.left-this._margins.right,this.height=e.height):(this.width=e.width,this.height=this._length=s.height-this._margins.top-this._margins.bottom)}_calculatePadding(e,s,a,o){const{ticks:{align:c,padding:u},position:h}=this.options,m=this.labelRotation!==0,g=h!=="top"&&this.axis==="x";if(this.isHorizontal()){const p=this.getPixelForTick(0)-this.left,x=this.right-this.getPixelForTick(this.ticks.length-1);let v=0,S=0;m?g?(v=o*e.width,S=a*s.height):(v=a*e.height,S=o*s.width):c==="start"?S=s.width:c==="end"?v=e.width:c!=="inner"&&(v=e.width/2,S=s.width/2),this.paddingLeft=Math.max((v-p+u)*this.width/(this.width-p),0),this.paddingRight=Math.max((S-x+u)*this.width/(this.width-x),0)}else{let p=s.height/2,x=e.height/2;c==="start"?(p=0,x=e.height):c==="end"&&(p=s.height,x=0),this.paddingTop=p+u,this.paddingBottom=x+u}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){Lt(this.options.afterFit,[this])}isHorizontal(){const{axis:e,position:s}=this.options;return s==="top"||s==="bottom"||e==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(e){this.beforeTickToLabelConversion(),this.generateTickLabels(e);let s,a;for(s=0,a=e.length;s<a;s++)wt(e[s].label)&&(e.splice(s,1),a--,s--);this.afterTickToLabelConversion()}_getLabelSizes(){let e=this._labelSizes;if(!e){const s=this.options.ticks.sampleSize;let a=this.ticks;s<a.length&&(a=hb(a,s)),this._labelSizes=e=this._computeLabelSizes(a,a.length,this.options.ticks.maxTicksLimit)}return e}_computeLabelSizes(e,s,a){const{ctx:o,_longestTextCache:c}=this,u=[],h=[],m=Math.floor(s/fb(s,a));let g=0,p=0,x,v,S,M,T,w,O,H,q,G,V;for(x=0;x<s;x+=m){if(M=e[x].label,T=this._resolveTickFontOptions(x),o.font=w=T.string,O=c[w]=c[w]||{data:{},gc:[]},H=T.lineHeight,q=G=0,!wt(M)&&!te(M))q=qp(o,O.data,O.gc,q,M),G=H;else if(te(M))for(v=0,S=M.length;v<S;++v)V=M[v],!wt(V)&&!te(V)&&(q=qp(o,O.data,O.gc,q,V),G+=H);u.push(q),h.push(G),g=Math.max(q,g),p=Math.max(G,p)}dw(c,s);const F=u.indexOf(g),X=h.indexOf(p),Z=I=>({width:u[I]||0,height:h[I]||0});return{first:Z(0),last:Z(s-1),widest:Z(F),highest:Z(X),widths:u,heights:h}}getLabelForValue(e){return e}getPixelForValue(e,s){return NaN}getValueForPixel(e){}getPixelForTick(e){const s=this.ticks;return e<0||e>s.length-1?null:this.getPixelForValue(s[e].value)}getPixelForDecimal(e){this._reversePixels&&(e=1-e);const s=this._startPixel+e*this._length;return WS(this._alignToPixels?Li(this.chart,s,0):s)}getDecimalForPixel(e){const s=(e-this._startPixel)/this._length;return this._reversePixels?1-s:s}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:e,max:s}=this;return e<0&&s<0?s:e>0&&s>0?e:0}getContext(e){const s=this.ticks||[];if(e>=0&&e<s.length){const a=s[e];return a.$context||(a.$context=gw(this.getContext(),e,a))}return this.$context||(this.$context=mw(this.chart.getContext(),this))}_tickSize(){const e=this.options.ticks,s=Fi(this.labelRotation),a=Math.abs(Math.cos(s)),o=Math.abs(Math.sin(s)),c=this._getLabelSizes(),u=e.autoSkipPadding||0,h=c?c.widest.width+u:0,m=c?c.highest.height+u:0;return this.isHorizontal()?m*a>h*o?h/a:m/o:m*o<h*a?m/a:h/o}_isVisible(){const e=this.options.display;return e!=="auto"?!!e:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(e){const s=this.axis,a=this.chart,o=this.options,{grid:c,position:u,border:h}=o,m=c.offset,g=this.isHorizontal(),x=this.ticks.length+(m?1:0),v=$a(c),S=[],M=h.setContext(this.getContext()),T=M.display?M.width:0,w=T/2,O=function(k){return Li(a,k,T)};let H,q,G,V,F,X,Z,I,rt,st,nt,Nt;if(u==="top")H=O(this.bottom),X=this.bottom-v,I=H-w,st=O(e.top)+w,Nt=e.bottom;else if(u==="bottom")H=O(this.top),st=e.top,Nt=O(e.bottom)-w,X=H+w,I=this.top+v;else if(u==="left")H=O(this.right),F=this.right-v,Z=H-w,rt=O(e.left)+w,nt=e.right;else if(u==="right")H=O(this.left),rt=e.left,nt=O(e.right)-w,F=H+w,Z=this.left+v;else if(s==="x"){if(u==="center")H=O((e.top+e.bottom)/2+.5);else if(vt(u)){const k=Object.keys(u)[0],Q=u[k];H=O(this.chart.scales[k].getPixelForValue(Q))}st=e.top,Nt=e.bottom,X=H+w,I=X+v}else if(s==="y"){if(u==="center")H=O((e.left+e.right)/2);else if(vt(u)){const k=Object.keys(u)[0],Q=u[k];H=O(this.chart.scales[k].getPixelForValue(Q))}F=H-w,Z=F-v,rt=e.left,nt=e.right}const Ht=mt(o.ticks.maxTicksLimit,x),gt=Math.max(1,Math.ceil(x/Ht));for(q=0;q<x;q+=gt){const k=this.getContext(q),Q=c.setContext(k),P=h.setContext(k),xt=Q.lineWidth,pt=Q.color,Tt=P.dash||[],ft=P.dashOffset,ct=Q.tickWidth,_t=Q.tickColor,Jt=Q.tickBorderDash||[],ue=Q.tickBorderDashOffset;G=hw(this,q,m),G!==void 0&&(V=Li(a,G,xt),g?F=Z=rt=nt=V:X=I=st=Nt=V,S.push({tx1:F,ty1:X,tx2:Z,ty2:I,x1:rt,y1:st,x2:nt,y2:Nt,width:xt,color:pt,borderDash:Tt,borderDashOffset:ft,tickWidth:ct,tickColor:_t,tickBorderDash:Jt,tickBorderDashOffset:ue}))}return this._ticksLength=x,this._borderValue=H,S}_computeLabelItems(e){const s=this.axis,a=this.options,{position:o,ticks:c}=a,u=this.isHorizontal(),h=this.ticks,{align:m,crossAlign:g,padding:p,mirror:x}=c,v=$a(a.grid),S=v+p,M=x?-p:S,T=-Fi(this.labelRotation),w=[];let O,H,q,G,V,F,X,Z,I,rt,st,nt,Nt="middle";if(o==="top")F=this.bottom-M,X=this._getXAxisLabelAlignment();else if(o==="bottom")F=this.top+M,X=this._getXAxisLabelAlignment();else if(o==="left"){const gt=this._getYAxisLabelAlignment(v);X=gt.textAlign,V=gt.x}else if(o==="right"){const gt=this._getYAxisLabelAlignment(v);X=gt.textAlign,V=gt.x}else if(s==="x"){if(o==="center")F=(e.top+e.bottom)/2+S;else if(vt(o)){const gt=Object.keys(o)[0],k=o[gt];F=this.chart.scales[gt].getPixelForValue(k)+S}X=this._getXAxisLabelAlignment()}else if(s==="y"){if(o==="center")V=(e.left+e.right)/2-S;else if(vt(o)){const gt=Object.keys(o)[0],k=o[gt];V=this.chart.scales[gt].getPixelForValue(k)}X=this._getYAxisLabelAlignment(v).textAlign}s==="y"&&(m==="start"?Nt="top":m==="end"&&(Nt="bottom"));const Ht=this._getLabelSizes();for(O=0,H=h.length;O<H;++O){q=h[O],G=q.label;const gt=c.setContext(this.getContext(O));Z=this.getPixelForTick(O)+c.labelOffset,I=this._resolveTickFontOptions(O),rt=I.lineHeight,st=te(G)?G.length:1;const k=st/2,Q=gt.color,P=gt.textStrokeColor,xt=gt.textStrokeWidth;let pt=X;u?(V=Z,X==="inner"&&(O===H-1?pt=this.options.reverse?"left":"right":O===0?pt=this.options.reverse?"right":"left":pt="center"),o==="top"?g==="near"||T!==0?nt=-st*rt+rt/2:g==="center"?nt=-Ht.highest.height/2-k*rt+rt:nt=-Ht.highest.height+rt/2:g==="near"||T!==0?nt=rt/2:g==="center"?nt=Ht.highest.height/2-k*rt:nt=Ht.highest.height-st*rt,x&&(nt*=-1),T!==0&&!gt.showLabelBackdrop&&(V+=rt/2*Math.sin(T))):(F=Z,nt=(1-st)*rt/2);let Tt;if(gt.showLabelBackdrop){const ft=en(gt.backdropPadding),ct=Ht.heights[O],_t=Ht.widths[O];let Jt=nt-ft.top,ue=0-ft.left;switch(Nt){case"middle":Jt-=ct/2;break;case"bottom":Jt-=ct;break}switch(X){case"center":ue-=_t/2;break;case"right":ue-=_t;break;case"inner":O===H-1?ue-=_t:O>0&&(ue-=_t/2);break}Tt={left:ue,top:Jt,width:_t+ft.width,height:ct+ft.height,color:gt.backdropColor}}w.push({label:G,font:I,textOffset:nt,options:{rotation:T,color:Q,strokeColor:P,strokeWidth:xt,textAlign:pt,textBaseline:Nt,translation:[V,F],backdrop:Tt}})}return w}_getXAxisLabelAlignment(){const{position:e,ticks:s}=this.options;if(-Fi(this.labelRotation))return e==="top"?"left":"right";let o="center";return s.align==="start"?o="left":s.align==="end"?o="right":s.align==="inner"&&(o="inner"),o}_getYAxisLabelAlignment(e){const{position:s,ticks:{crossAlign:a,mirror:o,padding:c}}=this.options,u=this._getLabelSizes(),h=e+c,m=u.widest.width;let g,p;return s==="left"?o?(p=this.right+c,a==="near"?g="left":a==="center"?(g="center",p+=m/2):(g="right",p+=m)):(p=this.right-h,a==="near"?g="right":a==="center"?(g="center",p-=m/2):(g="left",p=this.left)):s==="right"?o?(p=this.left+c,a==="near"?g="right":a==="center"?(g="center",p-=m/2):(g="left",p-=m)):(p=this.left+h,a==="near"?g="left":a==="center"?(g="center",p+=m/2):(g="right",p=this.right)):g="right",{textAlign:g,x:p}}_computeLabelArea(){if(this.options.ticks.mirror)return;const e=this.chart,s=this.options.position;if(s==="left"||s==="right")return{top:0,left:this.left,bottom:e.height,right:this.right};if(s==="top"||s==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:e.width}}drawBackground(){const{ctx:e,options:{backgroundColor:s},left:a,top:o,width:c,height:u}=this;s&&(e.save(),e.fillStyle=s,e.fillRect(a,o,c,u),e.restore())}getLineWidthForValue(e){const s=this.options.grid;if(!this._isVisible()||!s.display)return 0;const o=this.ticks.findIndex(c=>c.value===e);return o>=0?s.setContext(this.getContext(o)).lineWidth:0}drawGrid(e){const s=this.options.grid,a=this.ctx,o=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(e));let c,u;const h=(m,g,p)=>{!p.width||!p.color||(a.save(),a.lineWidth=p.width,a.strokeStyle=p.color,a.setLineDash(p.borderDash||[]),a.lineDashOffset=p.borderDashOffset,a.beginPath(),a.moveTo(m.x,m.y),a.lineTo(g.x,g.y),a.stroke(),a.restore())};if(s.display)for(c=0,u=o.length;c<u;++c){const m=o[c];s.drawOnChartArea&&h({x:m.x1,y:m.y1},{x:m.x2,y:m.y2},m),s.drawTicks&&h({x:m.tx1,y:m.ty1},{x:m.tx2,y:m.ty2},{color:m.tickColor,width:m.tickWidth,borderDash:m.tickBorderDash,borderDashOffset:m.tickBorderDashOffset})}}drawBorder(){const{chart:e,ctx:s,options:{border:a,grid:o}}=this,c=a.setContext(this.getContext()),u=a.display?c.width:0;if(!u)return;const h=o.setContext(this.getContext(0)).lineWidth,m=this._borderValue;let g,p,x,v;this.isHorizontal()?(g=Li(e,this.left,u)-u/2,p=Li(e,this.right,h)+h/2,x=v=m):(x=Li(e,this.top,u)-u/2,v=Li(e,this.bottom,h)+h/2,g=p=m),s.save(),s.lineWidth=c.width,s.strokeStyle=c.color,s.beginPath(),s.moveTo(g,x),s.lineTo(p,v),s.stroke(),s.restore()}drawLabels(e){if(!this.options.ticks.display)return;const a=this.ctx,o=this._computeLabelArea();o&&Kf(a,o);const c=this.getLabelItems(e);for(const u of c){const h=u.options,m=u.font,g=u.label,p=u.textOffset;Ml(a,g,0,p,m,h)}o&&Wf(a)}drawTitle(){const{ctx:e,options:{position:s,title:a,reverse:o}}=this;if(!a.display)return;const c=be(a.font),u=en(a.padding),h=a.align;let m=c.lineHeight/2;s==="bottom"||s==="center"||vt(s)?(m+=u.bottom,te(a.text)&&(m+=c.lineHeight*(a.text.length-1))):m+=u.top;const{titleX:g,titleY:p,maxWidth:x,rotation:v}=bw(this,m,s,h);Ml(e,a.text,0,0,c,{color:a.color,maxWidth:x,rotation:v,textAlign:pw(h,s,o),textBaseline:"middle",translation:[g,p]})}draw(e){this._isVisible()&&(this.drawBackground(),this.drawGrid(e),this.drawBorder(),this.drawTitle(),this.drawLabels(e))}_layers(){const e=this.options,s=e.ticks&&e.ticks.z||0,a=mt(e.grid&&e.grid.z,-1),o=mt(e.border&&e.border.z,0);return!this._isVisible()||this.draw!==Ws.prototype.draw?[{z:s,draw:c=>{this.draw(c)}}]:[{z:a,draw:c=>{this.drawBackground(),this.drawGrid(c),this.drawTitle()}},{z:o,draw:()=>{this.drawBorder()}},{z:s,draw:c=>{this.drawLabels(c)}}]}getMatchingVisibleMetas(e){const s=this.chart.getSortedVisibleDatasetMetas(),a=this.axis+"AxisID",o=[];let c,u;for(c=0,u=s.length;c<u;++c){const h=s[c];h[a]===this.id&&(!e||h.type===e)&&o.push(h)}return o}_resolveTickFontOptions(e){const s=this.options.ticks.setContext(this.getContext(e));return be(s.font)}_maxDigits(){const e=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/e}}class rr{constructor(e,s,a){this.type=e,this.scope=s,this.override=a,this.items=Object.create(null)}isForType(e){return Object.prototype.isPrototypeOf.call(this.type.prototype,e.prototype)}register(e){const s=Object.getPrototypeOf(e);let a;vw(s)&&(a=this.register(s));const o=this.items,c=e.id,u=this.scope+"."+c;if(!c)throw new Error("class does not have id: "+e);return c in o||(o[c]=e,xw(e,u,a),this.override&&Zt.override(e.id,e.overrides)),u}get(e){return this.items[e]}unregister(e){const s=this.items,a=e.id,o=this.scope;a in s&&delete s[a],o&&a in Zt[o]&&(delete Zt[o][a],this.override&&delete Ki[a])}}function xw(i,e,s){const a=yl(Object.create(null),[s?Zt.get(s):{},Zt.get(e),i.defaults]);Zt.set(e,a),i.defaultRoutes&&yw(e,i.defaultRoutes),i.descriptors&&Zt.describe(e,i.descriptors)}function yw(i,e){Object.keys(e).forEach(s=>{const a=s.split("."),o=a.pop(),c=[i].concat(a).join("."),u=e[s].split("."),h=u.pop(),m=u.join(".");Zt.route(c,o,m,h)})}function vw(i){return"id"in i&&"defaults"in i}class _w{constructor(){this.controllers=new rr(eh,"datasets",!0),this.elements=new rr(bi,"elements"),this.plugins=new rr(Object,"plugins"),this.scales=new rr(Ws,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...e){this._each("register",e)}remove(...e){this._each("unregister",e)}addControllers(...e){this._each("register",e,this.controllers)}addElements(...e){this._each("register",e,this.elements)}addPlugins(...e){this._each("register",e,this.plugins)}addScales(...e){this._each("register",e,this.scales)}getController(e){return this._get(e,this.controllers,"controller")}getElement(e){return this._get(e,this.elements,"element")}getPlugin(e){return this._get(e,this.plugins,"plugin")}getScale(e){return this._get(e,this.scales,"scale")}removeControllers(...e){this._each("unregister",e,this.controllers)}removeElements(...e){this._each("unregister",e,this.elements)}removePlugins(...e){this._each("unregister",e,this.plugins)}removeScales(...e){this._each("unregister",e,this.scales)}_each(e,s,a){[...s].forEach(o=>{const c=a||this._getRegistryForType(o);a||c.isForType(o)||c===this.plugins&&o.id?this._exec(e,c,o):Dt(o,u=>{const h=a||this._getRegistryForType(u);this._exec(e,h,u)})})}_exec(e,s,a){const o=Xf(e);Lt(a["before"+o],[],a),s[e](a),Lt(a["after"+o],[],a)}_getRegistryForType(e){for(let s=0;s<this._typedRegistries.length;s++){const a=this._typedRegistries[s];if(a.isForType(e))return a}return this.plugins}_get(e,s,a){const o=s.get(e);if(o===void 0)throw new Error('"'+e+'" is not a registered '+a+".");return o}}var xn=new _w;class Sw{constructor(){this._init=[]}notify(e,s,a,o){s==="beforeInit"&&(this._init=this._createDescriptors(e,!0),this._notify(this._init,e,"install"));const c=o?this._descriptors(e).filter(o):this._descriptors(e),u=this._notify(c,e,s,a);return s==="afterDestroy"&&(this._notify(c,e,"stop"),this._notify(this._init,e,"uninstall")),u}_notify(e,s,a,o){o=o||{};for(const c of e){const u=c.plugin,h=u[a],m=[s,o,c.options];if(Lt(h,m,u)===!1&&o.cancelable)return!1}return!0}invalidate(){wt(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(e){if(this._cache)return this._cache;const s=this._cache=this._createDescriptors(e);return this._notifyStateChanges(e),s}_createDescriptors(e,s){const a=e&&e.config,o=mt(a.options&&a.options.plugins,{}),c=Mw(a);return o===!1&&!s?[]:Tw(e,c,o,s)}_notifyStateChanges(e){const s=this._oldCache||[],a=this._cache,o=(c,u)=>c.filter(h=>!u.some(m=>h.plugin.id===m.plugin.id));this._notify(o(s,a),e,"stop"),this._notify(o(a,s),e,"start")}}function Mw(i){const e={},s=[],a=Object.keys(xn.plugins.items);for(let c=0;c<a.length;c++)s.push(xn.getPlugin(a[c]));const o=i.plugins||[];for(let c=0;c<o.length;c++){const u=o[c];s.indexOf(u)===-1&&(s.push(u),e[u.id]=!0)}return{plugins:s,localIds:e}}function ww(i,e){return!e&&i===!1?null:i===!0?{}:i}function Tw(i,{plugins:e,localIds:s},a,o){const c=[],u=i.getContext();for(const h of e){const m=h.id,g=ww(a[m],o);g!==null&&c.push({plugin:h,options:Aw(i.config,{plugin:h,local:s[m]},g,u)})}return c}function Aw(i,{plugin:e,local:s},a,o){const c=i.pluginScopeKeys(e),u=i.getOptionScopes(a,c);return s&&e.defaults&&u.push(e.defaults),i.createResolver(u,o,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Of(i,e){const s=Zt.datasets[i]||{};return((e.datasets||{})[i]||{}).indexAxis||e.indexAxis||s.indexAxis||"x"}function jw(i,e){let s=i;return i==="_index_"?s=e:i==="_value_"&&(s=e==="x"?"y":"x"),s}function Dw(i,e){return i===e?"_index_":"_value_"}function mb(i){if(i==="x"||i==="y"||i==="r")return i}function Ow(i){if(i==="top"||i==="bottom")return"x";if(i==="left"||i==="right")return"y"}function Ef(i,...e){if(mb(i))return i;for(const s of e){const a=s.axis||Ow(s.position)||i.length>1&&mb(i[0].toLowerCase());if(a)return a}throw new Error(`Cannot determine type of '${i}' axis. Please provide 'axis' or 'position' option.`)}function gb(i,e,s){if(s[e+"AxisID"]===i)return{axis:e}}function Ew(i,e){if(e.data&&e.data.datasets){const s=e.data.datasets.filter(a=>a.xAxisID===i||a.yAxisID===i);if(s.length)return gb(i,"x",s[0])||gb(i,"y",s[0])}return{}}function zw(i,e){const s=Ki[i.type]||{scales:{}},a=e.scales||{},o=Of(i.type,e),c=Object.create(null);return Object.keys(a).forEach(u=>{const h=a[u];if(!vt(h))return console.error(`Invalid scale configuration for scale: ${u}`);if(h._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${u}`);const m=Ef(u,h,Ew(u,i),Zt.scales[h.type]),g=Dw(m,o),p=s.scales||{};c[u]=rl(Object.create(null),[{axis:m},h,p[m],p[g]])}),i.data.datasets.forEach(u=>{const h=u.type||i.type,m=u.indexAxis||Of(h,e),p=(Ki[h]||{}).scales||{};Object.keys(p).forEach(x=>{const v=jw(x,m),S=u[v+"AxisID"]||v;c[S]=c[S]||Object.create(null),rl(c[S],[{axis:v},a[S],p[x]])})}),Object.keys(c).forEach(u=>{const h=c[u];rl(h,[Zt.scales[h.type],Zt.scale])}),c}function $0(i){const e=i.options||(i.options={});e.plugins=mt(e.plugins,{}),e.scales=zw(i,e)}function tx(i){return i=i||{},i.datasets=i.datasets||[],i.labels=i.labels||[],i}function Cw(i){return i=i||{},i.data=tx(i.data),$0(i),i}const pb=new Map,ex=new Set;function cr(i,e){let s=pb.get(i);return s||(s=e(),pb.set(i,s),ex.add(s)),s}const tl=(i,e,s)=>{const a=Xs(e,s);a!==void 0&&i.add(a)};class Rw{constructor(e){this._config=Cw(e),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(e){this._config.type=e}get data(){return this._config.data}set data(e){this._config.data=tx(e)}get options(){return this._config.options}set options(e){this._config.options=e}get plugins(){return this._config.plugins}update(){const e=this._config;this.clearCache(),$0(e)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(e){return cr(e,()=>[[`datasets.${e}`,""]])}datasetAnimationScopeKeys(e,s){return cr(`${e}.transition.${s}`,()=>[[`datasets.${e}.transitions.${s}`,`transitions.${s}`],[`datasets.${e}`,""]])}datasetElementScopeKeys(e,s){return cr(`${e}-${s}`,()=>[[`datasets.${e}.elements.${s}`,`datasets.${e}`,`elements.${s}`,""]])}pluginScopeKeys(e){const s=e.id,a=this.type;return cr(`${a}-plugin-${s}`,()=>[[`plugins.${s}`,...e.additionalOptionScopes||[]]])}_cachedScopes(e,s){const a=this._scopeCache;let o=a.get(e);return(!o||s)&&(o=new Map,a.set(e,o)),o}getOptionScopes(e,s,a){const{options:o,type:c}=this,u=this._cachedScopes(e,a),h=u.get(s);if(h)return h;const m=new Set;s.forEach(p=>{e&&(m.add(e),p.forEach(x=>tl(m,e,x))),p.forEach(x=>tl(m,o,x)),p.forEach(x=>tl(m,Ki[c]||{},x)),p.forEach(x=>tl(m,Zt,x)),p.forEach(x=>tl(m,jf,x))});const g=Array.from(m);return g.length===0&&g.push(Object.create(null)),ex.has(s)&&u.set(s,g),g}chartOptionScopes(){const{options:e,type:s}=this;return[e,Ki[s]||{},Zt.datasets[s]||{},{type:s},Zt,jf]}resolveNamedOptions(e,s,a,o=[""]){const c={$shared:!0},{resolver:u,subPrefixes:h}=bb(this._resolverCache,e,o);let m=u;if(Bw(u,s)){c.$shared=!1,a=pi(a)?a():a;const g=this.createResolver(e,a,h);m=Gs(u,a,g)}for(const g of s)c[g]=m[g];return c}createResolver(e,s,a=[""],o){const{resolver:c}=bb(this._resolverCache,e,a);return vt(s)?Gs(c,s,void 0,o):c}}function bb(i,e,s){let a=i.get(e);a||(a=new Map,i.set(e,a));const o=s.join();let c=a.get(o);return c||(c={resolver:Pf(e,s),subPrefixes:s.filter(h=>!h.toLowerCase().includes("hover"))},a.set(o,c)),c}const kw=i=>vt(i)&&Object.getOwnPropertyNames(i).some(e=>pi(i[e]));function Bw(i,e){const{isScriptable:s,isIndexable:a}=L0(i);for(const o of e){const c=s(o),u=a(o),h=(u||c)&&i[o];if(c&&(pi(h)||kw(h))||u&&te(h))return!0}return!1}var Nw="4.5.0";const Lw=["top","bottom","left","right","chartArea"];function xb(i,e){return i==="top"||i==="bottom"||Lw.indexOf(i)===-1&&e==="x"}function yb(i,e){return function(s,a){return s[i]===a[i]?s[e]-a[e]:s[i]-a[i]}}function vb(i){const e=i.chart,s=e.options.animation;e.notifyPlugins("afterRender"),Lt(s&&s.onComplete,[i],e)}function Hw(i){const e=i.chart,s=e.options.animation;Lt(s&&s.onProgress,[i],e)}function nx(i){return $f()&&typeof i=="string"?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}const pr={},_b=i=>{const e=nx(i);return Object.values(pr).filter(s=>s.canvas===e).pop()};function Uw(i,e,s){const a=Object.keys(i);for(const o of a){const c=+o;if(c>=e){const u=i[o];delete i[o],(s>0||c>e)&&(i[c+s]=u)}}}function qw(i,e,s,a){return!s||i.type==="mouseout"?null:a?e:i}let zl=class{static defaults=Zt;static instances=pr;static overrides=Ki;static registry=xn;static version=Nw;static getChart=_b;static register(...e){xn.add(...e),Sb()}static unregister(...e){xn.remove(...e),Sb()}constructor(e,s){const a=this.config=new Rw(s),o=nx(e),c=_b(o);if(c)throw new Error("Canvas is already in use. Chart with ID '"+c.id+"' must be destroyed before the canvas with ID '"+c.canvas.id+"' can be reused.");const u=a.createResolver(a.chartOptionScopes(),this.getContext());this.platform=new(a.platform||sw(o)),this.platform.updateConfig(a);const h=this.platform.acquireContext(o,u.aspectRatio),m=h&&h.canvas,g=m&&m.height,p=m&&m.width;if(this.id=RS(),this.ctx=h,this.canvas=m,this.width=p,this.height=g,this._options=u,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new Sw,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=$S(x=>this.update(x),u.resizeDelay||0),this._dataChanges=[],pr[this.id]=this,!h||!m){console.error("Failed to create chart: can't acquire context from the given item");return}Hn.listen(this,"complete",vb),Hn.listen(this,"progress",Hw),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:e,maintainAspectRatio:s},width:a,height:o,_aspectRatio:c}=this;return wt(e)?s&&c?c:o?a/o:null:e}get data(){return this.config.data}set data(e){this.config.data=e}get options(){return this._options}set options(e){this.config.options=e}get registry(){return xn}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Xp(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Vp(this.canvas,this.ctx),this}stop(){return Hn.stop(this),this}resize(e,s){Hn.running(this)?this._resizeBeforeDraw={width:e,height:s}:this._resize(e,s)}_resize(e,s){const a=this.options,o=this.canvas,c=a.maintainAspectRatio&&this.aspectRatio,u=this.platform.getMaximumSize(o,e,s,c),h=a.devicePixelRatio||this.platform.getDevicePixelRatio(),m=this.width?"resize":"attach";this.width=u.width,this.height=u.height,this._aspectRatio=this.aspectRatio,Xp(this,h,!0)&&(this.notifyPlugins("resize",{size:u}),Lt(a.onResize,[this,u],this),this.attached&&this._doResize(m)&&this.render())}ensureScalesHaveIDs(){const s=this.options.scales||{};Dt(s,(a,o)=>{a.id=o})}buildOrUpdateScales(){const e=this.options,s=e.scales,a=this.scales,o=Object.keys(a).reduce((u,h)=>(u[h]=!1,u),{});let c=[];s&&(c=c.concat(Object.keys(s).map(u=>{const h=s[u],m=Ef(u,h),g=m==="r",p=m==="x";return{options:h,dposition:g?"chartArea":p?"bottom":"left",dtype:g?"radialLinear":p?"category":"linear"}}))),Dt(c,u=>{const h=u.options,m=h.id,g=Ef(m,h),p=mt(h.type,u.dtype);(h.position===void 0||xb(h.position,g)!==xb(u.dposition))&&(h.position=u.dposition),o[m]=!0;let x=null;if(m in a&&a[m].type===p)x=a[m];else{const v=xn.getScale(p);x=new v({id:m,type:p,ctx:this.ctx,chart:this}),a[x.id]=x}x.init(h,e)}),Dt(o,(u,h)=>{u||delete a[h]}),Dt(a,u=>{Ie.configure(this,u,u.options),Ie.addBox(this,u)})}_updateMetasets(){const e=this._metasets,s=this.data.datasets.length,a=e.length;if(e.sort((o,c)=>o.index-c.index),a>s){for(let o=s;o<a;++o)this._destroyDatasetMeta(o);e.splice(s,a-s)}this._sortedMetasets=e.slice(0).sort(yb("order","index"))}_removeUnreferencedMetasets(){const{_metasets:e,data:{datasets:s}}=this;e.length>s.length&&delete this._stacks,e.forEach((a,o)=>{s.filter(c=>c===a._dataset).length===0&&this._destroyDatasetMeta(o)})}buildOrUpdateControllers(){const e=[],s=this.data.datasets;let a,o;for(this._removeUnreferencedMetasets(),a=0,o=s.length;a<o;a++){const c=s[a];let u=this.getDatasetMeta(a);const h=c.type||this.config.type;if(u.type&&u.type!==h&&(this._destroyDatasetMeta(a),u=this.getDatasetMeta(a)),u.type=h,u.indexAxis=c.indexAxis||Of(h,this.options),u.order=c.order||0,u.index=a,u.label=""+c.label,u.visible=this.isDatasetVisible(a),u.controller)u.controller.updateIndex(a),u.controller.linkScales();else{const m=xn.getController(h),{datasetElementType:g,dataElementType:p}=Zt.datasets[h];Object.assign(m,{dataElementType:xn.getElement(p),datasetElementType:g&&xn.getElement(g)}),u.controller=new m(this,a),e.push(u.controller)}}return this._updateMetasets(),e}_resetElements(){Dt(this.data.datasets,(e,s)=>{this.getDatasetMeta(s).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(e){const s=this.config;s.update();const a=this._options=s.createResolver(s.chartOptionScopes(),this.getContext()),o=this._animationsDisabled=!a.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:e,cancelable:!0})===!1)return;const c=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let u=0;for(let g=0,p=this.data.datasets.length;g<p;g++){const{controller:x}=this.getDatasetMeta(g),v=!o&&c.indexOf(x)===-1;x.buildOrUpdateElements(v),u=Math.max(+x.getMaxOverflow(),u)}u=this._minPadding=a.layout.autoPadding?u:0,this._updateLayout(u),o||Dt(c,g=>{g.reset()}),this._updateDatasets(e),this.notifyPlugins("afterUpdate",{mode:e}),this._layers.sort(yb("z","_idx"));const{_active:h,_lastEvent:m}=this;m?this._eventHandler(m,!0):h.length&&this._updateHoverStyles(h,h,!0),this.render()}_updateScales(){Dt(this.scales,e=>{Ie.removeBox(this,e)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const e=this.options,s=new Set(Object.keys(this._listeners)),a=new Set(e.events);(!zp(s,a)||!!this._responsiveListeners!==e.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:e}=this,s=this._getUniformDataChanges()||[];for(const{method:a,start:o,count:c}of s){const u=a==="_removeElements"?-c:c;Uw(e,o,u)}}_getUniformDataChanges(){const e=this._dataChanges;if(!e||!e.length)return;this._dataChanges=[];const s=this.data.datasets.length,a=c=>new Set(e.filter(u=>u[0]===c).map((u,h)=>h+","+u.splice(1).join(","))),o=a(0);for(let c=1;c<s;c++)if(!zp(o,a(c)))return;return Array.from(o).map(c=>c.split(",")).map(c=>({method:c[1],start:+c[2],count:+c[3]}))}_updateLayout(e){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;Ie.update(this,this.width,this.height,e);const s=this.chartArea,a=s.width<=0||s.height<=0;this._layers=[],Dt(this.boxes,o=>{a&&o.position==="chartArea"||(o.configure&&o.configure(),this._layers.push(...o._layers()))},this),this._layers.forEach((o,c)=>{o._idx=c}),this.notifyPlugins("afterLayout")}_updateDatasets(e){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:e,cancelable:!0})!==!1){for(let s=0,a=this.data.datasets.length;s<a;++s)this.getDatasetMeta(s).controller.configure();for(let s=0,a=this.data.datasets.length;s<a;++s)this._updateDataset(s,pi(e)?e({datasetIndex:s}):e);this.notifyPlugins("afterDatasetsUpdate",{mode:e})}}_updateDataset(e,s){const a=this.getDatasetMeta(e),o={meta:a,index:e,mode:s,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",o)!==!1&&(a.controller._update(s),o.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",o))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(Hn.has(this)?this.attached&&!Hn.running(this)&&Hn.start(this):(this.draw(),vb({chart:this})))}draw(){let e;if(this._resizeBeforeDraw){const{width:a,height:o}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(a,o)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const s=this._layers;for(e=0;e<s.length&&s[e].z<=0;++e)s[e].draw(this.chartArea);for(this._drawDatasets();e<s.length;++e)s[e].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(e){const s=this._sortedMetasets,a=[];let o,c;for(o=0,c=s.length;o<c;++o){const u=s[o];(!e||u.visible)&&a.push(u)}return a}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const e=this.getSortedVisibleDatasetMetas();for(let s=e.length-1;s>=0;--s)this._drawDataset(e[s]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(e){const s=this.ctx,a={meta:e,index:e.index,cancelable:!0},o=aM(this,e);this.notifyPlugins("beforeDatasetDraw",a)!==!1&&(o&&Kf(s,o),e.controller.draw(),o&&Wf(s),a.cancelable=!1,this.notifyPlugins("afterDatasetDraw",a))}isPointInArea(e){return Sl(e,this.chartArea,this._minPadding)}getElementsAtEventForMode(e,s,a,o){const c=LM.modes[s];return typeof c=="function"?c(this,e,a,o):[]}getDatasetMeta(e){const s=this.data.datasets[e],a=this._metasets;let o=a.filter(c=>c&&c._dataset===s).pop();return o||(o={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:s&&s.order||0,index:e,_dataset:s,_parsed:[],_sorted:!1},a.push(o)),o}getContext(){return this.$context||(this.$context=Wi(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(e){const s=this.data.datasets[e];if(!s)return!1;const a=this.getDatasetMeta(e);return typeof a.hidden=="boolean"?!a.hidden:!s.hidden}setDatasetVisibility(e,s){const a=this.getDatasetMeta(e);a.hidden=!s}toggleDataVisibility(e){this._hiddenIndices[e]=!this._hiddenIndices[e]}getDataVisibility(e){return!this._hiddenIndices[e]}_updateVisibility(e,s,a){const o=a?"show":"hide",c=this.getDatasetMeta(e),u=c.controller._resolveAnimations(void 0,o);vl(s)?(c.data[s].hidden=!a,this.update()):(this.setDatasetVisibility(e,a),u.update(c,{visible:a}),this.update(h=>h.datasetIndex===e?o:void 0))}hide(e,s){this._updateVisibility(e,s,!1)}show(e,s){this._updateVisibility(e,s,!0)}_destroyDatasetMeta(e){const s=this._metasets[e];s&&s.controller&&s.controller._destroy(),delete this._metasets[e]}_stop(){let e,s;for(this.stop(),Hn.remove(this),e=0,s=this.data.datasets.length;e<s;++e)this._destroyDatasetMeta(e)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:e,ctx:s}=this;this._stop(),this.config.clearCache(),e&&(this.unbindEvents(),Vp(e,s),this.platform.releaseContext(s),this.canvas=null,this.ctx=null),delete pr[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...e){return this.canvas.toDataURL(...e)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const e=this._listeners,s=this.platform,a=(c,u)=>{s.addEventListener(this,c,u),e[c]=u},o=(c,u,h)=>{c.offsetX=u,c.offsetY=h,this._eventHandler(c)};Dt(this.options.events,c=>a(c,o))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const e=this._responsiveListeners,s=this.platform,a=(m,g)=>{s.addEventListener(this,m,g),e[m]=g},o=(m,g)=>{e[m]&&(s.removeEventListener(this,m,g),delete e[m])},c=(m,g)=>{this.canvas&&this.resize(m,g)};let u;const h=()=>{o("attach",h),this.attached=!0,this.resize(),a("resize",c),a("detach",u)};u=()=>{this.attached=!1,o("resize",c),this._stop(),this._resize(0,0),a("attach",h)},s.isAttached(this.canvas)?h():u()}unbindEvents(){Dt(this._listeners,(e,s)=>{this.platform.removeEventListener(this,s,e)}),this._listeners={},Dt(this._responsiveListeners,(e,s)=>{this.platform.removeEventListener(this,s,e)}),this._responsiveListeners=void 0}updateHoverStyle(e,s,a){const o=a?"set":"remove";let c,u,h,m;for(s==="dataset"&&(c=this.getDatasetMeta(e[0].datasetIndex),c.controller["_"+o+"DatasetHoverStyle"]()),h=0,m=e.length;h<m;++h){u=e[h];const g=u&&this.getDatasetMeta(u.datasetIndex).controller;g&&g[o+"HoverStyle"](u.element,u.datasetIndex,u.index)}}getActiveElements(){return this._active||[]}setActiveElements(e){const s=this._active||[],a=e.map(({datasetIndex:c,index:u})=>{const h=this.getDatasetMeta(c);if(!h)throw new Error("No dataset found at index "+c);return{datasetIndex:c,element:h.data[u],index:u}});!yr(a,s)&&(this._active=a,this._lastEvent=null,this._updateHoverStyles(a,s))}notifyPlugins(e,s,a){return this._plugins.notify(this,e,s,a)}isPluginEnabled(e){return this._plugins._cache.filter(s=>s.plugin.id===e).length===1}_updateHoverStyles(e,s,a){const o=this.options.hover,c=(m,g)=>m.filter(p=>!g.some(x=>p.datasetIndex===x.datasetIndex&&p.index===x.index)),u=c(s,e),h=a?e:c(e,s);u.length&&this.updateHoverStyle(u,o.mode,!1),h.length&&o.mode&&this.updateHoverStyle(h,o.mode,!0)}_eventHandler(e,s){const a={event:e,replay:s,cancelable:!0,inChartArea:this.isPointInArea(e)},o=u=>(u.options.events||this.options.events).includes(e.native.type);if(this.notifyPlugins("beforeEvent",a,o)===!1)return;const c=this._handleEvent(e,s,a.inChartArea);return a.cancelable=!1,this.notifyPlugins("afterEvent",a,o),(c||a.changed)&&this.render(),this}_handleEvent(e,s,a){const{_active:o=[],options:c}=this,u=s,h=this._getActiveElements(e,o,a,u),m=US(e),g=qw(e,this._lastEvent,a,m);a&&(this._lastEvent=null,Lt(c.onHover,[e,h,this],this),m&&Lt(c.onClick,[e,h,this],this));const p=!yr(h,o);return(p||s)&&(this._active=h,this._updateHoverStyles(h,o,s)),this._lastEvent=g,p}_getActiveElements(e,s,a,o){if(e.type==="mouseout")return[];if(!a)return s;const c=this.options.hover;return this.getElementsAtEventForMode(e,c.mode,c,o)}};function Sb(){return Dt(zl.instances,i=>i._plugins.invalidate())}function ix(i,e,s=e){i.lineCap=mt(s.borderCapStyle,e.borderCapStyle),i.setLineDash(mt(s.borderDash,e.borderDash)),i.lineDashOffset=mt(s.borderDashOffset,e.borderDashOffset),i.lineJoin=mt(s.borderJoinStyle,e.borderJoinStyle),i.lineWidth=mt(s.borderWidth,e.borderWidth),i.strokeStyle=mt(s.borderColor,e.borderColor)}function Vw(i,e,s){i.lineTo(s.x,s.y)}function Fw(i){return i.stepped?d2:i.tension||i.cubicInterpolationMode==="monotone"?m2:Vw}function sx(i,e,s={}){const a=i.length,{start:o=0,end:c=a-1}=s,{start:u,end:h}=e,m=Math.max(o,u),g=Math.min(c,h),p=o<u&&c<u||o>h&&c>h;return{count:a,start:m,loop:e.loop,ilen:g<m&&!p?a+g-m:g-m}}function Yw(i,e,s,a){const{points:o,options:c}=e,{count:u,start:h,loop:m,ilen:g}=sx(o,s,a),p=Fw(c);let{move:x=!0,reverse:v}=a||{},S,M,T;for(S=0;S<=g;++S)M=o[(h+(v?g-S:S))%u],!M.skip&&(x?(i.moveTo(M.x,M.y),x=!1):p(i,T,M,v,c.stepped),T=M);return m&&(M=o[(h+(v?g:0))%u],p(i,T,M,v,c.stepped)),!!m}function Xw(i,e,s,a){const o=e.points,{count:c,start:u,ilen:h}=sx(o,s,a),{move:m=!0,reverse:g}=a||{};let p=0,x=0,v,S,M,T,w,O;const H=G=>(u+(g?h-G:G))%c,q=()=>{T!==w&&(i.lineTo(p,w),i.lineTo(p,T),i.lineTo(p,O))};for(m&&(S=o[H(0)],i.moveTo(S.x,S.y)),v=0;v<=h;++v){if(S=o[H(v)],S.skip)continue;const G=S.x,V=S.y,F=G|0;F===M?(V<T?T=V:V>w&&(w=V),p=(x*p+G)/++x):(q(),i.lineTo(G,V),M=F,x=0,T=w=V),O=V}q()}function zf(i){const e=i.options,s=e.borderDash&&e.borderDash.length;return!i._decimated&&!i._loop&&!e.tension&&e.cubicInterpolationMode!=="monotone"&&!e.stepped&&!s?Xw:Yw}function Gw(i){return i.stepped?Q2:i.tension||i.cubicInterpolationMode==="monotone"?Z2:qi}function Qw(i,e,s,a){let o=e._path;o||(o=e._path=new Path2D,e.path(o,s,a)&&o.closePath()),ix(i,e.options),i.stroke(o)}function Zw(i,e,s,a){const{segments:o,options:c}=e,u=zf(e);for(const h of o)ix(i,c,h.style),i.beginPath(),u(i,e,h,{start:s,end:s+a-1})&&i.closePath(),i.stroke()}const Kw=typeof Path2D=="function";function Ww(i,e,s,a){Kw&&!e.options.segment?Qw(i,e,s,a):Zw(i,e,s,a)}class Pw extends bi{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:e=>e!=="borderDash"&&e!=="fill"};constructor(e){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,e&&Object.assign(this,e)}updateControlPoints(e,s){const a=this.options;if((a.tension||a.cubicInterpolationMode==="monotone")&&!a.stepped&&!this._pointsUpdated){const o=a.spanGaps?this._loop:this._fullLoop;H2(this._points,a,e,o,s),this._pointsUpdated=!0}}set points(e){this._points=e,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=eM(this,this.options.segment))}first(){const e=this.segments,s=this.points;return e.length&&s[e[0].start]}last(){const e=this.segments,s=this.points,a=e.length;return a&&s[e[a-1].end]}interpolate(e,s){const a=this.options,o=e[s],c=this.points,u=I2(this,{property:s,start:o,end:o});if(!u.length)return;const h=[],m=Gw(a);let g,p;for(g=0,p=u.length;g<p;++g){const{start:x,end:v}=u[g],S=c[x],M=c[v];if(S===M){h.push(S);continue}const T=Math.abs((o-S[s])/(M[s]-S[s])),w=m(S,M,T,a.stepped);w[s]=e[s],h.push(w)}return h.length===1?h[0]:h}pathSegment(e,s,a){return zf(this)(e,this,s,a)}path(e,s,a){const o=this.segments,c=zf(this);let u=this._loop;s=s||0,a=a||this.points.length-s;for(const h of o)u&=c(e,this,h,{start:s,end:s+a-1});return!!u}draw(e,s,a,o){const c=this.options||{};(this.points||[]).length&&c.borderWidth&&(e.save(),Ww(e,this,a,o),e.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function Mb(i,e,s,a){const o=i.options,{[s]:c}=i.getProps([s],a);return Math.abs(e-c)<o.radius+o.hitRadius}class Jw extends bi{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(e){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,e&&Object.assign(this,e)}inRange(e,s,a){const o=this.options,{x:c,y:u}=this.getProps(["x","y"],a);return Math.pow(e-c,2)+Math.pow(s-u,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(e,s){return Mb(this,e,"x",s)}inYRange(e,s){return Mb(this,e,"y",s)}getCenterPoint(e){const{x:s,y:a}=this.getProps(["x","y"],e);return{x:s,y:a}}size(e){e=e||this.options||{};let s=e.radius||0;s=Math.max(s,s&&e.hoverRadius||0);const a=s&&e.borderWidth||0;return(s+a)*2}draw(e,s){const a=this.options;this.skip||a.radius<.1||!Sl(this,s,this.size(a)/2)||(e.strokeStyle=a.borderColor,e.lineWidth=a.borderWidth,e.fillStyle=a.backgroundColor,Df(e,a,this.x,this.y))}getRange(){const e=this.options||{};return e.radius+e.hitRadius}}function ax(i,e){const{x:s,y:a,base:o,width:c,height:u}=i.getProps(["x","y","base","width","height"],e);let h,m,g,p,x;return i.horizontal?(x=u/2,h=Math.min(s,o),m=Math.max(s,o),g=a-x,p=a+x):(x=c/2,h=s-x,m=s+x,g=Math.min(a,o),p=Math.max(a,o)),{left:h,top:g,right:m,bottom:p}}function mi(i,e,s,a){return i?0:He(e,s,a)}function Iw(i,e,s){const a=i.options.borderWidth,o=i.borderSkipped,c=N0(a);return{t:mi(o.top,c.top,0,s),r:mi(o.right,c.right,0,e),b:mi(o.bottom,c.bottom,0,s),l:mi(o.left,c.left,0,e)}}function $w(i,e,s){const{enableBorderRadius:a}=i.getProps(["enableBorderRadius"]),o=i.options.borderRadius,c=Us(o),u=Math.min(e,s),h=i.borderSkipped,m=a||vt(o);return{topLeft:mi(!m||h.top||h.left,c.topLeft,0,u),topRight:mi(!m||h.top||h.right,c.topRight,0,u),bottomLeft:mi(!m||h.bottom||h.left,c.bottomLeft,0,u),bottomRight:mi(!m||h.bottom||h.right,c.bottomRight,0,u)}}function tT(i){const e=ax(i),s=e.right-e.left,a=e.bottom-e.top,o=Iw(i,s/2,a/2),c=$w(i,s/2,a/2);return{outer:{x:e.left,y:e.top,w:s,h:a,radius:c},inner:{x:e.left+o.l,y:e.top+o.t,w:s-o.l-o.r,h:a-o.t-o.b,radius:{topLeft:Math.max(0,c.topLeft-Math.max(o.t,o.l)),topRight:Math.max(0,c.topRight-Math.max(o.t,o.r)),bottomLeft:Math.max(0,c.bottomLeft-Math.max(o.b,o.l)),bottomRight:Math.max(0,c.bottomRight-Math.max(o.b,o.r))}}}}function pf(i,e,s,a){const o=e===null,c=s===null,h=i&&!(o&&c)&&ax(i,a);return h&&(o||Yi(e,h.left,h.right))&&(c||Yi(s,h.top,h.bottom))}function eT(i){return i.topLeft||i.topRight||i.bottomLeft||i.bottomRight}function nT(i,e){i.rect(e.x,e.y,e.w,e.h)}function bf(i,e,s={}){const a=i.x!==s.x?-e:0,o=i.y!==s.y?-e:0,c=(i.x+i.w!==s.x+s.w?e:0)-a,u=(i.y+i.h!==s.y+s.h?e:0)-o;return{x:i.x+a,y:i.y+o,w:i.w+c,h:i.h+u,radius:i.radius}}class lx extends bi{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(e){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,e&&Object.assign(this,e)}draw(e){const{inflateAmount:s,options:{borderColor:a,backgroundColor:o}}=this,{inner:c,outer:u}=tT(this),h=eT(u.radius)?Sr:nT;e.save(),(u.w!==c.w||u.h!==c.h)&&(e.beginPath(),h(e,bf(u,s,c)),e.clip(),h(e,bf(c,-s,u)),e.fillStyle=a,e.fill("evenodd")),e.beginPath(),h(e,bf(c,s)),e.fillStyle=o,e.fill(),e.restore()}inRange(e,s,a){return pf(this,e,s,a)}inXRange(e,s){return pf(this,e,null,s)}inYRange(e,s){return pf(this,null,e,s)}getCenterPoint(e){const{x:s,y:a,base:o,horizontal:c}=this.getProps(["x","y","base","horizontal"],e);return{x:c?(s+o)/2:s,y:c?a:(a+o)/2}}getRange(e){return e==="x"?this.width/2:this.height/2}}const wb=(i,e)=>{let{boxHeight:s=e,boxWidth:a=e}=i;return i.usePointStyle&&(s=Math.min(s,e),a=i.pointStyleWidth||Math.min(a,e)),{boxWidth:a,boxHeight:s,itemHeight:Math.max(e,s)}},iT=(i,e)=>i!==null&&e!==null&&i.datasetIndex===e.datasetIndex&&i.index===e.index;class Tb extends bi{constructor(e){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,s,a){this.maxWidth=e,this.maxHeight=s,this._margins=a,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const e=this.options.labels||{};let s=Lt(e.generateLabels,[this.chart],this)||[];e.filter&&(s=s.filter(a=>e.filter(a,this.chart.data))),e.sort&&(s=s.sort((a,o)=>e.sort(a,o,this.chart.data))),this.options.reverse&&s.reverse(),this.legendItems=s}fit(){const{options:e,ctx:s}=this;if(!e.display){this.width=this.height=0;return}const a=e.labels,o=be(a.font),c=o.size,u=this._computeTitleHeight(),{boxWidth:h,itemHeight:m}=wb(a,c);let g,p;s.font=o.string,this.isHorizontal()?(g=this.maxWidth,p=this._fitRows(u,c,h,m)+10):(p=this.maxHeight,g=this._fitCols(u,o,h,m)+10),this.width=Math.min(g,e.maxWidth||this.maxWidth),this.height=Math.min(p,e.maxHeight||this.maxHeight)}_fitRows(e,s,a,o){const{ctx:c,maxWidth:u,options:{labels:{padding:h}}}=this,m=this.legendHitBoxes=[],g=this.lineWidths=[0],p=o+h;let x=e;c.textAlign="left",c.textBaseline="middle";let v=-1,S=-p;return this.legendItems.forEach((M,T)=>{const w=a+s/2+c.measureText(M.text).width;(T===0||g[g.length-1]+w+2*h>u)&&(x+=p,g[g.length-(T>0?0:1)]=0,S+=p,v++),m[T]={left:0,top:S,row:v,width:w,height:o},g[g.length-1]+=w+h}),x}_fitCols(e,s,a,o){const{ctx:c,maxHeight:u,options:{labels:{padding:h}}}=this,m=this.legendHitBoxes=[],g=this.columnSizes=[],p=u-e;let x=h,v=0,S=0,M=0,T=0;return this.legendItems.forEach((w,O)=>{const{itemWidth:H,itemHeight:q}=sT(a,s,c,w,o);O>0&&S+q+2*h>p&&(x+=v+h,g.push({width:v,height:S}),M+=v+h,T++,v=S=0),m[O]={left:M,top:S,col:T,width:H,height:q},v=Math.max(v,H),S+=q+h}),x+=v,g.push({width:v,height:S}),x}adjustHitBoxes(){if(!this.options.display)return;const e=this._computeTitleHeight(),{legendHitBoxes:s,options:{align:a,labels:{padding:o},rtl:c}}=this,u=qs(c,this.left,this.width);if(this.isHorizontal()){let h=0,m=de(a,this.left+o,this.right-this.lineWidths[h]);for(const g of s)h!==g.row&&(h=g.row,m=de(a,this.left+o,this.right-this.lineWidths[h])),g.top+=this.top+e+o,g.left=u.leftForLtr(u.x(m),g.width),m+=g.width+o}else{let h=0,m=de(a,this.top+e+o,this.bottom-this.columnSizes[h].height);for(const g of s)g.col!==h&&(h=g.col,m=de(a,this.top+e+o,this.bottom-this.columnSizes[h].height)),g.top=m,g.left+=this.left+o,g.left=u.leftForLtr(u.x(g.left),g.width),m+=g.height+o}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const e=this.ctx;Kf(e,this),this._draw(),Wf(e)}}_draw(){const{options:e,columnSizes:s,lineWidths:a,ctx:o}=this,{align:c,labels:u}=e,h=Zt.color,m=qs(e.rtl,this.left,this.width),g=be(u.font),{padding:p}=u,x=g.size,v=x/2;let S;this.drawTitle(),o.textAlign=m.textAlign("left"),o.textBaseline="middle",o.lineWidth=.5,o.font=g.string;const{boxWidth:M,boxHeight:T,itemHeight:w}=wb(u,x),O=function(F,X,Z){if(isNaN(M)||M<=0||isNaN(T)||T<0)return;o.save();const I=mt(Z.lineWidth,1);if(o.fillStyle=mt(Z.fillStyle,h),o.lineCap=mt(Z.lineCap,"butt"),o.lineDashOffset=mt(Z.lineDashOffset,0),o.lineJoin=mt(Z.lineJoin,"miter"),o.lineWidth=I,o.strokeStyle=mt(Z.strokeStyle,h),o.setLineDash(mt(Z.lineDash,[])),u.usePointStyle){const rt={radius:T*Math.SQRT2/2,pointStyle:Z.pointStyle,rotation:Z.rotation,borderWidth:I},st=m.xPlus(F,M/2),nt=X+v;k0(o,rt,st,nt,u.pointStyleWidth&&M)}else{const rt=X+Math.max((x-T)/2,0),st=m.leftForLtr(F,M),nt=Us(Z.borderRadius);o.beginPath(),Object.values(nt).some(Nt=>Nt!==0)?Sr(o,{x:st,y:rt,w:M,h:T,radius:nt}):o.rect(st,rt,M,T),o.fill(),I!==0&&o.stroke()}o.restore()},H=function(F,X,Z){Ml(o,Z.text,F,X+w/2,g,{strikethrough:Z.hidden,textAlign:m.textAlign(Z.textAlign)})},q=this.isHorizontal(),G=this._computeTitleHeight();q?S={x:de(c,this.left+p,this.right-a[0]),y:this.top+p+G,line:0}:S={x:this.left+p,y:de(c,this.top+G+p,this.bottom-s[0].height),line:0},F0(this.ctx,e.textDirection);const V=w+p;this.legendItems.forEach((F,X)=>{o.strokeStyle=F.fontColor,o.fillStyle=F.fontColor;const Z=o.measureText(F.text).width,I=m.textAlign(F.textAlign||(F.textAlign=u.textAlign)),rt=M+v+Z;let st=S.x,nt=S.y;m.setWidth(this.width),q?X>0&&st+rt+p>this.right&&(nt=S.y+=V,S.line++,st=S.x=de(c,this.left+p,this.right-a[S.line])):X>0&&nt+V>this.bottom&&(st=S.x=st+s[S.line].width+p,S.line++,nt=S.y=de(c,this.top+G+p,this.bottom-s[S.line].height));const Nt=m.x(st);if(O(Nt,nt,F),st=t2(I,st+M+v,q?st+rt:this.right,e.rtl),H(m.x(st),nt,F),q)S.x+=rt+p;else if(typeof F.text!="string"){const Ht=g.lineHeight;S.y+=ox(F,Ht)+p}else S.y+=V}),Y0(this.ctx,e.textDirection)}drawTitle(){const e=this.options,s=e.title,a=be(s.font),o=en(s.padding);if(!s.display)return;const c=qs(e.rtl,this.left,this.width),u=this.ctx,h=s.position,m=a.size/2,g=o.top+m;let p,x=this.left,v=this.width;if(this.isHorizontal())v=Math.max(...this.lineWidths),p=this.top+g,x=de(e.align,x,this.right-v);else{const M=this.columnSizes.reduce((T,w)=>Math.max(T,w.height),0);p=g+de(e.align,this.top,this.bottom-M-e.labels.padding-this._computeTitleHeight())}const S=de(h,x,x+v);u.textAlign=c.textAlign(Qf(h)),u.textBaseline="middle",u.strokeStyle=s.color,u.fillStyle=s.color,u.font=a.string,Ml(u,s.text,S,p,a)}_computeTitleHeight(){const e=this.options.title,s=be(e.font),a=en(e.padding);return e.display?s.lineHeight+a.height:0}_getLegendItemAt(e,s){let a,o,c;if(Yi(e,this.left,this.right)&&Yi(s,this.top,this.bottom)){for(c=this.legendHitBoxes,a=0;a<c.length;++a)if(o=c[a],Yi(e,o.left,o.left+o.width)&&Yi(s,o.top,o.top+o.height))return this.legendItems[a]}return null}handleEvent(e){const s=this.options;if(!oT(e.type,s))return;const a=this._getLegendItemAt(e.x,e.y);if(e.type==="mousemove"||e.type==="mouseout"){const o=this._hoveredItem,c=iT(o,a);o&&!c&&Lt(s.onLeave,[e,o,this],this),this._hoveredItem=a,a&&!c&&Lt(s.onHover,[e,a,this],this)}else a&&Lt(s.onClick,[e,a,this],this)}}function sT(i,e,s,a,o){const c=aT(a,i,e,s),u=lT(o,a,e.lineHeight);return{itemWidth:c,itemHeight:u}}function aT(i,e,s,a){let o=i.text;return o&&typeof o!="string"&&(o=o.reduce((c,u)=>c.length>u.length?c:u)),e+s.size/2+a.measureText(o).width}function lT(i,e,s){let a=i;return typeof e.text!="string"&&(a=ox(e,s)),a}function ox(i,e){const s=i.text?i.text.length:0;return e*s}function oT(i,e){return!!((i==="mousemove"||i==="mouseout")&&(e.onHover||e.onLeave)||e.onClick&&(i==="click"||i==="mouseup"))}var rx={id:"legend",_element:Tb,start(i,e,s){const a=i.legend=new Tb({ctx:i.ctx,options:s,chart:i});Ie.configure(i,a,s),Ie.addBox(i,a)},stop(i){Ie.removeBox(i,i.legend),delete i.legend},beforeUpdate(i,e,s){const a=i.legend;Ie.configure(i,a,s),a.options=s},afterUpdate(i){const e=i.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(i,e){e.replay||i.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(i,e,s){const a=e.datasetIndex,o=s.chart;o.isDatasetVisible(a)?(o.hide(a),e.hidden=!0):(o.show(a),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:i=>i.chart.options.color,boxWidth:40,padding:10,generateLabels(i){const e=i.data.datasets,{labels:{usePointStyle:s,pointStyle:a,textAlign:o,color:c,useBorderRadius:u,borderRadius:h}}=i.legend.options;return i._getSortedDatasetMetas().map(m=>{const g=m.controller.getStyle(s?0:void 0),p=en(g.borderWidth);return{text:e[m.index].label,fillStyle:g.backgroundColor,fontColor:c,hidden:!m.visible,lineCap:g.borderCapStyle,lineDash:g.borderDash,lineDashOffset:g.borderDashOffset,lineJoin:g.borderJoinStyle,lineWidth:(p.width+p.height)/4,strokeStyle:g.borderColor,pointStyle:a||g.pointStyle,rotation:g.rotation,textAlign:o||g.textAlign,borderRadius:u&&(h||g.borderRadius),datasetIndex:m.index}},this)}},title:{color:i=>i.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:i=>!i.startsWith("on"),labels:{_scriptable:i=>!["generateLabels","filter","sort"].includes(i)}}};class cx extends bi{constructor(e){super(),this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,s){const a=this.options;if(this.left=0,this.top=0,!a.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=e,this.height=this.bottom=s;const o=te(a.text)?a.text.length:1;this._padding=en(a.padding);const c=o*be(a.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=c:this.width=c}isHorizontal(){const e=this.options.position;return e==="top"||e==="bottom"}_drawArgs(e){const{top:s,left:a,bottom:o,right:c,options:u}=this,h=u.align;let m=0,g,p,x;return this.isHorizontal()?(p=de(h,a,c),x=s+e,g=c-a):(u.position==="left"?(p=a+e,x=de(h,o,s),m=ae*-.5):(p=c-e,x=de(h,s,o),m=ae*.5),g=o-s),{titleX:p,titleY:x,maxWidth:g,rotation:m}}draw(){const e=this.ctx,s=this.options;if(!s.display)return;const a=be(s.font),c=a.lineHeight/2+this._padding.top,{titleX:u,titleY:h,maxWidth:m,rotation:g}=this._drawArgs(c);Ml(e,s.text,0,0,a,{color:s.color,maxWidth:m,rotation:g,textAlign:Qf(s.align),textBaseline:"middle",translation:[u,h]})}}function rT(i,e){const s=new cx({ctx:i.ctx,options:e,chart:i});Ie.configure(i,s,e),Ie.addBox(i,s),i.titleBlock=s}var ux={id:"title",_element:cx,start(i,e,s){rT(i,s)},stop(i){const e=i.titleBlock;Ie.removeBox(i,e),delete i.titleBlock},beforeUpdate(i,e,s){const a=i.titleBlock;Ie.configure(i,a,s),a.options=s},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const ol={average(i){if(!i.length)return!1;let e,s,a=new Set,o=0,c=0;for(e=0,s=i.length;e<s;++e){const h=i[e].element;if(h&&h.hasValue()){const m=h.tooltipPosition();a.add(m.x),o+=m.y,++c}}return c===0||a.size===0?!1:{x:[...a].reduce((h,m)=>h+m)/a.size,y:o/c}},nearest(i,e){if(!i.length)return!1;let s=e.x,a=e.y,o=Number.POSITIVE_INFINITY,c,u,h;for(c=0,u=i.length;c<u;++c){const m=i[c].element;if(m&&m.hasValue()){const g=m.getCenterPoint(),p=Af(e,g);p<o&&(o=p,h=m)}}if(h){const m=h.tooltipPosition();s=m.x,a=m.y}return{x:s,y:a}}};function bn(i,e){return e&&(te(e)?Array.prototype.push.apply(i,e):i.push(e)),i}function Un(i){return(typeof i=="string"||i instanceof String)&&i.indexOf(`
`)>-1?i.split(`
`):i}function cT(i,e){const{element:s,datasetIndex:a,index:o}=e,c=i.getDatasetMeta(a).controller,{label:u,value:h}=c.getLabelAndValue(o);return{chart:i,label:u,parsed:c.getParsed(o),raw:i.data.datasets[a].data[o],formattedValue:h,dataset:c.getDataset(),dataIndex:o,datasetIndex:a,element:s}}function Ab(i,e){const s=i.chart.ctx,{body:a,footer:o,title:c}=i,{boxWidth:u,boxHeight:h}=e,m=be(e.bodyFont),g=be(e.titleFont),p=be(e.footerFont),x=c.length,v=o.length,S=a.length,M=en(e.padding);let T=M.height,w=0,O=a.reduce((G,V)=>G+V.before.length+V.lines.length+V.after.length,0);if(O+=i.beforeBody.length+i.afterBody.length,x&&(T+=x*g.lineHeight+(x-1)*e.titleSpacing+e.titleMarginBottom),O){const G=e.displayColors?Math.max(h,m.lineHeight):m.lineHeight;T+=S*G+(O-S)*m.lineHeight+(O-1)*e.bodySpacing}v&&(T+=e.footerMarginTop+v*p.lineHeight+(v-1)*e.footerSpacing);let H=0;const q=function(G){w=Math.max(w,s.measureText(G).width+H)};return s.save(),s.font=g.string,Dt(i.title,q),s.font=m.string,Dt(i.beforeBody.concat(i.afterBody),q),H=e.displayColors?u+2+e.boxPadding:0,Dt(a,G=>{Dt(G.before,q),Dt(G.lines,q),Dt(G.after,q)}),H=0,s.font=p.string,Dt(i.footer,q),s.restore(),w+=M.width,{width:w,height:T}}function uT(i,e){const{y:s,height:a}=e;return s<a/2?"top":s>i.height-a/2?"bottom":"center"}function fT(i,e,s,a){const{x:o,width:c}=a,u=s.caretSize+s.caretPadding;if(i==="left"&&o+c+u>e.width||i==="right"&&o-c-u<0)return!0}function hT(i,e,s,a){const{x:o,width:c}=s,{width:u,chartArea:{left:h,right:m}}=i;let g="center";return a==="center"?g=o<=(h+m)/2?"left":"right":o<=c/2?g="left":o>=u-c/2&&(g="right"),fT(g,i,e,s)&&(g="center"),g}function jb(i,e,s){const a=s.yAlign||e.yAlign||uT(i,s);return{xAlign:s.xAlign||e.xAlign||hT(i,e,s,a),yAlign:a}}function dT(i,e){let{x:s,width:a}=i;return e==="right"?s-=a:e==="center"&&(s-=a/2),s}function mT(i,e,s){let{y:a,height:o}=i;return e==="top"?a+=s:e==="bottom"?a-=o+s:a-=o/2,a}function Db(i,e,s,a){const{caretSize:o,caretPadding:c,cornerRadius:u}=i,{xAlign:h,yAlign:m}=s,g=o+c,{topLeft:p,topRight:x,bottomLeft:v,bottomRight:S}=Us(u);let M=dT(e,h);const T=mT(e,m,g);return m==="center"?h==="left"?M+=g:h==="right"&&(M-=g):h==="left"?M-=Math.max(p,v)+o:h==="right"&&(M+=Math.max(x,S)+o),{x:He(M,0,a.width-e.width),y:He(T,0,a.height-e.height)}}function ur(i,e,s){const a=en(s.padding);return e==="center"?i.x+i.width/2:e==="right"?i.x+i.width-a.right:i.x+a.left}function Ob(i){return bn([],Un(i))}function gT(i,e,s){return Wi(i,{tooltip:e,tooltipItems:s,type:"tooltip"})}function Eb(i,e){const s=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return s?i.override(s):i}const fx={beforeTitle:Ln,title(i){if(i.length>0){const e=i[0],s=e.chart.data.labels,a=s?s.length:0;if(this&&this.options&&this.options.mode==="dataset")return e.dataset.label||"";if(e.label)return e.label;if(a>0&&e.dataIndex<a)return s[e.dataIndex]}return""},afterTitle:Ln,beforeBody:Ln,beforeLabel:Ln,label(i){if(this&&this.options&&this.options.mode==="dataset")return i.label+": "+i.formattedValue||i.formattedValue;let e=i.dataset.label||"";e&&(e+=": ");const s=i.formattedValue;return wt(s)||(e+=s),e},labelColor(i){const s=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{borderColor:s.borderColor,backgroundColor:s.backgroundColor,borderWidth:s.borderWidth,borderDash:s.borderDash,borderDashOffset:s.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(i){const s=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{pointStyle:s.pointStyle,rotation:s.rotation}},afterLabel:Ln,afterBody:Ln,beforeFooter:Ln,footer:Ln,afterFooter:Ln};function Te(i,e,s,a){const o=i[e].call(s,a);return typeof o>"u"?fx[e].call(s,a):o}class zb extends bi{static positioners=ol;constructor(e){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=e.chart,this.options=e.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(e){this.options=e,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const e=this._cachedAnimations;if(e)return e;const s=this.chart,a=this.options.setContext(this.getContext()),o=a.enabled&&s.options.animation&&a.animations,c=new G0(this.chart,o);return o._cacheable&&(this._cachedAnimations=Object.freeze(c)),c}getContext(){return this.$context||(this.$context=gT(this.chart.getContext(),this,this._tooltipItems))}getTitle(e,s){const{callbacks:a}=s,o=Te(a,"beforeTitle",this,e),c=Te(a,"title",this,e),u=Te(a,"afterTitle",this,e);let h=[];return h=bn(h,Un(o)),h=bn(h,Un(c)),h=bn(h,Un(u)),h}getBeforeBody(e,s){return Ob(Te(s.callbacks,"beforeBody",this,e))}getBody(e,s){const{callbacks:a}=s,o=[];return Dt(e,c=>{const u={before:[],lines:[],after:[]},h=Eb(a,c);bn(u.before,Un(Te(h,"beforeLabel",this,c))),bn(u.lines,Te(h,"label",this,c)),bn(u.after,Un(Te(h,"afterLabel",this,c))),o.push(u)}),o}getAfterBody(e,s){return Ob(Te(s.callbacks,"afterBody",this,e))}getFooter(e,s){const{callbacks:a}=s,o=Te(a,"beforeFooter",this,e),c=Te(a,"footer",this,e),u=Te(a,"afterFooter",this,e);let h=[];return h=bn(h,Un(o)),h=bn(h,Un(c)),h=bn(h,Un(u)),h}_createItems(e){const s=this._active,a=this.chart.data,o=[],c=[],u=[];let h=[],m,g;for(m=0,g=s.length;m<g;++m)h.push(cT(this.chart,s[m]));return e.filter&&(h=h.filter((p,x,v)=>e.filter(p,x,v,a))),e.itemSort&&(h=h.sort((p,x)=>e.itemSort(p,x,a))),Dt(h,p=>{const x=Eb(e.callbacks,p);o.push(Te(x,"labelColor",this,p)),c.push(Te(x,"labelPointStyle",this,p)),u.push(Te(x,"labelTextColor",this,p))}),this.labelColors=o,this.labelPointStyles=c,this.labelTextColors=u,this.dataPoints=h,h}update(e,s){const a=this.options.setContext(this.getContext()),o=this._active;let c,u=[];if(!o.length)this.opacity!==0&&(c={opacity:0});else{const h=ol[a.position].call(this,o,this._eventPosition);u=this._createItems(a),this.title=this.getTitle(u,a),this.beforeBody=this.getBeforeBody(u,a),this.body=this.getBody(u,a),this.afterBody=this.getAfterBody(u,a),this.footer=this.getFooter(u,a);const m=this._size=Ab(this,a),g=Object.assign({},h,m),p=jb(this.chart,a,g),x=Db(a,g,p,this.chart);this.xAlign=p.xAlign,this.yAlign=p.yAlign,c={opacity:1,x:x.x,y:x.y,width:m.width,height:m.height,caretX:h.x,caretY:h.y}}this._tooltipItems=u,this.$context=void 0,c&&this._resolveAnimations().update(this,c),e&&a.external&&a.external.call(this,{chart:this.chart,tooltip:this,replay:s})}drawCaret(e,s,a,o){const c=this.getCaretPosition(e,a,o);s.lineTo(c.x1,c.y1),s.lineTo(c.x2,c.y2),s.lineTo(c.x3,c.y3)}getCaretPosition(e,s,a){const{xAlign:o,yAlign:c}=this,{caretSize:u,cornerRadius:h}=a,{topLeft:m,topRight:g,bottomLeft:p,bottomRight:x}=Us(h),{x:v,y:S}=e,{width:M,height:T}=s;let w,O,H,q,G,V;return c==="center"?(G=S+T/2,o==="left"?(w=v,O=w-u,q=G+u,V=G-u):(w=v+M,O=w+u,q=G-u,V=G+u),H=w):(o==="left"?O=v+Math.max(m,p)+u:o==="right"?O=v+M-Math.max(g,x)-u:O=this.caretX,c==="top"?(q=S,G=q-u,w=O-u,H=O+u):(q=S+T,G=q+u,w=O+u,H=O-u),V=q),{x1:w,x2:O,x3:H,y1:q,y2:G,y3:V}}drawTitle(e,s,a){const o=this.title,c=o.length;let u,h,m;if(c){const g=qs(a.rtl,this.x,this.width);for(e.x=ur(this,a.titleAlign,a),s.textAlign=g.textAlign(a.titleAlign),s.textBaseline="middle",u=be(a.titleFont),h=a.titleSpacing,s.fillStyle=a.titleColor,s.font=u.string,m=0;m<c;++m)s.fillText(o[m],g.x(e.x),e.y+u.lineHeight/2),e.y+=u.lineHeight+h,m+1===c&&(e.y+=a.titleMarginBottom-h)}}_drawColorBox(e,s,a,o,c){const u=this.labelColors[a],h=this.labelPointStyles[a],{boxHeight:m,boxWidth:g}=c,p=be(c.bodyFont),x=ur(this,"left",c),v=o.x(x),S=m<p.lineHeight?(p.lineHeight-m)/2:0,M=s.y+S;if(c.usePointStyle){const T={radius:Math.min(g,m)/2,pointStyle:h.pointStyle,rotation:h.rotation,borderWidth:1},w=o.leftForLtr(v,g)+g/2,O=M+m/2;e.strokeStyle=c.multiKeyBackground,e.fillStyle=c.multiKeyBackground,Df(e,T,w,O),e.strokeStyle=u.borderColor,e.fillStyle=u.backgroundColor,Df(e,T,w,O)}else{e.lineWidth=vt(u.borderWidth)?Math.max(...Object.values(u.borderWidth)):u.borderWidth||1,e.strokeStyle=u.borderColor,e.setLineDash(u.borderDash||[]),e.lineDashOffset=u.borderDashOffset||0;const T=o.leftForLtr(v,g),w=o.leftForLtr(o.xPlus(v,1),g-2),O=Us(u.borderRadius);Object.values(O).some(H=>H!==0)?(e.beginPath(),e.fillStyle=c.multiKeyBackground,Sr(e,{x:T,y:M,w:g,h:m,radius:O}),e.fill(),e.stroke(),e.fillStyle=u.backgroundColor,e.beginPath(),Sr(e,{x:w,y:M+1,w:g-2,h:m-2,radius:O}),e.fill()):(e.fillStyle=c.multiKeyBackground,e.fillRect(T,M,g,m),e.strokeRect(T,M,g,m),e.fillStyle=u.backgroundColor,e.fillRect(w,M+1,g-2,m-2))}e.fillStyle=this.labelTextColors[a]}drawBody(e,s,a){const{body:o}=this,{bodySpacing:c,bodyAlign:u,displayColors:h,boxHeight:m,boxWidth:g,boxPadding:p}=a,x=be(a.bodyFont);let v=x.lineHeight,S=0;const M=qs(a.rtl,this.x,this.width),T=function(Z){s.fillText(Z,M.x(e.x+S),e.y+v/2),e.y+=v+c},w=M.textAlign(u);let O,H,q,G,V,F,X;for(s.textAlign=u,s.textBaseline="middle",s.font=x.string,e.x=ur(this,w,a),s.fillStyle=a.bodyColor,Dt(this.beforeBody,T),S=h&&w!=="right"?u==="center"?g/2+p:g+2+p:0,G=0,F=o.length;G<F;++G){for(O=o[G],H=this.labelTextColors[G],s.fillStyle=H,Dt(O.before,T),q=O.lines,h&&q.length&&(this._drawColorBox(s,e,G,M,a),v=Math.max(x.lineHeight,m)),V=0,X=q.length;V<X;++V)T(q[V]),v=x.lineHeight;Dt(O.after,T)}S=0,v=x.lineHeight,Dt(this.afterBody,T),e.y-=c}drawFooter(e,s,a){const o=this.footer,c=o.length;let u,h;if(c){const m=qs(a.rtl,this.x,this.width);for(e.x=ur(this,a.footerAlign,a),e.y+=a.footerMarginTop,s.textAlign=m.textAlign(a.footerAlign),s.textBaseline="middle",u=be(a.footerFont),s.fillStyle=a.footerColor,s.font=u.string,h=0;h<c;++h)s.fillText(o[h],m.x(e.x),e.y+u.lineHeight/2),e.y+=u.lineHeight+a.footerSpacing}}drawBackground(e,s,a,o){const{xAlign:c,yAlign:u}=this,{x:h,y:m}=e,{width:g,height:p}=a,{topLeft:x,topRight:v,bottomLeft:S,bottomRight:M}=Us(o.cornerRadius);s.fillStyle=o.backgroundColor,s.strokeStyle=o.borderColor,s.lineWidth=o.borderWidth,s.beginPath(),s.moveTo(h+x,m),u==="top"&&this.drawCaret(e,s,a,o),s.lineTo(h+g-v,m),s.quadraticCurveTo(h+g,m,h+g,m+v),u==="center"&&c==="right"&&this.drawCaret(e,s,a,o),s.lineTo(h+g,m+p-M),s.quadraticCurveTo(h+g,m+p,h+g-M,m+p),u==="bottom"&&this.drawCaret(e,s,a,o),s.lineTo(h+S,m+p),s.quadraticCurveTo(h,m+p,h,m+p-S),u==="center"&&c==="left"&&this.drawCaret(e,s,a,o),s.lineTo(h,m+x),s.quadraticCurveTo(h,m,h+x,m),s.closePath(),s.fill(),o.borderWidth>0&&s.stroke()}_updateAnimationTarget(e){const s=this.chart,a=this.$animations,o=a&&a.x,c=a&&a.y;if(o||c){const u=ol[e.position].call(this,this._active,this._eventPosition);if(!u)return;const h=this._size=Ab(this,e),m=Object.assign({},u,this._size),g=jb(s,e,m),p=Db(e,m,g,s);(o._to!==p.x||c._to!==p.y)&&(this.xAlign=g.xAlign,this.yAlign=g.yAlign,this.width=h.width,this.height=h.height,this.caretX=u.x,this.caretY=u.y,this._resolveAnimations().update(this,p))}}_willRender(){return!!this.opacity}draw(e){const s=this.options.setContext(this.getContext());let a=this.opacity;if(!a)return;this._updateAnimationTarget(s);const o={width:this.width,height:this.height},c={x:this.x,y:this.y};a=Math.abs(a)<.001?0:a;const u=en(s.padding),h=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;s.enabled&&h&&(e.save(),e.globalAlpha=a,this.drawBackground(c,e,o,s),F0(e,s.textDirection),c.y+=u.top,this.drawTitle(c,e,s),this.drawBody(c,e,s),this.drawFooter(c,e,s),Y0(e,s.textDirection),e.restore())}getActiveElements(){return this._active||[]}setActiveElements(e,s){const a=this._active,o=e.map(({datasetIndex:h,index:m})=>{const g=this.chart.getDatasetMeta(h);if(!g)throw new Error("Cannot find a dataset at index "+h);return{datasetIndex:h,element:g.data[m],index:m}}),c=!yr(a,o),u=this._positionChanged(o,s);(c||u)&&(this._active=o,this._eventPosition=s,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(e,s,a=!0){if(s&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const o=this.options,c=this._active||[],u=this._getActiveElements(e,c,s,a),h=this._positionChanged(u,e),m=s||!yr(u,c)||h;return m&&(this._active=u,(o.enabled||o.external)&&(this._eventPosition={x:e.x,y:e.y},this.update(!0,s))),m}_getActiveElements(e,s,a,o){const c=this.options;if(e.type==="mouseout")return[];if(!o)return s.filter(h=>this.chart.data.datasets[h.datasetIndex]&&this.chart.getDatasetMeta(h.datasetIndex).controller.getParsed(h.index)!==void 0);const u=this.chart.getElementsAtEventForMode(e,c.mode,c,a);return c.reverse&&u.reverse(),u}_positionChanged(e,s){const{caretX:a,caretY:o,options:c}=this,u=ol[c.position].call(this,e,s);return u!==!1&&(a!==u.x||o!==u.y)}}var hx={id:"tooltip",_element:zb,positioners:ol,afterInit(i,e,s){s&&(i.tooltip=new zb({chart:i,options:s}))},beforeUpdate(i,e,s){i.tooltip&&i.tooltip.initialize(s)},reset(i,e,s){i.tooltip&&i.tooltip.initialize(s)},afterDraw(i){const e=i.tooltip;if(e&&e._willRender()){const s={tooltip:e};if(i.notifyPlugins("beforeTooltipDraw",{...s,cancelable:!0})===!1)return;e.draw(i.ctx),i.notifyPlugins("afterTooltipDraw",s)}},afterEvent(i,e){if(i.tooltip){const s=e.replay;i.tooltip.handleEvent(e.event,s,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(i,e)=>e.bodyFont.size,boxWidth:(i,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:fx},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:i=>i!=="filter"&&i!=="itemSort"&&i!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const pT=(i,e,s,a)=>(typeof e=="string"?(s=i.push(e)-1,a.unshift({index:s,label:e})):isNaN(e)&&(s=null),s);function bT(i,e,s,a){const o=i.indexOf(e);if(o===-1)return pT(i,e,s,a);const c=i.lastIndexOf(e);return o!==c?s:o}const xT=(i,e)=>i===null?null:He(Math.round(i),0,e);function Cb(i){const e=this.getLabels();return i>=0&&i<e.length?e[i]:i}class dx extends Ws{static id="category";static defaults={ticks:{callback:Cb}};constructor(e){super(e),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(e){const s=this._addedLabels;if(s.length){const a=this.getLabels();for(const{index:o,label:c}of s)a[o]===c&&a.splice(o,1);this._addedLabels=[]}super.init(e)}parse(e,s){if(wt(e))return null;const a=this.getLabels();return s=isFinite(s)&&a[s]===e?s:bT(a,e,mt(s,e),this._addedLabels),xT(s,a.length-1)}determineDataLimits(){const{minDefined:e,maxDefined:s}=this.getUserBounds();let{min:a,max:o}=this.getMinMax(!0);this.options.bounds==="ticks"&&(e||(a=0),s||(o=this.getLabels().length-1)),this.min=a,this.max=o}buildTicks(){const e=this.min,s=this.max,a=this.options.offset,o=[];let c=this.getLabels();c=e===0&&s===c.length-1?c:c.slice(e,s+1),this._valueRange=Math.max(c.length-(a?0:1),1),this._startValue=this.min-(a?.5:0);for(let u=e;u<=s;u++)o.push({value:u});return o}getLabelForValue(e){return Cb.call(this,e)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(e){return typeof e!="number"&&(e=this.parse(e)),e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getPixelForTick(e){const s=this.ticks;return e<0||e>s.length-1?null:this.getPixelForValue(s[e].value)}getValueForPixel(e){return Math.round(this._startValue+this.getDecimalForPixel(e)*this._valueRange)}getBasePixel(){return this.bottom}}function yT(i,e){const s=[],{bounds:o,step:c,min:u,max:h,precision:m,count:g,maxTicks:p,maxDigits:x,includeBounds:v}=i,S=c||1,M=p-1,{min:T,max:w}=e,O=!wt(u),H=!wt(h),q=!wt(g),G=(w-T)/(x+1);let V=Rp((w-T)/M/S)*S,F,X,Z,I;if(V<1e-14&&!O&&!H)return[{value:T},{value:w}];I=Math.ceil(w/V)-Math.floor(T/V),I>M&&(V=Rp(I*V/M/S)*S),wt(m)||(F=Math.pow(10,m),V=Math.ceil(V*F)/F),o==="ticks"?(X=Math.floor(T/V)*V,Z=Math.ceil(w/V)*V):(X=T,Z=w),O&&H&&c&&XS((h-u)/c,V/1e3)?(I=Math.round(Math.min((h-u)/V,p)),V=(h-u)/I,X=u,Z=h):q?(X=O?u:X,Z=H?h:Z,I=g-1,V=(Z-X)/I):(I=(Z-X)/V,cl(I,Math.round(I),V/1e3)?I=Math.round(I):I=Math.ceil(I));const rt=Math.max(kp(V),kp(X));F=Math.pow(10,wt(m)?rt:m),X=Math.round(X*F)/F,Z=Math.round(Z*F)/F;let st=0;for(O&&(v&&X!==u?(s.push({value:u}),X<u&&st++,cl(Math.round((X+st*V)*F)/F,u,Rb(u,G,i))&&st++):X<u&&st++);st<I;++st){const nt=Math.round((X+st*V)*F)/F;if(H&&nt>h)break;s.push({value:nt})}return H&&v&&Z!==h?s.length&&cl(s[s.length-1].value,h,Rb(h,G,i))?s[s.length-1].value=h:s.push({value:h}):(!H||Z===h)&&s.push({value:Z}),s}function Rb(i,e,{horizontal:s,minRotation:a}){const o=Fi(a),c=(s?Math.sin(o):Math.cos(o))||.001,u=.75*e*(""+i).length;return Math.min(e/c,u)}class vT extends Ws{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(e,s){return wt(e)||(typeof e=="number"||e instanceof Number)&&!isFinite(+e)?null:+e}handleTickRangeOptions(){const{beginAtZero:e}=this.options,{minDefined:s,maxDefined:a}=this.getUserBounds();let{min:o,max:c}=this;const u=m=>o=s?o:m,h=m=>c=a?c:m;if(e){const m=_n(o),g=_n(c);m<0&&g<0?h(0):m>0&&g>0&&u(0)}if(o===c){let m=c===0?1:Math.abs(c*.05);h(c+m),e||u(o-m)}this.min=o,this.max=c}getTickLimit(){const e=this.options.ticks;let{maxTicksLimit:s,stepSize:a}=e,o;return a?(o=Math.ceil(this.max/a)-Math.floor(this.min/a)+1,o>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${a} would result generating up to ${o} ticks. Limiting to 1000.`),o=1e3)):(o=this.computeTickLimit(),s=s||11),s&&(o=Math.min(s,o)),o}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const e=this.options,s=e.ticks;let a=this.getTickLimit();a=Math.max(2,a);const o={maxTicks:a,bounds:e.bounds,min:e.min,max:e.max,precision:s.precision,step:s.stepSize,count:s.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:s.minRotation||0,includeBounds:s.includeBounds!==!1},c=this._range||this,u=yT(o,c);return e.bounds==="ticks"&&GS(u,this,"value"),e.reverse?(u.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),u}configure(){const e=this.ticks;let s=this.min,a=this.max;if(super.configure(),this.options.offset&&e.length){const o=(a-s)/Math.max(e.length-1,1)/2;s-=o,a+=o}this._startValue=s,this._endValue=a,this._valueRange=a-s}getLabelForValue(e){return C0(e,this.chart.options.locale,this.options.ticks.format)}}class mx extends vT{static id="linear";static defaults={ticks:{callback:R0.formatters.numeric}};determineDataLimits(){const{min:e,max:s}=this.getMinMax(!0);this.min=tn(e)?e:0,this.max=tn(s)?s:1,this.handleTickRangeOptions()}computeTickLimit(){const e=this.isHorizontal(),s=e?this.width:this.height,a=Fi(this.options.ticks.minRotation),o=(e?Math.sin(a):Math.cos(a))||.001,c=this._resolveTickFontOptions(0);return Math.ceil(s/Math.min(40,c.lineHeight/o))}getPixelForValue(e){return e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getValueForPixel(e){return this._startValue+this.getDecimalForPixel(e)*this._valueRange}}const Rr={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Ae=Object.keys(Rr);function kb(i,e){return i-e}function Bb(i,e){if(wt(e))return null;const s=i._adapter,{parser:a,round:o,isoWeekday:c}=i._parseOpts;let u=e;return typeof a=="function"&&(u=a(u)),tn(u)||(u=typeof a=="string"?s.parse(u,a):s.parse(u)),u===null?null:(o&&(u=o==="week"&&(_l(c)||c===!0)?s.startOf(u,"isoWeek",c):s.startOf(u,o)),+u)}function Nb(i,e,s,a){const o=Ae.length;for(let c=Ae.indexOf(i);c<o-1;++c){const u=Rr[Ae[c]],h=u.steps?u.steps:Number.MAX_SAFE_INTEGER;if(u.common&&Math.ceil((s-e)/(h*u.size))<=a)return Ae[c]}return Ae[o-1]}function _T(i,e,s,a,o){for(let c=Ae.length-1;c>=Ae.indexOf(s);c--){const u=Ae[c];if(Rr[u].common&&i._adapter.diff(o,a,u)>=e-1)return u}return Ae[s?Ae.indexOf(s):0]}function ST(i){for(let e=Ae.indexOf(i)+1,s=Ae.length;e<s;++e)if(Rr[Ae[e]].common)return Ae[e]}function Lb(i,e,s){if(!s)i[e]=!0;else if(s.length){const{lo:a,hi:o}=Gf(s,e),c=s[a]>=e?s[a]:s[o];i[c]=!0}}function MT(i,e,s,a){const o=i._adapter,c=+o.startOf(e[0].value,a),u=e[e.length-1].value;let h,m;for(h=c;h<=u;h=+o.add(h,1,a))m=s[h],m>=0&&(e[m].major=!0);return e}function Hb(i,e,s){const a=[],o={},c=e.length;let u,h;for(u=0;u<c;++u)h=e[u],o[h]=u,a.push({value:h,major:!1});return c===0||!s?a:MT(i,a,o,s)}class Ub extends Ws{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(e){super(e),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(e,s={}){const a=e.time||(e.time={}),o=this._adapter=new CM._date(e.adapters.date);o.init(s),rl(a.displayFormats,o.formats()),this._parseOpts={parser:a.parser,round:a.round,isoWeekday:a.isoWeekday},super.init(e),this._normalized=s.normalized}parse(e,s){return e===void 0?null:Bb(this,e)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const e=this.options,s=this._adapter,a=e.time.unit||"day";let{min:o,max:c,minDefined:u,maxDefined:h}=this.getUserBounds();function m(g){!u&&!isNaN(g.min)&&(o=Math.min(o,g.min)),!h&&!isNaN(g.max)&&(c=Math.max(c,g.max))}(!u||!h)&&(m(this._getLabelBounds()),(e.bounds!=="ticks"||e.ticks.source!=="labels")&&m(this.getMinMax(!1))),o=tn(o)&&!isNaN(o)?o:+s.startOf(Date.now(),a),c=tn(c)&&!isNaN(c)?c:+s.endOf(Date.now(),a)+1,this.min=Math.min(o,c-1),this.max=Math.max(o+1,c)}_getLabelBounds(){const e=this.getLabelTimestamps();let s=Number.POSITIVE_INFINITY,a=Number.NEGATIVE_INFINITY;return e.length&&(s=e[0],a=e[e.length-1]),{min:s,max:a}}buildTicks(){const e=this.options,s=e.time,a=e.ticks,o=a.source==="labels"?this.getLabelTimestamps():this._generate();e.bounds==="ticks"&&o.length&&(this.min=this._userMin||o[0],this.max=this._userMax||o[o.length-1]);const c=this.min,u=this.max,h=JS(o,c,u);return this._unit=s.unit||(a.autoSkip?Nb(s.minUnit,this.min,this.max,this._getLabelCapacity(c)):_T(this,h.length,s.minUnit,this.min,this.max)),this._majorUnit=!a.major.enabled||this._unit==="year"?void 0:ST(this._unit),this.initOffsets(o),e.reverse&&h.reverse(),Hb(this,h,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(e=>+e.value))}initOffsets(e=[]){let s=0,a=0,o,c;this.options.offset&&e.length&&(o=this.getDecimalForValue(e[0]),e.length===1?s=1-o:s=(this.getDecimalForValue(e[1])-o)/2,c=this.getDecimalForValue(e[e.length-1]),e.length===1?a=c:a=(c-this.getDecimalForValue(e[e.length-2]))/2);const u=e.length<3?.5:.25;s=He(s,0,u),a=He(a,0,u),this._offsets={start:s,end:a,factor:1/(s+1+a)}}_generate(){const e=this._adapter,s=this.min,a=this.max,o=this.options,c=o.time,u=c.unit||Nb(c.minUnit,s,a,this._getLabelCapacity(s)),h=mt(o.ticks.stepSize,1),m=u==="week"?c.isoWeekday:!1,g=_l(m)||m===!0,p={};let x=s,v,S;if(g&&(x=+e.startOf(x,"isoWeek",m)),x=+e.startOf(x,g?"day":u),e.diff(a,s,u)>1e5*h)throw new Error(s+" and "+a+" are too far apart with stepSize of "+h+" "+u);const M=o.ticks.source==="data"&&this.getDataTimestamps();for(v=x,S=0;v<a;v=+e.add(v,h,u),S++)Lb(p,v,M);return(v===a||o.bounds==="ticks"||S===1)&&Lb(p,v,M),Object.keys(p).sort(kb).map(T=>+T)}getLabelForValue(e){const s=this._adapter,a=this.options.time;return a.tooltipFormat?s.format(e,a.tooltipFormat):s.format(e,a.displayFormats.datetime)}format(e,s){const o=this.options.time.displayFormats,c=this._unit,u=s||o[c];return this._adapter.format(e,u)}_tickFormatFunction(e,s,a,o){const c=this.options,u=c.ticks.callback;if(u)return Lt(u,[e,s,a],this);const h=c.time.displayFormats,m=this._unit,g=this._majorUnit,p=m&&h[m],x=g&&h[g],v=a[s],S=g&&x&&v&&v.major;return this._adapter.format(e,o||(S?x:p))}generateTickLabels(e){let s,a,o;for(s=0,a=e.length;s<a;++s)o=e[s],o.label=this._tickFormatFunction(o.value,s,e)}getDecimalForValue(e){return e===null?NaN:(e-this.min)/(this.max-this.min)}getPixelForValue(e){const s=this._offsets,a=this.getDecimalForValue(e);return this.getPixelForDecimal((s.start+a)*s.factor)}getValueForPixel(e){const s=this._offsets,a=this.getDecimalForPixel(e)/s.factor-s.end;return this.min+a*(this.max-this.min)}_getLabelSize(e){const s=this.options.ticks,a=this.ctx.measureText(e).width,o=Fi(this.isHorizontal()?s.maxRotation:s.minRotation),c=Math.cos(o),u=Math.sin(o),h=this._resolveTickFontOptions(0).size;return{w:a*c+h*u,h:a*u+h*c}}_getLabelCapacity(e){const s=this.options.time,a=s.displayFormats,o=a[s.unit]||a.millisecond,c=this._tickFormatFunction(e,0,Hb(this,[e],this._majorUnit),o),u=this._getLabelSize(c),h=Math.floor(this.isHorizontal()?this.width/u.w:this.height/u.h)-1;return h>0?h:1}getDataTimestamps(){let e=this._cache.data||[],s,a;if(e.length)return e;const o=this.getMatchingVisibleMetas();if(this._normalized&&o.length)return this._cache.data=o[0].controller.getAllParsedValues(this);for(s=0,a=o.length;s<a;++s)e=e.concat(o[s].controller.getAllParsedValues(this));return this._cache.data=this.normalize(e)}getLabelTimestamps(){const e=this._cache.labels||[];let s,a;if(e.length)return e;const o=this.getLabels();for(s=0,a=o.length;s<a;++s)e.push(Bb(this,o[s]));return this._cache.labels=this._normalized?e:this.normalize(e)}normalize(e){return O0(e.sort(kb))}}function fr(i,e,s){let a=0,o=i.length-1,c,u,h,m;s?(e>=i[a].pos&&e<=i[o].pos&&({lo:a,hi:o}=Xi(i,"pos",e)),{pos:c,time:h}=i[a],{pos:u,time:m}=i[o]):(e>=i[a].time&&e<=i[o].time&&({lo:a,hi:o}=Xi(i,"time",e)),{time:c,pos:h}=i[a],{time:u,pos:m}=i[o]);const g=u-c;return g?h+(m-h)*(e-c)/g:h}class bA extends Ub{static id="timeseries";static defaults=Ub.defaults;constructor(e){super(e),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const e=this._getTimestampsForTable(),s=this._table=this.buildLookupTable(e);this._minPos=fr(s,this.min),this._tableRange=fr(s,this.max)-this._minPos,super.initOffsets(e)}buildLookupTable(e){const{min:s,max:a}=this,o=[],c=[];let u,h,m,g,p;for(u=0,h=e.length;u<h;++u)g=e[u],g>=s&&g<=a&&o.push(g);if(o.length<2)return[{time:s,pos:0},{time:a,pos:1}];for(u=0,h=o.length;u<h;++u)p=o[u+1],m=o[u-1],g=o[u],Math.round((p+m)/2)!==g&&c.push({time:g,pos:u/(h-1)});return c}_generate(){const e=this.min,s=this.max;let a=super.getDataTimestamps();return(!a.includes(e)||!a.length)&&a.splice(0,0,e),(!a.includes(s)||a.length===1)&&a.push(s),a.sort((o,c)=>o-c)}_getTimestampsForTable(){let e=this._cache.all||[];if(e.length)return e;const s=this.getDataTimestamps(),a=this.getLabelTimestamps();return s.length&&a.length?e=this.normalize(s.concat(a)):e=s.length?s:a,e=this._cache.all=e,e}getDecimalForValue(e){return(fr(this._table,e)-this._minPos)/this._tableRange}getValueForPixel(e){const s=this._offsets,a=this.getDecimalForPixel(e)/s.factor-s.end;return fr(this._table,a*this._tableRange+this._minPos,!0)}}const gx="label";function qb(i,e){typeof i=="function"?i(e):i&&(i.current=e)}function wT(i,e){const s=i.options;s&&e&&Object.assign(s,e)}function px(i,e){i.labels=e}function bx(i,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:gx;const a=[];i.datasets=e.map(o=>{const c=i.datasets.find(u=>u[s]===o[s]);return!c||!o.data||a.includes(c)?{...o}:(a.push(c),Object.assign(c,o),c)})}function TT(i){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:gx;const s={labels:[],datasets:[]};return px(s,i.labels),bx(s,i.datasets,e),s}function AT(i,e){const{height:s=150,width:a=300,redraw:o=!1,datasetIdKey:c,type:u,data:h,options:m,plugins:g=[],fallbackContent:p,updateMode:x,...v}=i,S=Y.useRef(null),M=Y.useRef(null),T=()=>{S.current&&(M.current=new zl(S.current,{type:u,data:TT(h,c),options:m&&{...m},plugins:g}),qb(e,M.current))},w=()=>{qb(e,null),M.current&&(M.current.destroy(),M.current=null)};return Y.useEffect(()=>{!o&&M.current&&m&&wT(M.current,m)},[o,m]),Y.useEffect(()=>{!o&&M.current&&px(M.current.config.data,h.labels)},[o,h.labels]),Y.useEffect(()=>{!o&&M.current&&h.datasets&&bx(M.current.config.data,h.datasets,c)},[o,h.datasets]),Y.useEffect(()=>{M.current&&(o?(w(),setTimeout(T)):M.current.update(x))},[o,m,h.labels,h.datasets,x]),Y.useEffect(()=>{M.current&&(w(),setTimeout(T))},[u]),Y.useEffect(()=>(T(),()=>w()),[]),Lf.createElement("canvas",{ref:S,role:"img",height:s,width:a,...v},p)}const jT=Y.forwardRef(AT);function xx(i,e){return zl.register(e),Y.forwardRef((s,a)=>Lf.createElement(jT,{...s,ref:a,type:i}))}const DT=xx("line",zM),yx=xx("bar",EM);zl.register(dx,mx,lx,ux,hx,rx);const OT=()=>{const[i,e]=Y.useState(null),[s,a]=Y.useState(!0);Y.useEffect(()=>{(async()=>{try{const h=await cS();e(h)}catch(h){console.error("Failed to fetch dashboard stats",h)}a(!1)})()},[]);const o={labels:i?.fund_vs_benchmark?.labels||[],datasets:[{label:"Fund Performance",data:i?.fund_vs_benchmark?.fund_performance||[],backgroundColor:"rgba(75, 192, 192, 0.6)"},{label:"Benchmark Performance",data:i?.fund_vs_benchmark?.benchmark_performance||[],backgroundColor:"rgba(153, 102, 255, 0.6)"}]},c={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{font:{size:14},padding:20}},title:{display:!0,text:"Fund vs. Benchmark Performance",font:{size:16}},tooltip:{titleFont:{size:14},bodyFont:{size:13}}},scales:{x:{ticks:{font:{size:12}}},y:{ticks:{font:{size:12}}}}};return b.jsxs(K,{children:[b.jsx(et,{variant:"h4",gutterBottom:!0,children:"Staff Dashboard"}),s?b.jsx("div",{children:"Loading..."}):i&&b.jsxs(K,{sx:{display:"grid",gridTemplateColumns:{xs:"1fr",md:"repeat(4, 1fr)"},gap:3},children:[b.jsxs(on,{sx:{p:4,height:{xs:140,md:180},display:"flex",flexDirection:"column",justifyContent:"center",gridColumn:{xs:"1",md:"1 / 3"}},children:[b.jsx(et,{variant:"h6",sx:{fontSize:"1.4rem",fontWeight:600,color:"primary.main",mb:1},children:"Total Clients"}),b.jsx(et,{variant:"h2",sx:{fontWeight:"bold",color:"text.primary"},children:i.num_clients})]}),b.jsxs(on,{sx:{p:4,height:{xs:140,md:180},display:"flex",flexDirection:"column",justifyContent:"center",gridColumn:{xs:"1",md:"3 / 5"}},children:[b.jsx(et,{variant:"h6",sx:{fontSize:"1.4rem",fontWeight:600,color:"success.main",mb:1},children:"Active Folios"}),b.jsx(et,{variant:"h2",sx:{fontWeight:"bold",color:"text.primary"},children:i.num_active_folios})]}),b.jsxs(on,{sx:{p:4,height:{xs:450,md:550},display:"flex",flexDirection:"column",gridColumn:{xs:"1",md:"1 / -1"}},children:[b.jsx(et,{variant:"h5",sx:{mb:3,fontWeight:600},children:"Fund vs. Benchmark Performance"}),b.jsx(K,{sx:{height:{xs:370,md:470},width:"100%",position:"relative",flexGrow:1},children:b.jsx(yx,{options:c,data:o})})]})]})]})},ET=()=>{const[i,e]=Y.useState(""),[s,a]=Y.useState("name"),[o,c]=Y.useState([]),[u,h]=Y.useState(!1),[m,g]=Y.useState(null),[p,x]=Y.useState(!1),[v,S]=Y.useState(""),[M,T]=Y.useState(""),[w,O]=Y.useState(!1),H=async F=>{F.preventDefault(),h(!0),g(null);try{const Z=await Tp(s==="name"?{name_query:i}:{folio_number_query:i});c(Z)}catch(X){g(Ue(X)),c([])}h(!1)},q=F=>{S(F),x(!0)},G=()=>{x(!1),S(""),T(""),g(null)},V=async()=>{if(!M){g("Folio number is required.");return}const F=parseInt(M);if(isNaN(F)||F<=0){g("Folio number must be a positive integer.");return}O(!0),g(null);try{await rS(v,M);const Z=await Tp(s==="name"?{name_query:i}:{folio_number_query:i});c(Z),G()}catch(X){g(Ue(X))}O(!1)};return b.jsxs(K,{children:[b.jsx(et,{variant:"h4",gutterBottom:!0,children:"Client Directory"}),b.jsxs(K,{component:"form",onSubmit:H,sx:{mb:4,display:"flex",gap:2,alignItems:"center"},children:[b.jsx(Mt,{label:`Search by ${s==="name"?"Name":"Folio Number"}`,value:i,onChange:F=>e(F.target.value),variant:"outlined",sx:{flexGrow:1}}),b.jsxs(Ct,{onClick:()=>a(s==="name"?"folio":"name"),children:["Search by ",s==="name"?"Folio":"Name"]}),b.jsx(Ct,{type:"submit",variant:"contained",disabled:u,children:u?b.jsx($e,{size:24}):"Search"})]}),m&&b.jsx(le,{severity:"error",sx:{mb:2},children:m}),b.jsx(K,{sx:{overflowX:"auto"},children:b.jsx(ml,{component:on,children:b.jsxs(gl,{children:[b.jsx(pl,{children:b.jsxs(yn,{children:[b.jsx(at,{children:"Username"}),b.jsx(at,{children:"Name"}),b.jsx(at,{children:"Email"}),b.jsx(at,{children:"Folios (Active/Terminated)"}),b.jsx(at,{children:"Actions"})]})}),b.jsx(bl,{children:o.map(F=>b.jsxs(yn,{children:[b.jsx(at,{children:F.username}),b.jsx(at,{children:`${F.first_name} ${F.last_name}`}),b.jsx(at,{children:F.email}),b.jsx(at,{children:Object.entries(F.folio_numbers).map(([X,Z])=>b.jsxs("span",{style:{color:Z?"red":"green"},children:[X,Z?" (Terminated)":" (Active)"," "]},X))}),b.jsx(at,{children:b.jsx(Ct,{variant:"contained",onClick:()=>q(F.username),children:"Create Folio"})})]},F.username))})]})})}),b.jsxs(Xb,{open:p,onClose:G,maxWidth:"sm",fullWidth:!0,children:[b.jsx(Gb,{children:"Create New Folio"}),b.jsxs(Qb,{children:[b.jsxs(et,{variant:"body2",sx:{mb:2},children:["Creating a new folio for user: ",b.jsx("strong",{children:v})]}),m&&b.jsx(le,{severity:"error",sx:{mb:2},children:m}),b.jsx(Mt,{label:"Folio Number",value:M,onChange:F=>T(F.target.value),fullWidth:!0,required:!0,type:"number",slotProps:{htmlInput:{min:1}},helperText:"Enter a unique positive integer for the folio number",sx:{mt:1}})]}),b.jsxs(Zb,{children:[b.jsx(Ct,{onClick:G,disabled:w,children:"Cancel"}),b.jsx(Ct,{onClick:V,variant:"contained",disabled:w||!M,children:w?b.jsx($e,{size:20}):"Create Folio"})]})]})]})},zT=()=>{const[i,e]=Y.useState(""),[s,a]=Y.useState(""),[o,c]=Y.useState(""),[u,h]=Y.useState(""),[m,g]=Y.useState(""),[p,x]=Y.useState(!1),[v,S]=Y.useState(null),[M,T]=Y.useState(null),w=async()=>{if(x(!0),S(null),T(null),!i||!s||!o||!u||!m){S("All fields are required."),x(!1);return}const O=parseInt(m);if(isNaN(O)||O<=0){S("Folio number must be a positive integer."),x(!1);return}try{await aS({pan:i,email:s,first_name:o,last_name:u,folio_number:m}),T("Client registered successfully!"),e(""),a(""),c(""),h(""),g("")}catch(H){S(Ue(H))}x(!1)};return b.jsxs(K,{sx:{maxWidth:600,margin:"auto"},children:[b.jsx(et,{variant:"h4",gutterBottom:!0,children:"Register New Client"}),v&&b.jsx(le,{severity:"error",sx:{mb:2},children:v}),M&&b.jsx(le,{severity:"success",sx:{mb:2},children:M}),b.jsxs(K,{children:[b.jsx(Mt,{label:"PAN Number",value:i,onChange:O=>e(O.target.value.toUpperCase()),fullWidth:!0,margin:"normal"}),b.jsx(Mt,{label:"Email",value:s,onChange:O=>a(O.target.value),fullWidth:!0,margin:"normal"}),b.jsx(Mt,{label:"First Name",value:o,onChange:O=>c(O.target.value),fullWidth:!0,margin:"normal"}),b.jsx(Mt,{label:"Last Name",value:u,onChange:O=>h(O.target.value),fullWidth:!0,margin:"normal"}),b.jsx(Mt,{label:"Folio Number",value:m,onChange:O=>g(O.target.value),fullWidth:!0,margin:"normal",required:!0,type:"number",slotProps:{htmlInput:{min:1}},helperText:"Enter a unique positive integer for the folio number"}),b.jsx(Ct,{onClick:w,variant:"contained",color:"primary",disabled:p||!i||!s||!o||!u||!m,fullWidth:!0,sx:{mt:2},children:p?b.jsx($e,{size:24}):"Register Client"})]})]})};var br={exports:{}},CT=br.exports,Vb;function RT(){return Vb||(Vb=1,(function(i,e){(function(s,a){a()})(CT,function(){function s(g,p){return typeof p>"u"?p={autoBom:!1}:typeof p!="object"&&(console.warn("Deprecated: Expected third argument to be a object"),p={autoBom:!p}),p.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(g.type)?new Blob(["\uFEFF",g],{type:g.type}):g}function a(g,p,x){var v=new XMLHttpRequest;v.open("GET",g),v.responseType="blob",v.onload=function(){m(v.response,p,x)},v.onerror=function(){console.error("could not download file")},v.send()}function o(g){var p=new XMLHttpRequest;p.open("HEAD",g,!1);try{p.send()}catch{}return 200<=p.status&&299>=p.status}function c(g){try{g.dispatchEvent(new MouseEvent("click"))}catch{var p=document.createEvent("MouseEvents");p.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),g.dispatchEvent(p)}}var u=typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof Po=="object"&&Po.global===Po?Po:void 0,h=u.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),m=u.saveAs||(typeof window!="object"||window!==u?function(){}:"download"in HTMLAnchorElement.prototype&&!h?function(g,p,x){var v=u.URL||u.webkitURL,S=document.createElement("a");p=p||g.name||"download",S.download=p,S.rel="noopener",typeof g=="string"?(S.href=g,S.origin===location.origin?c(S):o(S.href)?a(g,p,x):c(S,S.target="_blank")):(S.href=v.createObjectURL(g),setTimeout(function(){v.revokeObjectURL(S.href)},4e4),setTimeout(function(){c(S)},0))}:"msSaveOrOpenBlob"in navigator?function(g,p,x){if(p=p||g.name||"download",typeof g!="string")navigator.msSaveOrOpenBlob(s(g,x),p);else if(o(g))a(g,p,x);else{var v=document.createElement("a");v.href=g,v.target="_blank",setTimeout(function(){c(v)})}}:function(g,p,x,v){if(v=v||open("","_blank"),v&&(v.document.title=v.document.body.innerText="downloading..."),typeof g=="string")return a(g,p,x);var S=g.type==="application/octet-stream",M=/constructor/i.test(u.HTMLElement)||u.safari,T=/CriOS\/[\d]+/.test(navigator.userAgent);if((T||S&&M||h)&&typeof FileReader<"u"){var w=new FileReader;w.onloadend=function(){var q=w.result;q=T?q:q.replace(/^data:[^;]*;/,"data:attachment/file;"),v?v.location.href=q:location=q,v=null},w.readAsDataURL(g)}else{var O=u.URL||u.webkitURL,H=O.createObjectURL(g);v?v.location=H:location.href=H,v=null,setTimeout(function(){O.revokeObjectURL(H)},4e4)}});u.saveAs=m.saveAs=m,i.exports=m})})(br)),br.exports}var vx=RT();const kT=()=>{const[i,e]=Y.useState(null),[s,a]=Y.useState({year:new Date().getFullYear(),month:new Date().getMonth()+1}),[o,c]=Y.useState(!0),u=async()=>{c(!0);try{const g=await _0(s);e(g)}catch(g){console.error("Failed to fetch documents",g)}c(!1)};Y.useEffect(()=>{u()},[]);const h=g=>{const{name:p,value:x}=g.target;a({...s,[p]:x})},m=async g=>{try{const p=await oS(g.id);vx.saveAs(p,g.file_name)}catch(p){console.error("Failed to download document",p)}};return b.jsxs(K,{children:[b.jsx(et,{variant:"h4",gutterBottom:!0,children:"Document Manager"}),b.jsxs(Qt,{container:!0,spacing:2,alignItems:"center",sx:{mb:4},children:[b.jsx(Qt,{size:{xs:12,sm:6,md:4},children:b.jsx(Mt,{name:"year",label:"Year",type:"number",value:s.year,onChange:h})}),b.jsx(Qt,{size:{xs:12,sm:6,md:4},children:b.jsx(Mt,{name:"month",label:"Month",type:"number",value:s.month,onChange:h})}),b.jsx(Qt,{size:{xs:12,sm:12,md:4},children:b.jsx(Ct,{variant:"contained",onClick:u,children:"Filter"})})]}),o?b.jsx("div",{children:"Loading..."}):i&&b.jsxs(K,{children:[b.jsxs(et,{variant:"h5",gutterBottom:!0,children:["Company Documents (",i.total_documents.company,")"]}),b.jsx(ml,{component:on,sx:{mb:4},children:b.jsxs(gl,{children:[b.jsx(pl,{children:b.jsxs(yn,{children:[b.jsx(at,{children:"File Name"}),b.jsx(at,{children:"Subtype"}),b.jsx(at,{children:"Date"}),b.jsx(at,{children:"Size"}),b.jsx(at,{children:"Download"})]})}),b.jsx(bl,{children:i.company_documents.map(g=>b.jsxs(yn,{children:[b.jsx(at,{children:g.file_name}),b.jsx(at,{children:g.subtype_display||g.subtype}),b.jsxs(at,{children:[g.year,"-",g.month]}),b.jsxs(at,{children:[(g.file_size/1024).toFixed(2)," KB"]}),b.jsx(at,{children:b.jsx(Ct,{variant:"contained",onClick:()=>m(g),children:"Download"})})]},g.id))})]})}),b.jsx(K,{sx:{mt:4,mb:2},children:b.jsxs(et,{variant:"h5",gutterBottom:!0,children:["Client Documents (",i.total_documents.client,")"]})}),i.client_documents.map(g=>b.jsxs(el,{children:[b.jsx(nl,{expandIcon:b.jsx(il,{}),children:b.jsxs(et,{children:["Folio: ",g.folio_number," - ",g.client_name]})}),b.jsx(sl,{children:b.jsx(ml,{component:on,children:b.jsxs(gl,{children:[b.jsx(pl,{children:b.jsxs(yn,{children:[b.jsx(at,{children:"File Name"}),b.jsx(at,{children:"Date"}),b.jsx(at,{children:"Size"}),b.jsx(at,{children:"Download"})]})}),b.jsx(bl,{children:g.documents.map(p=>b.jsxs(yn,{children:[b.jsx(at,{children:p.file_name}),b.jsxs(at,{children:[p.year,"-",p.month]}),b.jsxs(at,{children:[(p.file_size/1024).toFixed(2)," KB"]}),b.jsx(at,{children:b.jsx(Ct,{variant:"contained",onClick:()=>m(p),children:"Download"})})]},p.id))})]})})})]},g.folio_number))]})]})},BT=()=>{const[i,e]=Y.useState({}),[s,a]=Y.useState(null),[o,c]=Y.useState(!1),[u,h]=Y.useState(null),[m,g]=Y.useState(null),[p,x]=Y.useState([]);Y.useEffect(()=>{(async()=>{try{const w=await _0({}),O=[...w.company_documents];w.client_documents.forEach(H=>{O.push(...H.documents)}),x(O)}catch(w){console.error("Failed to fetch documents",w)}})()},[]);const v=T=>{const{name:w,value:O}=T.target;e({...i,[w]:O})},S=T=>{T.target.files&&a(T.target.files[0])},M=async T=>{T.preventDefault(),c(!0),h(null),g(null);const{document_type:w,year:O,month:H,folio_number:q}=i;if(p.find(F=>F.document_type===w&&F.year===parseInt(O,10)&&F.month===parseInt(H,10)&&(w==="Client"?F.folio_number===parseInt(q,10):!0))&&!window.confirm("A document of this type for the selected month and year already exists. Do you want to replace it?")){c(!1);return}const V=new FormData;if(Object.keys(i).forEach(F=>{V.append(F,i[F])}),s){const F=s.name.split(".").pop(),X=`${w}_${O}_${H}.${F}`;V.append("uploaded_file",s,X)}try{await lS(V),g("Document uploaded successfully!"),e({}),a(null)}catch(F){h(Ue(F))}c(!1)};return b.jsxs(K,{sx:{maxWidth:800,margin:"auto"},children:[b.jsx(et,{variant:"h4",gutterBottom:!0,children:"Upload Document"}),b.jsxs(K,{component:"form",onSubmit:M,sx:{mt:3},children:[b.jsxs(Qt,{container:!0,spacing:2,children:[b.jsx(Qt,{size:{xs:12},children:b.jsxs(tp,{fullWidth:!0,children:[b.jsx(ep,{children:"Document Type"}),b.jsxs(np,{name:"document_type",value:i.document_type||"",onChange:v,children:[b.jsx(Za,{value:"Company",children:"Company"}),b.jsx(Za,{value:"Client",children:"Client"})]})]})}),i.document_type==="Company"&&b.jsx(Qt,{size:{xs:12},children:b.jsxs(tp,{fullWidth:!0,children:[b.jsx(ep,{children:"Company Subtype"}),b.jsxs(np,{name:"subtype",value:i.subtype||"",onChange:v,children:[b.jsx(Za,{value:"monthly_fund_performance",children:"Monthly Fund Performance"}),b.jsx(Za,{value:"monthly_investor_presentation",children:"Monthly Investor Presentation"}),b.jsx(Za,{value:"sebi_quarterly_report",children:"SEBI Quarterly Report"})]})]})}),i.document_type==="Client"&&b.jsx(Qt,{size:{xs:12},children:b.jsx(Mt,{name:"folio_number",label:"Folio Number",fullWidth:!0,onChange:v})}),b.jsx(Qt,{size:{xs:6},children:b.jsx(Mt,{name:"year",label:"Year",type:"number",fullWidth:!0,onChange:v})}),b.jsx(Qt,{size:{xs:6},children:b.jsx(Mt,{name:"month",label:"Month",type:"number",fullWidth:!0,onChange:v})}),i.document_type==="Client"&&b.jsxs(b.Fragment,{children:[b.jsx(Qt,{size:{xs:6},children:b.jsx(Mt,{name:"pre_tax_nav",label:"Pre-tax NAV",type:"number",fullWidth:!0,onChange:v})}),b.jsx(Qt,{size:{xs:6},children:b.jsx(Mt,{name:"post_tax_nav",label:"Post-tax NAV",type:"number",fullWidth:!0,onChange:v})})]}),i.document_type==="Company"&&i.subtype==="monthly_fund_performance"&&b.jsxs(b.Fragment,{children:[b.jsx(Qt,{size:{xs:6},children:b.jsx(Mt,{name:"fund_nav",label:"Fund NAV",type:"number",fullWidth:!0,onChange:v})}),b.jsx(Qt,{size:{xs:6},children:b.jsx(Mt,{name:"benchmark_value",label:"Benchmark Value",type:"number",fullWidth:!0,onChange:v})})]}),b.jsxs(Qt,{size:{xs:12},children:[b.jsxs(Ct,{variant:"contained",component:"label",children:["Choose File",b.jsx("input",{type:"file",hidden:!0,onChange:S})]}),s&&b.jsx(et,{sx:{ml:2,display:"inline"},children:s.name})]})]}),u&&b.jsx(le,{severity:"error",sx:{mt:2},children:u}),m&&b.jsx(le,{severity:"success",sx:{mt:2},children:m}),b.jsx(Ct,{type:"submit",fullWidth:!0,variant:"contained",sx:{mt:3},disabled:o,children:o?b.jsx($e,{size:24}):"Upload"})]})]})},NT=()=>{const[i,e]=Y.useState(""),[s,a]=Y.useState(!1),[o,c]=Y.useState(null),[u,h]=Y.useState(null),[m,g]=Y.useState(!1),[p,x]=Y.useState(""),v=()=>g(!0),S=()=>g(!1),M=async()=>{a(!0),c(null),h(null);try{const w=await uS(i);h(w.message),S()}catch(w){c(Ue(w))}a(!1)},T=()=>p===`${i} delete`;return b.jsxs(K,{children:[b.jsx(et,{variant:"h4",gutterBottom:!0,children:"Terminate Folio"}),b.jsxs(K,{component:"form",onSubmit:w=>{w.preventDefault(),v()},sx:{mb:4,display:"flex",gap:2,alignItems:"center"},children:[b.jsx(Mt,{label:"Folio Number",value:i,onChange:w=>e(w.target.value),variant:"outlined",sx:{flexGrow:1}}),b.jsx(Ct,{type:"submit",variant:"contained",color:"error",disabled:!i,children:"Terminate"})]}),o&&b.jsx(le,{severity:"error",sx:{mb:2},children:o}),u&&b.jsx(le,{severity:"success",sx:{mb:2},children:u}),b.jsxs(Xb,{open:m,onClose:S,children:[b.jsx(Gb,{children:"Confirm Termination"}),b.jsxs(Qb,{children:[b.jsxs(Zv,{children:['To confirm termination, please type the folio number followed by "delete". For example: "',i,' delete"']}),b.jsx(Mt,{autoFocus:!0,margin:"dense",label:"Confirmation Text",type:"text",fullWidth:!0,variant:"standard",value:p,onChange:w=>x(w.target.value)})]}),b.jsxs(Zb,{children:[b.jsx(Ct,{onClick:S,children:"Cancel"}),b.jsx(Ct,{onClick:M,color:"error",disabled:!T()||s,children:s?b.jsx($e,{size:24}):"Terminate"})]})]})]})},xf=240,LT=({mobileOpen:i,handleDrawerToggle:e})=>{const s=b.jsxs(K,{children:[b.jsx(Vn,{}),b.jsx(K,{sx:{overflow:"auto"},children:b.jsxs(Fs,{children:[b.jsx(me,{disablePadding:!0,component:ye,to:"/client/dashboard",children:b.jsxs(Pe,{children:[b.jsx(Je,{children:b.jsx(Cf,{})}),b.jsx(ge,{primary:"Dashboard"})]})}),b.jsx(me,{disablePadding:!0,component:ye,to:"/client/statements",children:b.jsxs(Pe,{children:[b.jsx(Je,{children:b.jsx(Yb,{})}),b.jsx(ge,{primary:"Statements"})]})})]})})]});return b.jsxs(K,{component:"nav",sx:{width:{sm:xf},flexShrink:{sm:0}},"aria-label":"mailbox folders",children:[b.jsx(Vs,{variant:"temporary",open:i,onClose:e,ModalProps:{keepMounted:!0},sx:{display:{xs:"block",sm:"none"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:xf}},children:s}),b.jsx(Vs,{variant:"permanent",sx:{display:{xs:"none",sm:"block"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:xf}},open:!0,children:s})]})},HT=({children:i})=>{const[e,s]=Y.useState(!1),a=Rf(),o=kf(a.breakpoints.down("sm")),{user:c,logout:u}=Ol(),h=()=>{s(!e)};return b.jsxs(K,{sx:{display:"flex"},children:[b.jsx(Bf,{position:"fixed",sx:{zIndex:m=>m.zIndex.drawer+1},children:b.jsxs(Vn,{children:[o&&b.jsx(hl,{color:"inherit","aria-label":"open drawer",edge:"start",onClick:h,sx:{mr:2,display:{sm:"none"}},children:b.jsx(Nf,{})}),b.jsx(et,{variant:"h6",component:"div",sx:{flexGrow:1},children:"Ampersand Capital"}),c&&b.jsx(Ct,{color:"inherit",onClick:u,children:"Logout"})]})}),b.jsx(LT,{mobileOpen:e,handleDrawerToggle:h}),b.jsxs(K,{component:"main",sx:{flexGrow:1,p:3},children:[b.jsx(Vn,{}),i]})]})},UT=()=>b.jsx(HT,{children:b.jsx(Tr,{})}),qT=async()=>(await Vt.get("/api/client/dashboard/")).data,VT=async()=>(await Vt.get("api/client/documents/")).data,FT=async i=>(await Vt.get(`/api/client/download-document/${i}/`,{responseType:"blob"})).data;zl.register(dx,mx,lx,Pw,Jw,ux,hx,rx);const YT=()=>{const[i,e]=Y.useState(null),[s,a]=Y.useState(!0);if(Y.useEffect(()=>{(async()=>{try{const u=await qT();e(u)}catch(u){console.error("Failed to fetch dashboard data",u)}a(!1)})()},[]),s)return b.jsx("div",{children:"Loading..."});const o={labels:i?.performance_chart_data.map(c=>c.period),datasets:[{label:"Fund Performance",data:i?.performance_chart_data.map(c=>c.fund_performance),backgroundColor:"rgba(13, 27, 75, 0.8)"},{label:"Benchmark Performance",data:i?.performance_chart_data.map(c=>c.benchmark_performance),backgroundColor:"rgba(212, 175, 55, 0.8)"}]};return b.jsxs(K,{children:[b.jsx(et,{variant:"h4",gutterBottom:!0,children:i?.message}),b.jsxs(K,{sx:{display:"grid",gridTemplateColumns:{xs:"1fr",md:"repeat(2, 1fr)",lg:"repeat(3, 1fr)"},gap:3},children:[b.jsxs(on,{sx:{p:3,height:{xs:380,sm:430,md:480},display:"flex",flexDirection:"column",gridColumn:{xs:"1",md:"1 / -1",lg:"1 / 3"}},children:[b.jsx(et,{variant:"h6",sx:{mb:2,fontSize:"1.25rem",fontWeight:600},children:"Fund vs Benchmark Performance (%)"}),b.jsx(K,{sx:{height:{xs:300,sm:350,md:400},width:"100%",position:"relative",flexGrow:1},children:b.jsx(yx,{data:o,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!0,position:"top",labels:{font:{size:14},padding:20}},tooltip:{titleFont:{size:14},bodyFont:{size:13}}},scales:{x:{ticks:{font:{size:12}}},y:{ticks:{font:{size:12}}}}}})})]}),b.jsxs(on,{sx:{p:3,height:{xs:360,sm:430,md:480},display:"flex",flexDirection:"column",gridColumn:{xs:"1",lg:"3"}},children:[b.jsx(et,{variant:"h6",sx:{mb:2,fontSize:"1.25rem",fontWeight:600},children:"Documents for Current Month"}),b.jsx(K,{sx:{flexGrow:1,overflow:"auto"},children:i?.company_documents_current_month?.length>0||i?.client_documents_current_month?.length>0?b.jsxs(Fs,{children:[i?.company_documents_current_month.map(c=>b.jsx(me,{component:"a",href:c.uploaded_file_url,target:"_blank",children:b.jsx(ge,{primary:`${c.subtype_display||c.subtype} (${c.year}-${c.month})`})},c.id)),i?.client_documents_current_month.map(c=>b.jsx(me,{component:"a",href:c.uploaded_file_url,target:"_blank",children:b.jsx(ge,{primary:`Folio ${c.folio_number} Statement (${c.year}-${c.month})`})},c.id))]}):b.jsx(K,{sx:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%",color:"text.secondary"},children:b.jsx(et,{variant:"body1",children:"No documents available"})})})]}),i?.client_line_charts_data.map(c=>b.jsxs(on,{sx:{p:3,height:{xs:360,sm:430,md:480},display:"flex",flexDirection:"column"},children:[b.jsxs(et,{variant:"h6",sx:{mb:2,fontSize:"1.25rem",fontWeight:600},children:["Folio ",c.folio_number," NAV"]}),b.jsx(K,{sx:{height:{xs:280,sm:350,md:400},width:"100%",position:"relative",flexGrow:1},children:b.jsx(DT,{data:{labels:c.labels,datasets:c.datasets.map((u,h)=>{const m=[{border:"#1976d2",background:"rgba(25, 118, 210, 0.1)"},{border:"#9c27b0",background:"rgba(156, 39, 176, 0.1)"},{border:"#388e3c",background:"rgba(56, 142, 60, 0.1)"},{border:"#f57c00",background:"rgba(245, 124, 0, 0.1)"},{border:"#00796b",background:"rgba(0, 121, 107, 0.1)"},{border:"#5d4037",background:"rgba(93, 64, 55, 0.1)"}],g=h%m.length;return{...u,tension:.1,borderWidth:3,pointRadius:4,pointHoverRadius:6,borderColor:m[g].border,backgroundColor:m[g].background,fill:!1}})},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!0,position:"top",labels:{font:{size:13},padding:15}},tooltip:{titleFont:{size:13},bodyFont:{size:12}}},scales:{x:{ticks:{font:{size:11},maxTicksLimit:8}},y:{ticks:{font:{size:11}}}}}})})]},c.folio_number))]})]})},XT=()=>{const[i,e]=Y.useState(null),[s,a]=Y.useState(!0);Y.useEffect(()=>{(async()=>{try{const u=await VT();e(u)}catch(u){console.error("Failed to fetch documents",u)}a(!1)})()},[]);const o=async c=>{try{const u=await FT(c.id);vx.saveAs(u,c.file_name)}catch(u){console.error("Failed to download document",u)}};return s?b.jsx("div",{children:"Loading..."}):b.jsxs(K,{children:[b.jsx(et,{variant:"h4",gutterBottom:!0,children:"All Documents"}),b.jsx(et,{variant:"h5",gutterBottom:!0,children:"Company Documents"}),i&&Object.entries(i.company_documents).map(([c,u])=>b.jsxs(el,{children:[b.jsx(nl,{expandIcon:b.jsx(il,{}),children:b.jsx(et,{children:c})}),b.jsx(sl,{children:Object.entries(u).map(([h,m])=>b.jsxs(el,{children:[b.jsx(nl,{expandIcon:b.jsx(il,{}),children:b.jsxs(et,{children:["Quarter ",h]})}),b.jsx(sl,{children:Object.entries(m).map(([g,p])=>b.jsxs(K,{sx:{mb:2},children:[b.jsx(et,{variant:"h6",children:g}),b.jsx(Fs,{children:p.map(x=>b.jsxs(me,{children:[b.jsx(ge,{primary:x.subtype_display||x.subtype}),b.jsx(Ct,{variant:"contained",onClick:()=>o(x),children:"Download"})]},x.id))})]},g))})]},h))})]},c)),b.jsx(et,{variant:"h5",gutterBottom:!0,sx:{mt:4},children:"Client Documents"}),i&&Object.entries(i.client_documents).map(([c,u])=>b.jsxs(el,{children:[b.jsx(nl,{expandIcon:b.jsx(il,{}),children:b.jsxs(et,{children:["Folio ",c]})}),b.jsx(sl,{children:Object.entries(u).map(([h,m])=>b.jsxs(el,{children:[b.jsx(nl,{expandIcon:b.jsx(il,{}),children:b.jsx(et,{children:h})}),b.jsx(sl,{children:b.jsx(Fs,{children:m.map(g=>b.jsxs(me,{children:[b.jsx(ge,{primary:`Statement - ${g.month}`}),b.jsx(Ct,{variant:"contained",onClick:()=>o(g),children:"Download"})]},g.id))})})]},h))})]},c))]})},GT=()=>{const[i,e]=Y.useState(""),[s,a]=Y.useState(!1),[o,c]=Y.useState(null),[u,h]=Y.useState(null),m=async g=>{g.preventDefault(),a(!0),h(null),c(null);try{const p=await q_(i);c(p.detail)}catch(p){h(Ue(p))}finally{a(!1)}};return b.jsx(Kb,{component:"main",maxWidth:"xs",children:b.jsxs(K,{sx:{marginTop:8,display:"flex",flexDirection:"column",alignItems:"center"},children:[b.jsx(et,{component:"h1",variant:"h5",children:"Forgot Password"}),b.jsxs(K,{component:"form",onSubmit:m,noValidate:!0,sx:{mt:1},children:[b.jsx(Mt,{margin:"normal",required:!0,fullWidth:!0,id:"email",label:"Email Address",name:"email",autoComplete:"email",autoFocus:!0,value:i,onChange:g=>e(g.target.value)}),o&&b.jsx(le,{severity:"success",sx:{width:"100%",mt:2},children:o}),u&&b.jsx(le,{severity:"error",sx:{width:"100%",mt:2},children:u}),b.jsx(Ct,{type:"submit",fullWidth:!0,variant:"contained",sx:{mt:3,mb:2},disabled:s,children:s?b.jsx($e,{size:24}):"Send Password Reset Email"}),b.jsx(Qt,{container:!0,children:b.jsx(Qt,{children:b.jsx(yf,{component:ye,to:"/login",variant:"body2",children:"Go to Login"})})})]})]})})},QT=()=>{const{uidb64:i,token:e}=t1(),s=Wb(),[a,o]=Y.useState(""),[c,u]=Y.useState(""),[h,m]=Y.useState(!1),[g,p]=Y.useState(null),[x,v]=Y.useState(null),S=async M=>{if(M.preventDefault(),a!==c){v("Passwords do not match");return}m(!0),v(null),p(null);try{if(!i||!e){v("Invalid password reset link.");return}const T=await V_(i,e,a);p(T.detail),setTimeout(()=>{s("/login")},5173)}catch(T){v(Ue(T))}finally{m(!1)}};return b.jsx(Kb,{component:"main",maxWidth:"xs",children:b.jsxs(K,{sx:{marginTop:8,display:"flex",flexDirection:"column",alignItems:"center"},children:[b.jsx(et,{component:"h1",variant:"h5",children:"Reset Password"}),b.jsxs(K,{component:"form",onSubmit:S,noValidate:!0,sx:{mt:1},children:[b.jsx(Mt,{margin:"normal",required:!0,fullWidth:!0,name:"password",label:"New Password",type:"password",id:"password",value:a,onChange:M=>o(M.target.value)}),b.jsx(Mt,{margin:"normal",required:!0,fullWidth:!0,name:"passwordConfirm",label:"Confirm New Password",type:"password",id:"passwordConfirm",value:c,onChange:M=>u(M.target.value)}),g&&b.jsx(le,{severity:"success",sx:{width:"100%",mt:2},children:g}),x&&b.jsx(le,{severity:"error",sx:{width:"100%",mt:2},children:x}),b.jsx(Ct,{type:"submit",fullWidth:!0,variant:"contained",sx:{mt:3,mb:2},disabled:h,children:h?b.jsx($e,{size:24}):"Reset Password"})]})]})})},ZT=()=>b.jsx(K,{sx:{display:"flex"},children:b.jsxs(e1,{children:[b.jsx(Bt,{path:"/login",element:b.jsx(Y_,{})}),b.jsx(Bt,{path:"/password-reset",element:b.jsx(GT,{})}),b.jsx(Bt,{path:"/password-reset/confirm/:uidb64/:token",element:b.jsx(QT,{})}),b.jsx(Bt,{path:"/unauthorized",element:b.jsx(X_,{})}),b.jsx(Bt,{path:"/admin",element:b.jsx(ef,{allowedRoles:["admin"]}),children:b.jsxs(Bt,{path:"",element:b.jsx(Z_,{}),children:[b.jsx(Bt,{path:"",element:b.jsx(Hs,{to:"dashboard",replace:!0})}),b.jsx(Bt,{path:"dashboard",element:b.jsx(I_,{})}),b.jsx(Bt,{path:"staff",element:b.jsx($_,{})}),b.jsx(Bt,{path:"clients",element:b.jsx(tS,{})}),b.jsx(Bt,{path:"register-staff",element:b.jsx(eS,{})})]})}),b.jsx(Bt,{path:"/staff",element:b.jsx(ef,{allowedRoles:["staff"]}),children:b.jsxs(Bt,{path:"",element:b.jsx(sS,{}),children:[b.jsx(Bt,{path:"",element:b.jsx(Hs,{to:"dashboard",replace:!0})}),b.jsx(Bt,{path:"dashboard",element:b.jsx(OT,{})}),b.jsx(Bt,{path:"clients",element:b.jsx(ET,{})}),b.jsx(Bt,{path:"register-client",element:b.jsx(zT,{})}),b.jsx(Bt,{path:"documents",element:b.jsx(kT,{})}),b.jsx(Bt,{path:"upload-document",element:b.jsx(BT,{})}),b.jsx(Bt,{path:"terminate-folio",element:b.jsx(NT,{})})]})}),b.jsx(Bt,{path:"/client",element:b.jsx(ef,{allowedRoles:["client"]}),children:b.jsxs(Bt,{path:"",element:b.jsx(UT,{}),children:[b.jsx(Bt,{path:"",element:b.jsx(Hs,{to:"dashboard",replace:!0})}),b.jsx(Bt,{path:"dashboard",element:b.jsx(YT,{})}),b.jsx(Bt,{path:"statements",element:b.jsx(XT,{})})]})}),b.jsx(Bt,{path:"/",element:b.jsx(Hs,{to:"/login",replace:!0})})]})}),KT=Kv({palette:{primary:{main:"#0D1B4B"},secondary:{main:"#D4AF37"},error:{main:"#D32F2F"},background:{default:"#F7F9FC",paper:"#FFFFFF"},text:{primary:"#333333",secondary:"#555555"}},typography:{fontFamily:["-apple-system","BlinkMacSystemFont",'"Segoe UI"',"Roboto",'"Helvetica Neue"',"Arial","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"'].join(","),h1:{fontSize:"2.5rem",fontWeight:700},h2:{fontSize:"2rem",fontWeight:700},h3:{fontSize:"1.75rem",fontWeight:700},body1:{fontSize:"1rem"}},components:{MuiButton:{styleOverrides:{root:{borderRadius:8,textTransform:"none"}}},MuiAppBar:{styleOverrides:{root:{backgroundColor:"#FFFFFF",color:"#333333",boxShadow:"0 2px 4px -1px rgba(0,0,0,0.06), 0 4px 5px 0 rgba(0,0,0,0.04), 0 1px 10px 0 rgba(0,0,0,0.04)"}}}}});r1.createRoot(document.getElementById("root")).render(b.jsx(Lf.StrictMode,{children:b.jsxs(Wv,{theme:KT,children:[b.jsx(Pv,{}),b.jsx(n1,{children:b.jsx(F_,{children:b.jsx(ZT,{})})})]})}));

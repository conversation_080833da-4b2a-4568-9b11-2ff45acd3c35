import{r as i}from"./mui-Ct6pHtrK.js";/**
 * react-router v7.9.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var le="popstate";function Le(e={}){function t(r,a){let{pathname:o,search:l,hash:s}=r.location;return G("",{pathname:o,search:l,hash:s},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:O(a)}return $e(t,n,null,e)}function R(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function S(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function ke(){return Math.random().toString(36).substring(2,10)}function ie(e,t){return{usr:e.state,key:e.key,idx:t}}function G(e,t,n=null,r){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?I(t):t,state:n,key:t&&t.key||r||ke()}}function O({pathname:e="/",search:t="",hash:n=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),n&&n!=="#"&&(e+=n.charAt(0)==="#"?n:"#"+n),e}function I(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function $e(e,t,n,r={}){let{window:a=document.defaultView,v5Compat:o=!1}=r,l=a.history,s="POP",u=null,c=f();c==null&&(c=0,l.replaceState({...l.state,idx:c},""));function f(){return(l.state||{idx:null}).idx}function d(){s="POP";let p=f(),m=p==null?null:p-c;c=p,u&&u({action:s,location:w.location,delta:m})}function y(p,m){s="PUSH";let h=G(w.location,p,m);c=f()+1;let x=ie(h,c),C=w.createHref(h);try{l.pushState(x,"",C)}catch(b){if(b instanceof DOMException&&b.name==="DataCloneError")throw b;a.location.assign(C)}o&&u&&u({action:s,location:w.location,delta:1})}function g(p,m){s="REPLACE";let h=G(w.location,p,m);c=f();let x=ie(h,c),C=w.createHref(h);l.replaceState(x,"",C),o&&u&&u({action:s,location:w.location,delta:0})}function v(p){return Fe(p)}let w={get action(){return s},get location(){return e(a,l)},listen(p){if(u)throw new Error("A history only accepts one active listener");return a.addEventListener(le,d),u=p,()=>{a.removeEventListener(le,d),u=null}},createHref(p){return t(a,p)},createURL:v,encodeLocation(p){let m=v(p);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:y,replace:g,go(p){return l.go(p)}};return w}function Fe(e,t=!1){let n="http://localhost";typeof window<"u"&&(n=window.location.origin!=="null"?window.location.origin:window.location.href),R(n,"No window.location.(origin|href) available to create URL");let r=typeof e=="string"?e:O(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}function fe(e,t,n="/"){return Ne(e,t,n,!1)}function Ne(e,t,n,r){let a=typeof t=="string"?I(t):t,o=$(a.pathname||"/",n);if(o==null)return null;let l=de(e);Ie(l);let s=null;for(let u=0;s==null&&u<l.length;++u){let c=Ve(o);s=He(l[u],c,r)}return s}function de(e,t=[],n=[],r="",a=!1){let o=(l,s,u=a,c)=>{let f={relativePath:c===void 0?l.path||"":c,caseSensitive:l.caseSensitive===!0,childrenIndex:s,route:l};if(f.relativePath.startsWith("/")){if(!f.relativePath.startsWith(r)&&u)return;R(f.relativePath.startsWith(r),`Absolute route path "${f.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),f.relativePath=f.relativePath.slice(r.length)}let d=k([r,f.relativePath]),y=n.concat(f);l.children&&l.children.length>0&&(R(l.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${d}".`),de(l.children,t,y,d,u)),!(l.path==null&&!l.index)&&t.push({path:d,score:We(d,l.index),routesMeta:y})};return e.forEach((l,s)=>{if(l.path===""||!l.path?.includes("?"))o(l,s);else for(let u of he(l.path))o(l,s,!0,u)}),t}function he(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(r.length===0)return a?[o,""]:[o];let l=he(r.join("/")),s=[];return s.push(...l.map(u=>u===""?o:[o,u].join("/"))),a&&s.push(...l),s.map(u=>e.startsWith("/")&&u===""?"/":u)}function Ie(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Ue(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}var Te=/^:[\w-]+$/,De=3,Be=2,Me=1,Oe=10,Ae=-2,ue=e=>e==="*";function We(e,t){let n=e.split("/"),r=n.length;return n.some(ue)&&(r+=Ae),t&&(r+=Be),n.filter(a=>!ue(a)).reduce((a,o)=>a+(Te.test(o)?De:o===""?Me:Oe),r)}function Ue(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function He(e,t,n=!1){let{routesMeta:r}=e,a={},o="/",l=[];for(let s=0;s<r.length;++s){let u=r[s],c=s===r.length-1,f=o==="/"?t:t.slice(o.length)||"/",d=j({path:u.relativePath,caseSensitive:u.caseSensitive,end:c},f),y=u.route;if(!d&&c&&n&&!r[r.length-1].route.index&&(d=j({path:u.relativePath,caseSensitive:u.caseSensitive,end:!1},f)),!d)return null;Object.assign(a,d.params),l.push({params:a,pathname:k([o,d.pathname]),pathnameBase:Ke(k([o,d.pathnameBase])),route:y}),d.pathnameBase!=="/"&&(o=k([o,d.pathnameBase]))}return l}function j(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=_e(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],l=o.replace(/(.)\/+$/,"$1"),s=a.slice(1);return{params:r.reduce((c,{paramName:f,isOptional:d},y)=>{if(f==="*"){let v=s[y]||"";l=o.slice(0,o.length-v.length).replace(/(.)\/+$/,"$1")}const g=s[y];return d&&!g?c[f]=void 0:c[f]=(g||"").replace(/%2F/g,"/"),c},{}),pathname:o,pathnameBase:l,pattern:e}}function _e(e,t=!1,n=!0){S(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(l,s,u)=>(r.push({paramName:s,isOptional:u!=null}),u?"/?([^\\/]+)?":"/([^\\/]+)")).replace(/\/([\w-]+)\?(\/|$)/g,"(/$1)?$2");return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function Ve(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return S(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function $(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function ze(e,t="/"){let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?I(e):e;return{pathname:n?n.startsWith("/")?n:je(n,t):t,search:Ye(r),hash:qe(a)}}function je(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function Y(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Je(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Q(e){let t=Je(e);return t.map((n,r)=>r===t.length-1?n.pathname:n.pathnameBase)}function Z(e,t,n,r=!1){let a;typeof e=="string"?a=I(e):(a={...e},R(!a.pathname||!a.pathname.includes("?"),Y("?","pathname","search",a)),R(!a.pathname||!a.pathname.includes("#"),Y("#","pathname","hash",a)),R(!a.search||!a.search.includes("#"),Y("#","search","hash",a)));let o=e===""||a.pathname==="",l=o?"/":a.pathname,s;if(l==null)s=n;else{let d=t.length-1;if(!r&&l.startsWith("..")){let y=l.split("/");for(;y[0]==="..";)y.shift(),d-=1;a.pathname=y.join("/")}s=d>=0?t[d]:"/"}let u=ze(a,s),c=l&&l!=="/"&&l.endsWith("/"),f=(o||l===".")&&n.endsWith("/");return!u.pathname.endsWith("/")&&(c||f)&&(u.pathname+="/"),u}var k=e=>e.join("/").replace(/\/\/+/g,"/"),Ke=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ye=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,qe=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Ge(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var pe=["POST","PUT","PATCH","DELETE"];new Set(pe);var Xe=["GET",...pe];new Set(Xe);var T=i.createContext(null);T.displayName="DataRouter";var J=i.createContext(null);J.displayName="DataRouterState";i.createContext(!1);var me=i.createContext({isTransitioning:!1});me.displayName="ViewTransition";var Qe=i.createContext(new Map);Qe.displayName="Fetchers";var Ze=i.createContext(null);Ze.displayName="Await";var L=i.createContext(null);L.displayName="Navigation";var A=i.createContext(null);A.displayName="Location";var E=i.createContext({outlet:null,matches:[],isDataRoute:!1});E.displayName="Route";var ee=i.createContext(null);ee.displayName="RouteError";function et(e,{relative:t}={}){R(D(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=i.useContext(L),{hash:a,pathname:o,search:l}=W(e,{relative:t}),s=o;return n!=="/"&&(s=o==="/"?n:k([n,o])),r.createHref({pathname:s,search:l,hash:a})}function D(){return i.useContext(A)!=null}function F(){return R(D(),"useLocation() may be used only in the context of a <Router> component."),i.useContext(A).location}var ye="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function ge(e){i.useContext(L).static||i.useLayoutEffect(e)}function ve(){let{isDataRoute:e}=i.useContext(E);return e?mt():tt()}function tt(){R(D(),"useNavigate() may be used only in the context of a <Router> component.");let e=i.useContext(T),{basename:t,navigator:n}=i.useContext(L),{matches:r}=i.useContext(E),{pathname:a}=F(),o=JSON.stringify(Q(r)),l=i.useRef(!1);return ge(()=>{l.current=!0}),i.useCallback((u,c={})=>{if(S(l.current,ye),!l.current)return;if(typeof u=="number"){n.go(u);return}let f=Z(u,JSON.parse(o),a,c.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:k([t,f.pathname])),(c.replace?n.replace:n.push)(f,c.state,c)},[t,n,o,a,e])}var nt=i.createContext(null);function rt(e){let t=i.useContext(E).outlet;return t&&i.createElement(nt.Provider,{value:e},t)}function Xt(){let{matches:e}=i.useContext(E),t=e[e.length-1];return t?t.params:{}}function W(e,{relative:t}={}){let{matches:n}=i.useContext(E),{pathname:r}=F(),a=JSON.stringify(Q(n));return i.useMemo(()=>Z(e,JSON.parse(a),r,t==="path"),[e,a,r,t])}function at(e,t){return we(e,t)}function we(e,t,n,r,a){R(D(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o}=i.useContext(L),{matches:l}=i.useContext(E),s=l[l.length-1],u=s?s.params:{},c=s?s.pathname:"/",f=s?s.pathnameBase:"/",d=s&&s.route;{let h=d&&d.path||"";xe(c,!d||h.endsWith("*")||h.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${c}" (under <Route path="${h}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${h}"> to <Route path="${h==="/"?"*":`${h}/*`}">.`)}let y=F(),g;if(t){let h=typeof t=="string"?I(t):t;R(f==="/"||h.pathname?.startsWith(f),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${f}" but pathname "${h.pathname}" was given in the \`location\` prop.`),g=h}else g=y;let v=g.pathname||"/",w=v;if(f!=="/"){let h=f.replace(/^\//,"").split("/");w="/"+v.replace(/^\//,"").split("/").slice(h.length).join("/")}let p=fe(e,{pathname:w});S(d||p!=null,`No routes matched location "${g.pathname}${g.search}${g.hash}" `),S(p==null||p[p.length-1].route.element!==void 0||p[p.length-1].route.Component!==void 0||p[p.length-1].route.lazy!==void 0,`Matched leaf route at location "${g.pathname}${g.search}${g.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let m=st(p&&p.map(h=>Object.assign({},h,{params:Object.assign({},u,h.params),pathname:k([f,o.encodeLocation?o.encodeLocation(h.pathname).pathname:h.pathname]),pathnameBase:h.pathnameBase==="/"?f:k([f,o.encodeLocation?o.encodeLocation(h.pathnameBase).pathname:h.pathnameBase])})),l,n,r,a);return t&&m?i.createElement(A.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...g},navigationType:"POP"}},m):m}function ot(){let e=pt(),t=Ge(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:r},o={padding:"2px 4px",backgroundColor:r},l=null;return console.error("Error handled by React Router default ErrorBoundary:",e),l=i.createElement(i.Fragment,null,i.createElement("p",null,"💿 Hey developer 👋"),i.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",i.createElement("code",{style:o},"ErrorBoundary")," or"," ",i.createElement("code",{style:o},"errorElement")," prop on your route.")),i.createElement(i.Fragment,null,i.createElement("h2",null,"Unexpected Application Error!"),i.createElement("h3",{style:{fontStyle:"italic"}},t),n?i.createElement("pre",{style:a},n):null,l)}var lt=i.createElement(ot,null),it=class extends i.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){this.props.unstable_onError?this.props.unstable_onError(e,t):console.error("React Router caught the following error during render",e)}render(){return this.state.error!==void 0?i.createElement(E.Provider,{value:this.props.routeContext},i.createElement(ee.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function ut({routeContext:e,match:t,children:n}){let r=i.useContext(T);return r&&r.static&&r.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=t.route.id),i.createElement(E.Provider,{value:e},n)}function st(e,t=[],n=null,r=null,a=null){if(e==null){if(!n)return null;if(n.errors)e=n.matches;else if(t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let o=e,l=n?.errors;if(l!=null){let c=o.findIndex(f=>f.route.id&&l?.[f.route.id]!==void 0);R(c>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(l).join(",")}`),o=o.slice(0,Math.min(o.length,c+1))}let s=!1,u=-1;if(n)for(let c=0;c<o.length;c++){let f=o[c];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(u=c),f.route.id){let{loaderData:d,errors:y}=n,g=f.route.loader&&!d.hasOwnProperty(f.route.id)&&(!y||y[f.route.id]===void 0);if(f.route.lazy||g){s=!0,u>=0?o=o.slice(0,u+1):o=[o[0]];break}}}return o.reduceRight((c,f,d)=>{let y,g=!1,v=null,w=null;n&&(y=l&&f.route.id?l[f.route.id]:void 0,v=f.route.errorElement||lt,s&&(u<0&&d===0?(xe("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),g=!0,w=null):u===d&&(g=!0,w=f.route.hydrateFallbackElement||null)));let p=t.concat(o.slice(0,d+1)),m=()=>{let h;return y?h=v:g?h=w:f.route.Component?h=i.createElement(f.route.Component,null):f.route.element?h=f.route.element:h=c,i.createElement(ut,{match:f,routeContext:{outlet:c,matches:p,isDataRoute:n!=null},children:h})};return n&&(f.route.ErrorBoundary||f.route.errorElement||d===0)?i.createElement(it,{location:n.location,revalidation:n.revalidation,component:v,error:y,children:m(),routeContext:{outlet:null,matches:p,isDataRoute:!0},unstable_onError:r}):m()},null)}function te(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function ct(e){let t=i.useContext(T);return R(t,te(e)),t}function ft(e){let t=i.useContext(J);return R(t,te(e)),t}function dt(e){let t=i.useContext(E);return R(t,te(e)),t}function ne(e){let t=dt(e),n=t.matches[t.matches.length-1];return R(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}function ht(){return ne("useRouteId")}function pt(){let e=i.useContext(ee),t=ft("useRouteError"),n=ne("useRouteError");return e!==void 0?e:t.errors?.[n]}function mt(){let{router:e}=ct("useNavigate"),t=ne("useNavigate"),n=i.useRef(!1);return ge(()=>{n.current=!0}),i.useCallback(async(a,o={})=>{S(n.current,ye),n.current&&(typeof a=="number"?e.navigate(a):await e.navigate(a,{fromRouteId:t,...o}))},[e,t])}var se={};function xe(e,t,n){!t&&!se[e]&&(se[e]=!0,S(!1,n))}i.memo(yt);function yt({routes:e,future:t,state:n,unstable_onError:r}){return we(e,void 0,n,r,t)}function Qt({to:e,replace:t,state:n,relative:r}){R(D(),"<Navigate> may be used only in the context of a <Router> component.");let{static:a}=i.useContext(L);S(!a,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:o}=i.useContext(E),{pathname:l}=F(),s=ve(),u=Z(e,Q(o),l,r==="path"),c=JSON.stringify(u);return i.useEffect(()=>{s(JSON.parse(c),{replace:t,state:n,relative:r})},[s,c,r,t,n]),null}function Zt(e){return rt(e.context)}function gt(e){R(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function vt({basename:e="/",children:t=null,location:n,navigationType:r="POP",navigator:a,static:o=!1}){R(!D(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=e.replace(/^\/*/,"/"),s=i.useMemo(()=>({basename:l,navigator:a,static:o,future:{}}),[l,a,o]);typeof n=="string"&&(n=I(n));let{pathname:u="/",search:c="",hash:f="",state:d=null,key:y="default"}=n,g=i.useMemo(()=>{let v=$(u,l);return v==null?null:{location:{pathname:v,search:c,hash:f,state:d,key:y},navigationType:r}},[l,u,c,f,d,y,r]);return S(g!=null,`<Router basename="${l}"> is not able to match the URL "${u}${c}${f}" because it does not start with the basename, so the <Router> won't render anything.`),g==null?null:i.createElement(L.Provider,{value:s},i.createElement(A.Provider,{children:t,value:g}))}function en({children:e,location:t}){return at(X(e),t)}function X(e,t=[]){let n=[];return i.Children.forEach(e,(r,a)=>{if(!i.isValidElement(r))return;let o=[...t,a];if(r.type===i.Fragment){n.push.apply(n,X(r.props.children,o));return}R(r.type===gt,`[${typeof r.type=="string"?r.type:r.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),R(!r.props.index||!r.props.children,"An index route cannot have child routes.");let l={id:r.props.id||o.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,hydrateFallbackElement:r.props.hydrateFallbackElement,HydrateFallback:r.props.HydrateFallback,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.hasErrorBoundary===!0||r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(l.children=X(r.props.children,o)),n.push(l)}),n}var V="get",z="application/x-www-form-urlencoded";function K(e){return e!=null&&typeof e.tagName=="string"}function wt(e){return K(e)&&e.tagName.toLowerCase()==="button"}function xt(e){return K(e)&&e.tagName.toLowerCase()==="form"}function Rt(e){return K(e)&&e.tagName.toLowerCase()==="input"}function Et(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Ct(e,t){return e.button===0&&(!t||t==="_self")&&!Et(e)}var _=null;function bt(){if(_===null)try{new FormData(document.createElement("form"),0),_=!1}catch{_=!0}return _}var Pt=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function q(e){return e!=null&&!Pt.has(e)?(S(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${z}"`),null):e}function St(e,t){let n,r,a,o,l;if(xt(e)){let s=e.getAttribute("action");r=s?$(s,t):null,n=e.getAttribute("method")||V,a=q(e.getAttribute("enctype"))||z,o=new FormData(e)}else if(wt(e)||Rt(e)&&(e.type==="submit"||e.type==="image")){let s=e.form;if(s==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let u=e.getAttribute("formaction")||s.getAttribute("action");if(r=u?$(u,t):null,n=e.getAttribute("formmethod")||s.getAttribute("method")||V,a=q(e.getAttribute("formenctype"))||q(s.getAttribute("enctype"))||z,o=new FormData(s,e),!bt()){let{name:c,type:f,value:d}=e;if(f==="image"){let y=c?`${c}.`:"";o.append(`${y}x`,"0"),o.append(`${y}y`,"0")}else c&&o.append(c,d)}}else{if(K(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=V,r=null,a=z,l=e}return o&&a==="text/plain"&&(l=o,o=void 0),{action:r,method:n.toLowerCase(),encType:a,formData:o,body:l}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function re(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Lt(e,t,n){let r=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return r.pathname==="/"?r.pathname=`_root.${n}`:t&&$(r.pathname,t)==="/"?r.pathname=`${t.replace(/\/$/,"")}/_root.${n}`:r.pathname=`${r.pathname.replace(/\/$/,"")}.${n}`,r}async function kt(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function $t(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function Ft(e,t,n){let r=await Promise.all(e.map(async a=>{let o=t.routes[a.route.id];if(o){let l=await kt(o,n);return l.links?l.links():[]}return[]}));return Dt(r.flat(1).filter($t).filter(a=>a.rel==="stylesheet"||a.rel==="preload").map(a=>a.rel==="stylesheet"?{...a,rel:"prefetch",as:"style"}:{...a,rel:"prefetch"}))}function ce(e,t,n,r,a,o){let l=(u,c)=>n[c]?u.route.id!==n[c].route.id:!0,s=(u,c)=>n[c].pathname!==u.pathname||n[c].route.path?.endsWith("*")&&n[c].params["*"]!==u.params["*"];return o==="assets"?t.filter((u,c)=>l(u,c)||s(u,c)):o==="data"?t.filter((u,c)=>{let f=r.routes[u.route.id];if(!f||!f.hasLoader)return!1;if(l(u,c)||s(u,c))return!0;if(u.route.shouldRevalidate){let d=u.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:n[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:u.params,defaultShouldRevalidate:!0});if(typeof d=="boolean")return d}return!0}):[]}function Nt(e,t,{includeHydrateFallback:n}={}){return It(e.map(r=>{let a=t.routes[r.route.id];if(!a)return[];let o=[a.module];return a.clientActionModule&&(o=o.concat(a.clientActionModule)),a.clientLoaderModule&&(o=o.concat(a.clientLoaderModule)),n&&a.hydrateFallbackModule&&(o=o.concat(a.hydrateFallbackModule)),a.imports&&(o=o.concat(a.imports)),o}).flat(1))}function It(e){return[...new Set(e)]}function Tt(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}function Dt(e,t){let n=new Set;return new Set(t),e.reduce((r,a)=>{let o=JSON.stringify(Tt(a));return n.has(o)||(n.add(o),r.push({key:o,link:a})),r},[])}function Re(){let e=i.useContext(T);return re(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function Bt(){let e=i.useContext(J);return re(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var ae=i.createContext(void 0);ae.displayName="FrameworkContext";function Ee(){let e=i.useContext(ae);return re(e,"You must render this element inside a <HydratedRouter> element"),e}function Mt(e,t){let n=i.useContext(ae),[r,a]=i.useState(!1),[o,l]=i.useState(!1),{onFocus:s,onBlur:u,onMouseEnter:c,onMouseLeave:f,onTouchStart:d}=t,y=i.useRef(null);i.useEffect(()=>{if(e==="render"&&l(!0),e==="viewport"){let w=m=>{m.forEach(h=>{l(h.isIntersecting)})},p=new IntersectionObserver(w,{threshold:.5});return y.current&&p.observe(y.current),()=>{p.disconnect()}}},[e]),i.useEffect(()=>{if(r){let w=setTimeout(()=>{l(!0)},100);return()=>{clearTimeout(w)}}},[r]);let g=()=>{a(!0)},v=()=>{a(!1),l(!1)};return n?e!=="intent"?[o,y,{}]:[o,y,{onFocus:M(s,g),onBlur:M(u,v),onMouseEnter:M(c,g),onMouseLeave:M(f,v),onTouchStart:M(d,g)}]:[!1,y,{}]}function M(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function Ot({page:e,...t}){let{router:n}=Re(),r=i.useMemo(()=>fe(n.routes,e,n.basename),[n.routes,e,n.basename]);return r?i.createElement(Wt,{page:e,matches:r,...t}):null}function At(e){let{manifest:t,routeModules:n}=Ee(),[r,a]=i.useState([]);return i.useEffect(()=>{let o=!1;return Ft(e,t,n).then(l=>{o||a(l)}),()=>{o=!0}},[e,t,n]),r}function Wt({page:e,matches:t,...n}){let r=F(),{manifest:a,routeModules:o}=Ee(),{basename:l}=Re(),{loaderData:s,matches:u}=Bt(),c=i.useMemo(()=>ce(e,t,u,a,r,"data"),[e,t,u,a,r]),f=i.useMemo(()=>ce(e,t,u,a,r,"assets"),[e,t,u,a,r]),d=i.useMemo(()=>{if(e===r.pathname+r.search+r.hash)return[];let v=new Set,w=!1;if(t.forEach(m=>{let h=a.routes[m.route.id];!h||!h.hasLoader||(!c.some(x=>x.route.id===m.route.id)&&m.route.id in s&&o[m.route.id]?.shouldRevalidate||h.hasClientLoader?w=!0:v.add(m.route.id))}),v.size===0)return[];let p=Lt(e,l,"data");return w&&v.size>0&&p.searchParams.set("_routes",t.filter(m=>v.has(m.route.id)).map(m=>m.route.id).join(",")),[p.pathname+p.search]},[l,s,r,a,c,t,e,o]),y=i.useMemo(()=>Nt(f,a),[f,a]),g=At(f);return i.createElement(i.Fragment,null,d.map(v=>i.createElement("link",{key:v,rel:"prefetch",as:"fetch",href:v,...n})),y.map(v=>i.createElement("link",{key:v,rel:"modulepreload",href:v,...n})),g.map(({key:v,link:w})=>i.createElement("link",{key:v,nonce:n.nonce,...w})))}function Ut(...e){return t=>{e.forEach(n=>{typeof n=="function"?n(t):n!=null&&(n.current=t)})}}var Ce=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Ce&&(window.__reactRouterVersion="7.9.1")}catch{}function tn({basename:e,children:t,window:n}){let r=i.useRef();r.current==null&&(r.current=Le({window:n,v5Compat:!0}));let a=r.current,[o,l]=i.useState({action:a.action,location:a.location}),s=i.useCallback(u=>{i.startTransition(()=>l(u))},[l]);return i.useLayoutEffect(()=>a.listen(s),[a,s]),i.createElement(vt,{basename:e,children:t,location:o.location,navigationType:o.action,navigator:a})}var be=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Pe=i.forwardRef(function({onClick:t,discover:n="render",prefetch:r="none",relative:a,reloadDocument:o,replace:l,state:s,target:u,to:c,preventScrollReset:f,viewTransition:d,...y},g){let{basename:v}=i.useContext(L),w=typeof c=="string"&&be.test(c),p,m=!1;if(typeof c=="string"&&w&&(p=c,Ce))try{let P=new URL(window.location.href),N=c.startsWith("//")?new URL(P.protocol+c):new URL(c),oe=$(N.pathname,v);N.origin===P.origin&&oe!=null?c=oe+N.search+N.hash:m=!0}catch{S(!1,`<Link to="${c}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let h=et(c,{relative:a}),[x,C,b]=Mt(r,y),U=zt(c,{replace:l,state:s,target:u,preventScrollReset:f,relative:a,viewTransition:d});function B(P){t&&t(P),P.defaultPrevented||U(P)}let H=i.createElement("a",{...y,...b,href:p||h,onClick:m||o?t:B,ref:Ut(g,C),target:u,"data-discover":!w&&n==="render"?"true":void 0});return x&&!w?i.createElement(i.Fragment,null,H,i.createElement(Ot,{page:h})):H});Pe.displayName="Link";var Ht=i.forwardRef(function({"aria-current":t="page",caseSensitive:n=!1,className:r="",end:a=!1,style:o,to:l,viewTransition:s,children:u,...c},f){let d=W(l,{relative:c.relative}),y=F(),g=i.useContext(J),{navigator:v,basename:w}=i.useContext(L),p=g!=null&&qt(d)&&s===!0,m=v.encodeLocation?v.encodeLocation(d).pathname:d.pathname,h=y.pathname,x=g&&g.navigation&&g.navigation.location?g.navigation.location.pathname:null;n||(h=h.toLowerCase(),x=x?x.toLowerCase():null,m=m.toLowerCase()),x&&w&&(x=$(x,w)||x);const C=m!=="/"&&m.endsWith("/")?m.length-1:m.length;let b=h===m||!a&&h.startsWith(m)&&h.charAt(C)==="/",U=x!=null&&(x===m||!a&&x.startsWith(m)&&x.charAt(m.length)==="/"),B={isActive:b,isPending:U,isTransitioning:p},H=b?t:void 0,P;typeof r=="function"?P=r(B):P=[r,b?"active":null,U?"pending":null,p?"transitioning":null].filter(Boolean).join(" ");let N=typeof o=="function"?o(B):o;return i.createElement(Pe,{...c,"aria-current":H,className:P,ref:f,style:N,to:l,viewTransition:s},typeof u=="function"?u(B):u)});Ht.displayName="NavLink";var _t=i.forwardRef(({discover:e="render",fetcherKey:t,navigate:n,reloadDocument:r,replace:a,state:o,method:l=V,action:s,onSubmit:u,relative:c,preventScrollReset:f,viewTransition:d,...y},g)=>{let v=Kt(),w=Yt(s,{relative:c}),p=l.toLowerCase()==="get"?"get":"post",m=typeof s=="string"&&be.test(s),h=x=>{if(u&&u(x),x.defaultPrevented)return;x.preventDefault();let C=x.nativeEvent.submitter,b=C?.getAttribute("formmethod")||l;v(C||x.currentTarget,{fetcherKey:t,method:b,navigate:n,replace:a,state:o,relative:c,preventScrollReset:f,viewTransition:d})};return i.createElement("form",{ref:g,method:p,action:w,onSubmit:r?u:h,...y,"data-discover":!m&&e==="render"?"true":void 0})});_t.displayName="Form";function Vt(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Se(e){let t=i.useContext(T);return R(t,Vt(e)),t}function zt(e,{target:t,replace:n,state:r,preventScrollReset:a,relative:o,viewTransition:l}={}){let s=ve(),u=F(),c=W(e,{relative:o});return i.useCallback(f=>{if(Ct(f,t)){f.preventDefault();let d=n!==void 0?n:O(u)===O(c);s(e,{replace:d,state:r,preventScrollReset:a,relative:o,viewTransition:l})}},[u,s,c,n,r,t,e,a,o,l])}var jt=0,Jt=()=>`__${String(++jt)}__`;function Kt(){let{router:e}=Se("useSubmit"),{basename:t}=i.useContext(L),n=ht();return i.useCallback(async(r,a={})=>{let{action:o,method:l,encType:s,formData:u,body:c}=St(r,t);if(a.navigate===!1){let f=a.fetcherKey||Jt();await e.fetch(f,n,a.action||o,{preventScrollReset:a.preventScrollReset,formData:u,body:c,formMethod:a.method||l,formEncType:a.encType||s,flushSync:a.flushSync})}else await e.navigate(a.action||o,{preventScrollReset:a.preventScrollReset,formData:u,body:c,formMethod:a.method||l,formEncType:a.encType||s,replace:a.replace,state:a.state,fromRouteId:n,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,n])}function Yt(e,{relative:t}={}){let{basename:n}=i.useContext(L),r=i.useContext(E);R(r,"useFormAction must be used inside a RouteContext");let[a]=r.matches.slice(-1),o={...W(e||".",{relative:t})},l=F();if(e==null){o.search=l.search;let s=new URLSearchParams(o.search),u=s.getAll("index");if(u.some(f=>f==="")){s.delete("index"),u.filter(d=>d).forEach(d=>s.append("index",d));let f=s.toString();o.search=f?`?${f}`:""}}return(!e||e===".")&&a.route.index&&(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index"),n!=="/"&&(o.pathname=o.pathname==="/"?n:k([n,o.pathname])),O(o)}function qt(e,{relative:t}={}){let n=i.useContext(me);R(n!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=Se("useViewTransitionState"),a=W(e,{relative:t});if(!n.isTransitioning)return!1;let o=$(n.currentLocation.pathname,r)||n.currentLocation.pathname,l=$(n.nextLocation.pathname,r)||n.nextLocation.pathname;return j(a.pathname,l)!=null||j(a.pathname,o)!=null}export{tn as B,Pe as L,Qt as N,Zt as O,en as R,Xt as a,gt as b,ve as u};
